<?php
/**
 * create user：shannon
 * create time：2024/5/17 09:32
 */
namespace queue;

use common\service\resume\ResumeCacheService;
use yii\base\BaseObject;

class RedisUpdate extends BaseObject implements \yii\queue\JobInterface
{
    //简历缓存
    const TYPE_RESUME = 1;

    public $mainId;
    public $type;

    public function execute($queue)
    {
        switch ($this->type) {
            case self::TYPE_RESUME:
                $this->resume();
                break;
        }

        return true;
    }

    /**
     * 更新简历缓存文件夹
     * @return void
     */
    private function resume()
    {
        ResumeCacheService::setAll($this->mainId);
    }
}