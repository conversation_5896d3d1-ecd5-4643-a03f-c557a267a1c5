<?php
$params = array_merge(require __DIR__ . '/../../common/config/params.php',
    require __DIR__ . '/../../common/config/params-local.php', require __DIR__ . '/params.php',
    require __DIR__ . '/params-local.php');

return [
    'id'                  => 'app-miniApp',
    'basePath'            => dirname(__DIR__),
    'controllerNamespace' => 'miniApp\controllers',
    'bootstrap'           => ['log'],
    'defaultRoute'        => 'home',

    'components' => [
        'urlManager'   => [
            'enablePrettyUrl' => true,
            'showScriptName'  => false,
        ],
        'errorHandler' => [
            'class' => 'miniApp\components\CommonErrorHandler',
        ],
        'user'         => [
            'identityClass'   => 'miniApp\models\Member',
            'enableAutoLogin' => true,
        ],
        'log'          => [
            'targets' => [
                [
                    'class'   => 'yii\log\FileTarget',
                    'levels'  => [
                        'error',
                        'warning',
                        'info',
                        'trace',
                    ],
                    'logVars' => [],

                    'logFile' => '@log/miniApp/1.log',

                ],

            ],
        ],
    ],
    'params'     => $params,
];
