<?php

namespace miniApp\controllers;

use common\service\CommonService;
use common\service\resume\GetEditInfoService;
use common\service\resume\ResumeService;
use yii\base\Exception;

class ResumeAcademicPatentController extends BaseMiniAppController
{
    /**
     * 学术专利列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionIndex()
    {
        try {
            //获取服务对象
            $service = new ResumeService();
            //调用服务获取学术专利列表
            $patent_list = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_PATENT_INDEX)
                ->init()
                ->run();

            return $this->success($patent_list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 学术专利添加
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAdd()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            //获取服务对象
            $service = new ResumeService();
            //调用服务添加学术专利
            $result = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_PATENT_ADD)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success($result);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 学术专利编辑
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEdit()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            //获取服务对象
            $service = new ResumeService();
            //调用服务编辑学术专利
            $result = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_PATENT_EDIT)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success($result);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 学术专利删除
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelete()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            //获取服务对象
            $service = new ResumeService();
            //调用服务删除学术专利
            $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_PATENT_DELETE)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取编辑页面回显
     * @return \yii\console\Response|\yii\web\Response
     * @throws Exception
     */
    public function actionGetEditInfo()
    {
        $id       = \Yii::$app->request->get('id');
        $memberId = $this->memberId;
        $info     = [];
        if (!empty($id)) {
            $service = new GetEditInfoService();
            $info    = $service->setOparetion(GetEditInfoService::INFO_PATENT)
                ->init([
                    'id'       => $id,
                    'memberId' => $memberId,
                ])
                ->run();
        } else {
            return $this->fail('学术专利id不能为空！');
        }

        return $this->success($info);
    }

}