<?php

namespace miniApp\controllers;

class ChannelController extends BaseMiniAppController
{
    /**
     * 获取视频号相关配置
     */
    public function actionGetConfig()
    {
        return $this->success([
            'sphid' => 'sph58cVcjcJOJ8J',
        ]);
    }

    // 获取直播相关配置(这里主要是告诉前端,拿的视频回放的时间段)
    public function actionGetLiveConfig()
    {
        return $this->success([
            // 从三个月前的时间戳(毫秒)到现在
            'startTime' => strtotime('-3 month') * 1000,
            'endTime'   => CUR_TIMESTAMP * 1000,
        ]);
    }
}