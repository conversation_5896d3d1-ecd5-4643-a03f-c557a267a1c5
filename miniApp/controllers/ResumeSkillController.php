<?php

namespace miniApp\controllers;

use common\service\CommonService;
use common\service\resume\GetEditInfoService;
use common\service\resume\ResumeService;
use yii\base\Exception;
use Yii;

class ResumeSkillController extends BaseMiniAppController
{
    /**
     * 技能语言列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionIndex()
    {
        try {
            //获取服务对象
            $service = new ResumeService();
            //调用服务获取技能语言列表
            $skill_list = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_SKILL_INDEX)
                ->init()
                ->run();

            return $this->success($skill_list);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 技能语言添加
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAdd()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            //获取服务对象
            $service = new ResumeService();
            //调用服务添加技能语言
            $result = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_SKILL_ADD)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success($result);
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 技能语言编辑
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEdit()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            //获取服务对象
            $service = new ResumeService();
            //调用服务编辑技能语言
            $result = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_SKILL_EDIT)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success($result);
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 技能语言删除
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelete()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            //获取服务对象
            $service = new ResumeService();
            //调用服务删除技能语言
            $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_SKILL_DELETE)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取编辑页面回显
     * @return \yii\console\Response|\yii\web\Response
     * @throws Exception
     */
    public function actionGetEditInfo()
    {
        $id       = \Yii::$app->request->get('id');
        $memberId = $this->memberId;
        $info     = [];
        if (!empty($id)) {
            $service = new GetEditInfoService();
            $info    = $service->setOparetion(GetEditInfoService::INFO_SKILL)
                ->init([
                    'id'       => $id,
                    'memberId' => $memberId,
                ])
                ->run();
        } else {
            return $this->fail('技能特长id不能为空！');
        }

        return $this->success($info);
    }
}