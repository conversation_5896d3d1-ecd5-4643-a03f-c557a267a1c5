<?php

namespace miniApp\controllers;

use common\service\chat\ChatApplication;

class ChatCommonPhraseController extends BaseMiniAppController
{
    public function actionGetList()
    {
        $memberId = $this->memberId;

        try {
            $app  = ChatApplication::getInstance();
            $list = $app->getCommonPhrase($memberId);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionDelete()
    {
        $memberId = $this->memberId;
        $id       = \Yii::$app->request->post('id');

        try {
            $app = ChatApplication::getInstance();
            $app->deleteCommonPhrase($memberId, $id);

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionEdit()
    {
        $memberId = $this->memberId;
        $id       = \Yii::$app->request->post('id');
        $content  = \Yii::$app->request->post('content');

        try {
            $app = ChatApplication::getInstance();
            $app->editCommonPhrase($memberId, $content, $id);

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}