<?php
/**
 * create user：shannon
 * create time：2025/2/28 下午2:56
 */
namespace miniApp\controllers;

use common\base\models\BaseHwActivity;
use common\components\MessageException;
use common\helpers\FormatConverter;
use common\service\CommonService;
use common\service\zhaoPinHuiColumn\ActivityService;
use Yii;

class ActivityController extends BaseMiniAppController
{

    /**
     * 活动详情页
     */
    public function actionIndex()
    {
        $activityId = Yii::$app->request->get('id');
        if (!$activityId) {
            //去404
            $this->notFound();
        }

        return $this->success(ActivityService::getInstance()
            ->setPlatform(CommonService::PLATFORM_MINI)
            ->runDetail([
                'activityId' => $activityId,
                'pageSize'   => 30,
            ]));
    }

    /**
     * 参会单位列表
     */
    public function actionGetCompanyList()
    {
        try {
            $page               = Yii::$app->request->get('page', 1);
            $pageSize           = Yii::$app->request->get('pageSize', 30);
            $params             = Yii::$app->request->get();
            $params['page']     = $page;
            $params['pageSize'] = $pageSize;

            return $this->success(ActivityService::getInstance()
                ->setPlatform(CommonService::PLATFORM_MINI)
                ->runCompanyList($params));
        } catch (MessageException $e) {
            return $this->fail($e->getMessage());
        }
    }
}