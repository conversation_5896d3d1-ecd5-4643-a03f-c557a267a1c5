<?php

namespace miniApp\controllers;

use common\service\CommonService;
use common\service\resume\GetEditInfoService;
use common\service\resume\ResumeService;
use yii\base\Exception;
use Yii;

class ResumeOtherSkillController extends BaseMiniAppController
{
    /**
     * 其他技能列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionIndex()
    {
        try {
            //获取服务对象
            $service = new ResumeService();
            //调用服务获取其他技能列表
            $other_skill_list = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_OTHER_SKILL_INDEX)
                ->init()
                ->run();

            return $this->success($other_skill_list);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 其他技能添加
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAdd()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            //获取服务对象
            $service = new ResumeService();
            //调用服务添加其他技能
            $result = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_OTHER_SKILL_ADD)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success($result);
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 其他技能编辑
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEdit()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            //获取服务对象
            $service = new ResumeService();
            //调用服务编辑其他技能
            $result = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_OTHER_SKILL_EDIT)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success($result);
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 其他技能删除
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelete()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            //获取服务对象
            $service = new ResumeService();
            //调用服务删除其他技能
            $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_OTHER_SKILL_DELETE)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取编辑页面回显
     * @return \yii\console\Response|\yii\web\Response
     * @throws Exception
     */
    public function actionGetEditInfo()
    {
        $id       = \Yii::$app->request->get('id');
        $memberId = $this->memberId;
        $info     = [];
        if (!empty($id)) {
            $service = new GetEditInfoService();
            $info    = $service->setOparetion(GetEditInfoService::INFO_OTHER_SKILL)
                ->init([
                    'id'       => $id,
                    'memberId' => $memberId,
                ])
                ->run();
        } else {
            return $this->fail('其他技能id不能为空！');
        }

        return $this->success($info);
    }

}