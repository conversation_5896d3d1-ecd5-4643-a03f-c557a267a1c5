<style type="text/css">
html,
body {
    height: 100%;
    color: #333;
}
.email-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100%;
}
.email-wrapper .email-container {
    width: 680px;
    flex-shrink: 0;
    border: 1px solid #ebebeb;
    border-radius: 16px;
    margin-top: 50px;
    margin-bottom: 50px;
}
.email-wrapper .email-container a {
    text-decoration: none !important;
}
.email-wrapper .email-container .common-title {
    font-size: 16px;
    font-weight: bold;
    background-image: url("<?=$triangle ?>");
    background-repeat: no-repeat;
    background-position: left;
    line-height: 1;
    padding: 30px 0 30px 30px;
}
.email-wrapper .email-container .header {
    height: 60px;
    background-color: #ffa000;
    position: relative;
    padding-left: 50px;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    display: flex;
    align-items: center;
}
.email-wrapper .email-container .header .link {
    height: 33px;
    display: flex;
    align-items: center;
    color: #fff;
    text-decoration: none;
    font-size: 14px;
    padding-left: 137px;
    background: url("//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png")
        no-repeat left center/122px auto;
}
.email-wrapper .email-container .header .notes {
    position: absolute;
    right: 20px;
    width: 116px;
    height: 116px;
    display: block;
    bottom: -8px;
}
.email-wrapper .email-container .container {
    margin: 21px 21px 0;
    border: 1px dashed #ffa000;
    border-bottom: none;
    padding-left: 29px;
    padding-right: 29px;
    padding-bottom: 30px;
    border-radius: 8px 8px 0px 0px;
}
.email-wrapper .email-container .footer {
    color: #333 !important;
    background-color: #f8f8f8;
    padding: 15px 50px;
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px;
}
.email-wrapper .email-container .footer .qr-code {
    height: 74px;
    background: url("//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/11.png")
            no-repeat right 140px center/66px,
        url("<?= $bottom ?>") no-repeat left center/100% auto;
}
.email-wrapper .email-container .footer p {
    margin: 0;
    opacity: 0.8;
    font-size: 12px;
}
.email-wrapper .email-container .footer p + p {
    margin-top: 8px;
}
.email-wrapper .email-container .footer p .orange {
    color: #fa635c !important;
}
.email-wrapper .email-container .footer p .blue {
    color: #486cf5 !important;
}
.email-wrapper .email-container .footer p a {
    text-decoration: none !important;
}
.email-container {
    font-size: 14px;
}
.email-container a {
    text-decoration: none !important;
}
.email-container .email-body {
    line-height: 28px;
    padding-bottom: 18px;
}
.email-container .email-body .hi {
    padding-top: 28px;
    font-size: 15px;
    font-weight: bold;
}
.email-container .email-body .email-content {
    margin-bottom: 3px;
    text-indent: 2em;
}
.email-container .email-body a {
    font-weight: bold;
    text-decoration: none !important;
    color: #ffa000 !important;
}
.email-container .email-body .email-footer {
    text-align: right;
}
.email-container .job-invite-content {
    border-radius: 8px;
    padding: 20px;
    background-color: #f3f8fd;
}
.email-container .job-invite-content .job-detail {
    display: block;
    padding: 48px 20px 16px;
    position: relative;
    border-radius: 8px;
    background-color: #fff;
    color: inherit !important;
}
.email-container .job-invite-content .job-detail .title {
    font-size: 12px;
    color: #6a4b2c;
    padding: 0 14px;
    position: absolute;
    left: -7px;
    top: 17px;
    line-height: 18px;
    background: linear-gradient(90deg, #e5be9a, #f7e4d1);
    border-radius: 0px 8px 8px 0px;
}
.email-container .job-invite-content .job-detail .title::after {
    content: "";
    display: block;
    position: absolute;
    width: 7px;
    height: 4px;
    left: -1px;
    top: 100%;
    background: linear-gradient(45deg, transparent 50%, #c5946d 50%);
}
.email-container .job-invite-content .job-detail .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.email-container .job-invite-content .job-detail .top .job-name {
    font-size: 18px;
    font-weight: bold;
}
.email-container .job-invite-content .job-detail .top .job-date {
    opacity: 0.8;
}
.email-container .job-invite-content .job-detail .job-info {
    display: flex;
    padding: 13px 0;
}
.email-container .job-invite-content .job-detail .job-info .job-require {
    flex-shrink: 0;
    margin-right: 8px;
}
.email-container .job-invite-content .job-detail .job-info .job-welfare {
    display: flex;
}
.email-container
    .job-invite-content
    .job-detail
    .job-info
    .job-welfare
    .tag-primary {
    padding: 2px 8px;
    background: #fff3e0;
    border-radius: 4px;
    color: #ffa000;
    display: inline-flex;
    align-self: center;
    flex-shrink: 0;
    margin-right: 8px;
    font-size: 12px;
}
.email-container
    .job-invite-content
    .job-detail
    .job-info
    .job-welfare
    .tag-collect {
    position: relative;
}
.email-container
    .job-invite-content
    .job-detail
    .job-info
    .job-welfare
    .tag-collect
    .hover-area {
    display: none;
    position: absolute;
    z-index: 99;
    background: #fff;
    border: 1px solid #e4e7ed;
    width: 304px;
    padding: 12px 4px 0px 12px;
    top: 30px;
    transform: translateX(-50%);
    left: 40%;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.email-container
    .job-invite-content
    .job-detail
    .job-info
    .job-welfare
    .tag-collect
    .hover-area::after {
    position: absolute;
    background: #fff;
    border: 1px solid;
    border-color: rgba(0, 0, 0, 0) #e4e7ed #e4e7ed rgba(0, 0, 0, 0);
    width: 10px;
    height: 10px;
    content: " ";
    transform: translateX(-50%) rotate(-135deg);
    left: 50%;
    top: -6px;
}
.email-container
    .job-invite-content
    .job-detail
    .job-info
    .job-welfare
    .tag-collect
    .hover-area
    .tag-primary {
    float: left;
    margin-bottom: 12px;
}
.email-container
    .job-invite-content
    .job-detail
    .job-info
    .job-welfare
    .tag-collect:hover
    .hover-area {
    display: block;
}
.email-container .job-invite-content .job-detail .work-require {
    border-top: 1px solid #ebebeb;
}
.email-container .job-invite-content .job-detail .work-require .require {
    font-size: 16px;
    padding-top: 15px;
    padding-bottom: 10px;
    font-weight: bold;
}
.email-container .job-invite-content .job-detail .work-require pre {
    font-family: inherit;
    line-height: 28px;
    margin: 0;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: pre-line;
    word-break: break-all;
    opacity: 0.8;
}
.email-container .job-invite-content .job-detail .more {
    font-size: 14px;
    color: #ffa000;
    padding: 10px 0 7px;
    position: relative;
}
.email-container .job-invite-content .job-detail .more::after {
    content: "";
    display: inline-block;
    width: 10px;
    height: 10px;
    border-top: 2px solid #ffa000;
    border-right: 2px solid #ffa000;
    transform: translateX(-2px) rotate(45deg) scale(0.6);
}
.email-container .job-invite-content .accept-btn {
    display: block;
    margin: 20px auto 0;
    width: 96px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    background: #ffa000;
    border-radius: 4px;
    color: #fff;
}
.email-container .footer {
    padding: 0 !important;
    background-color: #fff !important;
}
.email-container .footer .cantact-info {
    opacity: 1 !important;
    line-height: 44px;
    text-align: center;
}
.email-container .footer .cantact-info a {
    color: inherit;
    opacity: 0.6;
}
</style>

<body>
<div class="email-wrapper">
    <div class="email-container">
        <header class="header">
            <a class="link" target="_blank" href="www.gaoxiaojob.com">高层次人才求职招聘综合服务平台</a>
            <img src="<?= $notes ?>" alt="" class="notes">
        </header>

        <?= $html ?>

        <footer class="footer">
            <div class="qr-code"></div>
            <p class="cantact-info">
                此邮件为系统邮件，<span class="orange">请勿直接回复。</span>
                <a href="mailto:<?= $replyEmail ?>">Email：<?= $replyEmail ?>；</a>
                <a href="tel:<?= $replyPhone ?>">服务热线：<?= $replyPhone ?></a>
            </p>
        </footer>
    </div>
</div>
</body>
