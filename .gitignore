# yii console commands
/yii
/yii_test
/yii_test.bat
/composer.lock

# phpstorm project files
.idea

# netbeans project files
nbproject

# zend studio for eclipse project files
.buildpath
.project
.settings

# windows thumbnail cache
Thumbs.db

# composer vendor dir
/vendor

# composer itself is not needed
composer.phar

# Mac DS_Store Files
.DS_Store

# phpunit itself is not needed
phpunit.phar
# local phpunit config
/phpunit.xml

# vagrant runtime
/.vagrant

/frontendPc/web/.well-known/
/frontendPc/views/site/index.php

/admin/web/.well-known/
/miniApp/web/.well-known/
/admin/web/assets/


/frontendPc/web/uploads
frontendPc/views/company/index.html
frontendPc/views/person/index.html
frontendPc/web/dist/company
frontendPc/web/dist/person
frontendPc/web/static/js/person/job/config.js


/admin/web/dist
/admin/views/home/<USER>

TestController.php

/.well-know

/sitemap
/uploads


/h5/web/xij1M0NtOl.txt
/h5/web/23144af048cd4a00a92b7f6410b1fc2f.txt
/frontendPc/web/xij1M0NtOl.txt
/frontendPc/web/6d8c306d232c43349cd266e70f423c66.txt
/h5/web/6d8c306d232c43349cd266e70f423c66.txt
/miniApp/web/xij1M0NtOl.txt
/h5/web/.well-known/acme-challenge/*
/api/web/.well-known/acme-challenge/*
/document/meilisearch/*

**/robots.txt

/admin/web/admin.gcjob.ideaboat.cn/*
/boShiHou/web/BingSiteAuth.xml
/boShiHou/web/baidu_verify_codeva-Ack0MfD7W6.html
/boShiHou/web/fbd07ca257e5a7c4079544a4fecf4f50.txt
/boShiHou/web/google8e537646a5769de7.html
/boShiHou/web/shenma-site-verification .txt
/boShiHou/web/sitemap/*
/boShiHou/web/sogousiteverification.txt

/h5/web/.well-known/

/zhaoPinHui/web/BingSiteAuth.xml
/zhaoPinHui/web/baidu_verify_codeva-xeBuNIHody.html
/zhaoPinHui/web/d65ea6a477253283d8df2cddb84496e4.txt
/zhaoPinHui/web/google8e537646a5769de7.html
/zhaoPinHui/web/shenma-site-verification.txt
/zhaoPinHui/web/sitemap/*
/zhaoPinHui/web/sitemap.xml
/zhaoPinHui/web/sogousiteverification.txt

/.cursor

/.cunzhi-memory
