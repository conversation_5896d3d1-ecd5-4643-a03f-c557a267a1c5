<?php
/**
 * create user：shannon
 * create time：2024/3/1 10:42
 */
namespace h5\controllers;

use common\base\models\BaseSeoHotWordConfig;
use common\libs\BaiduTimeFactor;
use common\libs\ToutiaoTimeFactor;
use Yii;

class HotWordController extends BaseH5Controller
{
    /**
     * 热词搜索
     * @return string
     */
    public function actionIndex()
    {
        //获取热词code
        $code = Yii::$app->request->get('code');
        //不存在code直接去404
        if (empty($code)) {
            $this->notFound();
        }
        //获取一下关键词信息
        $hotInfo = BaseSeoHotWordConfig::findOne(['code' => $code]);
        //不存在code直接去404
        if (empty($hotInfo)) {
            $this->notFound();
        }

        $data = BaseSeoHotWordConfig::getHotWord($hotInfo->id);
        $this->setSeo($data['seo']);
        unset($data['seo']);

        //时间因子
        BaiduTimeFactor::createHotword();
        ToutiaoTimeFactor::createHotword();

        return $this->render('index.html', $data);
    }
}