<?php

namespace h5\controllers;

use Codeception\Util\Debug;
use Yii;
use yii\base\Exception;
use yii\helpers\Url;
use common\service\payment\ResumeServer as ResumePaymentServer;
use h5\models\Member;
use h5\models\ResumeOrder;
use h5\models\ResumeEquity;
use h5\models\ResumeEquitySetting;
use h5\models\ResumeEquityPackageSetting;
use h5\models\ResumeEquityPackageRelationSetting;
use common\libs\WxPublic;
use common\libs\Cache;

class PaymentController extends BaseH5Controller
{
    /**
     * 支付
     * H5
     * 创建预支付订单
     */
    public function actionPay()
    {
        try {
            $equityPackageId = Yii::$app->request->post('equityPackageId');
            $uuid            = Yii::$app->request->post('uuid');
            if (empty($equityPackageId)) {
                throw new Exception('equityPackageId 不能为空');
            }

            // 求职者简历id
            $resumeId = $this->getResumeId();
            if (empty($resumeId)) {
                throw new Exception('resumeId 不能为空');
            }

            $service = new ResumePaymentServer();
            $res     = $service->setPlatform(ResumeOrder::PLATFORM_H5)
                ->setPayway(ResumeOrder::PAYWAY_WXPAY)
                ->setPayChannel(ResumePaymentServer::CHANNEL_H5)
                ->setReturnUrl(Url::toRoute('payment/middle-pay', true))
                ->setNotifyUrl(Url::toRoute('notify', true) . '/payway/' . ResumeOrder::PAYWAY_WXPAY)
                ->setOparetion(ResumePaymentServer::PAY_ORDER)
                ->setParams([
                    'resumeId'        => $resumeId,
                    'equityPackageId' => $equityPackageId,
                    'uuid'            => $uuid,
                ])
                ->run();

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 支付
     * JSAPI
     * 创建预支付订单
     */
    public function actionPayJsapi()
    {
        try {
            $equityPackageId = Yii::$app->request->post('equityPackageId');
            $uuid            = Yii::$app->request->post('uuid');
            if (empty($equityPackageId)) {
                throw new Exception('equityPackageId 不能为空');
            }
            $openId = Yii::$app->request->post('openId');
            if (empty($openId)) {
                throw new Exception('openId 不能为空');
            }

            // 求职者简历id
            $resumeId = $this->getResumeId();
            if (empty($resumeId)) {
                throw new Exception('resumeId 不能为空');
            }

            $service = new ResumePaymentServer();
            $res     = $service->setPlatform(ResumeOrder::PLATFORM_H5)
                ->setPayway(ResumeOrder::PAYWAY_WXPAY)
                ->setPayChannel(ResumePaymentServer::CHANNEL_JSAPI)
                ->setNotifyUrl(Url::toRoute('notify', true) . '/payway/' . ResumeOrder::PAYWAY_WXPAY)
                ->setOparetion(ResumePaymentServer::PAY_ORDER)
                ->setParams([
                    'resumeId'        => $resumeId,
                    'equityPackageId' => $equityPackageId,
                    'openId'          => $openId,
                    'uuid'            => $uuid,
                ])
                ->run();

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 支付回调
     */
    public function actionNotify()
    {
        try {
            $payway = Yii::$app->request->get('payway');

            $service = new ResumePaymentServer();
            $res     = $service->setPayway($payway)
                ->setOparetion(ResumePaymentServer::PAY_NOTIFY)
                ->run();

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 支付查询
     */
    public function actionQuery()
    {
        try {
            $orderId = Yii::$app->request->get('orderId');
            if (empty($orderId)) {
                throw new Exception('OrderId 不能为空');
            }

            // 求职者简历id
            $resumeId = $this->getResumeId();
            if (empty($resumeId)) {
                throw new Exception('resumeId 不能为空');
            }

            $service = new ResumePaymentServer();
            $res     = $service->setOparetion(ResumePaymentServer::PAY_QUERY)
                ->setPlatform(ResumeOrder::PLATFORM_H5)
                ->setParams([
                    'resumeId' => $resumeId,
                    'orderId'  => $orderId,
                ])
                ->run();

            // 支付成功并且购买的权益组合包含求职资源
            // if ($res['status'] == ResumeOrder::STATUS_PAID) {
            //     // 查询订单的权益组合id
            //     $equityPackageId = ResumeOrder::findOneVal(['id' => $orderId], 'equity_package_id');
            //     // 查询权益组合下面的权益
            //     $equityIds = ResumeEquityPackageRelationSetting::getEquityIdsByPackageId($equityPackageId,
            //         ResumeEquitySetting::STATUS_ONLINE);
            //     // 查询权益id是否包含求职资源
            //     if (in_array(ResumeEquitySetting::ID_JOB_RESOURCES, $equityIds)) {
            //         // 获取权益组合信息
            //         $equityPackageRow = ResumeEquityPackageSetting::findOne($equityPackageId);
            //         // 获取求职资源二维码链接
            //         // $info['url']      = 'https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=gQHI8DwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyaDc1a2RVZjlmR0QxRVFtVWhBY18AAgS0W69kAwSAOgkA';
            //         $info                    = ResumeEquity::getJobResources($resumeId);
            //         $info['title']           = "已为您开通{$equityPackageRow['subname']}，服务时长为{$equityPackageRow['days']}天";
            //         $info['successContent']  = "套餐内所包含的“求职资料包”权益，需您扫码关注【高校人才网服务号】，回复“求职”，领取VIP专属求职学习资料包！（开通“钻石VIP”套餐 或 “黄金VIP·180天”套餐的会员用户，需回复“会员课程”，领取“高才优课”课程学习。）";
            //         $res['jobResourcesInfo'] = $info;
            //     }
            // }

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 支付中间页面
     * H5支付
     */
    public function actionMiddlePay()
    {
        $uuid = Yii::$app->request->get('uuid');
        // 是否微信打开
        if (strpos($_SERVER['HTTP_USER_AGENT'], 'MicroMessenger') !== false) {
            return $this->redirect('/payment/middle-pay-jsapi?uuid=' . $uuid);
        }

        return $this->render('pay.html', ['uuid' => $uuid]);
    }

    /**
     * 支付中间页
     * JSAPI支付
     * 获取openid
     */
    public function actionMiddlePayJsapi()
    {
        $uuid = Yii::$app->request->get('uuid');
        try {
            // 求职者简历id
            $resumeId = $this->getResumeId();
            if (empty($resumeId)) {
                throw new Exception('系统异常');
            }

            $code = Yii::$app->request->get('code');

            $wxPublic = WxPublic::getInstance(Member::TYPE_PERSON);
            if (empty($code)) {
                // 获取用户授权
                $redirectUrl = $wxPublic->getAuthorizeUrl(Url::toRoute('payment/middle-pay-jsapi?uuid=' . $uuid, true),
                    'snsapi_base');
                header("Location:{$redirectUrl}");
                exit;
            } else {
                $user   = $wxPublic->getUserInfoByCode($code);
                $openId = $user['token_response']['openid'];
            }

            return $this->render('payJsapi.html', [
                'openId' => $openId,
                'uuid'   => $uuid,
            ]);
        } catch (\Exception $e) {
            // 有可能用户滑动返回上一步,导致一样的code置换openid报错,所以重定向重新获取
            if (strpos($e->getMessage(), '40163') !== false) {
                return $this->redirect('/payment/middle-pay-jsapi?uuid=' . $uuid);
            }

            return $this->fail($e->getMessage());
        }
    }
}