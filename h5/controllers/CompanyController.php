<?php

namespace h5\controllers;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyCollect;
use common\base\models\BaseDictionary;
use common\base\models\BaseHomePosition;
use common\base\models\BaseJob;
use common\base\models\BaseMember;
use common\base\models\BaseShowcase;
use common\base\models\BaseTrade;
use common\base\models\BaseWelfareLabel;
use common\libs\BaiduTimeFactor;
use common\libs\ToutiaoTimeFactor;
use common\service\search\CompanyListService;
use common\service\specialNeedService\CompanyInformationService;
use h5\models\Company;
use h5\models\HomePosition;
use h5\models\Job;
use Yii;
use yii\base\Exception;
use yii\helpers\Url;

class CompanyController extends BaseH5Controller
{
    public function actionIndex()
    {
        $searchData = \Yii::$app->request->get();
        if (!empty(Yii::$app->user->id)) {
            $isLogin                = BaseMember::IS_LOGIN_YES;
            $searchData['memberId'] = Yii::$app->user->id;
        } else {
            $isLogin = BaseMember::IS_LOGIN_NO;
        }
        //获取单位列表
        //        $data = Company::searchForMList($searchData);

        $data = (new CompanyListService)->run($searchData, CompanyListService::TYPE_PC_COMPANY_LIST);
        //获取单位类型
        $companyTypeList = BaseDictionary::getCompanyTypeList();
        //获取单位性质
        $companyNatureList = BaseDictionary::getCompanyNatureList();
        //获取工作地点
        $cityList    = BaseArea::getAllHierarchyCityList();
        $hotCityList = \Yii::$app->params['homePosition']['jobList']['hotCity'];

        //获取行业类别
        $industryList = BaseTrade::getTradeList();
        //单位规模
        $companyScaleList = BaseDictionary::getCompanyScaleList();
        //职位福利
        $welfareLabelList = BaseWelfareLabel::getWelfareLabelList();
        //行业类别
        $tradeList = BaseTrade::getTradeList();
        //获取广告位轮播图
        $advertList = BaseShowcase::getAdvertList();

        $companyList = $data['list'] ?: [];
        $pageSize    = $data['pageSize'] ?: 0;
        $totalNum    = $data['totalNum'] ?: 0;

        $seoConfig = Yii::$app->params['seo']['companyList'];
        $this->setSeo([
            'title'       => $seoConfig['title'],
            'keywords'    => $seoConfig['keyword'],
            'description' => $seoConfig['description'],
        ]);

        //新增广告位，推荐单位
        $recommendCompanyList = HomePosition::getShowCaseList('zhiweituijiandanweiK1_m', 'jobList');

        //判断是否进入了搜索页面
        if (array_key_exists('keyword', $searchData)) {
            $isSearchPage = true;
        } else {
            $isSearchPage = false;
        }

        if (!empty($searchData['companyType'])) {
            $companyTypeIdList = json_encode(explode('_', $searchData['companyType']));
        }
        if (!empty($searchData['companyNature'])) {
            $companyNatureIdList = json_encode(explode('_', $searchData['companyNature']));
        }
        if (!empty($searchData['areaId'])) {
            $areaIdList = json_encode(explode('_', $searchData['areaId']));
        }

        $data = [
            'cityList'            => $cityList,
            'companyNatureList'   => $companyNatureList,
            'companyTypeList'     => $companyTypeList,
            'industryList'        => $industryList,
            'companyScaleList'    => $companyScaleList,
            'welfareLabelList'    => $welfareLabelList,
            'searchData'          => $searchData,
            'companyList'         => $companyList,
            'pageSize'            => $pageSize,
            'totalNum'            => $totalNum,
            'isLogin'             => $isLogin,
            'tradeList'           => $tradeList,
            'advertList'          => $advertList,
            'companyTypeIdList'   => $companyTypeIdList,
            'companyNatureIdList' => $companyNatureIdList,
            'areaIdList'          => $areaIdList,
            'hotCityList'         => $hotCityList,
            'keyword'             => $searchData['keyword'],
        ];

        if ($isSearchPage) {
            return $this->render('search-result.html', $data);
        } else {
            //职位列表热门搜索
            $companyNumber        = 'danweiliebiao_remensousuo_m';
            $companyHotId         = BaseHomePosition::findOneVal(['number' => $companyNumber], 'id');
            $companyHotSearchList = BaseShowcase::getByPositionConfig($companyHotId, $companyNumber);
            foreach ($companyHotSearchList as $k => $value) {
                if (strlen($value['real_target_link']) < 1) {
                    $companyHotSearchList[$k]['url'] = Url::toRoute([
                        'company/index',
                        'keyword' => $value['title'],
                    ]);
                }
            }

            $recommendCompanyListOne   = array_slice($recommendCompanyList, 0, 4);
            $recommendCompanyListTwo   = array_slice($recommendCompanyList, 4, 4);
            $recommendCompanyListThree = array_slice($recommendCompanyList, 8, 4);

            return $this->render('index.html', array_merge($data, [
                'recommendCompanyListOne'   => $recommendCompanyListOne,
                'recommendCompanyListTwo'   => $recommendCompanyListTwo,
                'recommendCompanyListThree' => $recommendCompanyListThree,
                'companyHotSearchList'      => $companyHotSearchList,
            ]));
        }
    }

    /**
     * 查询单位列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetList()
    {
        $searchData = \Yii::$app->request->get();
        //获取单位列表
        $data = BaseCompany::search($searchData);
        foreach ($data['list'] as &$company) {
            if ($company['jobAmount'] > 99) {
                $company['jobAmount'] = '99+';
            }
            if ($company['announcementAmount'] > 99) {
                $company['announcementAmount'] = '99+';
            }
        }

        return $this->success($data['list']);
    }

    public function actionDetail()
    {
        $id            = Yii::$app->request->get('id');
        $isCooperation = BaseCompany::checkIsCooperation($id);
        if ($isCooperation) {
            return $this->actionCooperationDetail($id);
        }

        return $this->actionUnCooperationDetail($id);
    }

    /**
     * 单位详情页
     * @return string
     * @throws \Exception
     */
    public function actionUnCooperationDetail($id)
    {
        $memberId = Yii::$app->user->id;
        $info     = Company::getDetail($id, $memberId);
        //        $updateTime = Company::getDetailUpdateTime($id);
        if (!$info) {
            $this->notFound();
        }
        $info['userStatus'] = BaseMember::USER_STATUS_UN_LOGIN;

        if (!empty($memberId)) {
            //判断用户当前的状态（是否完成简历前三步）
            $info['userStatus'] = BaseMember::getUserResumeStatus($memberId);
        }

        $seoConfig   = Yii::$app->params['seo']['companyDetail'];
        $description = str_replace('【单位名称】', $info['companyName'], $seoConfig['description']);
        $keywords    = str_replace('【单位名称】', $info['companyName'], $seoConfig['keywords']);
        $this->setSeo([
            'title'       => str_replace('【单位名称】', $info['companyName'], $seoConfig['title']),
            'keywords'    => $keywords,
            'description' => $description,
        ]);
        Company::click($id);

        // BaiduTimeFactor::create($info['add_time'], $updateTime);
        // ToutiaoTimeFactor::create($info['add_time'], $updateTime);
        BaseCompany::getCompanyDetailTimeJs($info['add_time'], $id);

        return $this->render('detail.html', [
            'info' => $info,
        ]);
    }

    /**
     * 合作单位介绍页面
     * @param $id
     * @return string
     * @throws \Exception
     */
    public function actionCooperationDetail($id)
    {
        $memberId = Yii::$app->user->id;
        $info     = Company::getDetailIntroduceInfo($id);
        //        $updateTime = Company::getDetailUpdateTime($id);
        if (!$info) {
            $this->notFound();
        }
        $info['userStatus'] = BaseMember::USER_STATUS_UN_LOGIN;

        if (!empty($memberId)) {
            //判断用户当前的状态（是否完成简历前三步）
            $info['userStatus'] = BaseMember::getUserResumeStatus($memberId);
            $info['isCollect']  = BaseCompanyCollect::getCollectStatus($id, $memberId);
        }

        // 设置好seo的信息
        $seoConfig       = Yii::$app->params['seo']['cooperationCompanyDetail'];
        $descriptionName = str_replace('【单位名称】', $info['companyName'], $seoConfig['description']);
        $descriptionType = str_replace('【单位类型】', $info['type'], $descriptionName);
        $description     = str_replace('【单位性质】', $info['nature'], $descriptionType);
        $keywords        = str_replace('【单位名称】', $info['companyName'], $seoConfig['keywords']);
        $this->setSeo([
            'title'       => str_replace('【单位名称】', $info['companyName'], $seoConfig['title']),
            'keywords'    => $keywords,
            'description' => $description,
        ]);
        Company::click($id);

        // BaiduTimeFactor::create($info['add_time'], $updateTime);
        // ToutiaoTimeFactor::create($info['add_time'], $updateTime);
        BaseCompany::getCompanyDetailTimeJs($info['add_time'], $id);

        return $this->render('companyDetail.html', [
            'info' => $info,
        ]);
    }

    /**
     * 合作单位公告页面
     * @return string
     * @throws \Exception
     */
    public function actionDetailAnnouncementList()
    {
        $id       = Yii::$app->request->get('id');
        $memberId = Yii::$app->user->id;
        $info     = Company::getDetailAnnouncementListInfo($id);
        //        $updateTime = Company::getDetailUpdateTime($id);
        if (!$info) {
            $this->notFound();
        }

        $isCooperation = BaseCompany::checkIsCooperation($id);
        if (!$isCooperation) {
            $this->notFound();
        }

        $info['userStatus'] = BaseMember::USER_STATUS_UN_LOGIN;

        if (!empty($memberId)) {
            //判断用户当前的状态（是否完成简历前三步）
            $info['userStatus'] = BaseMember::getUserResumeStatus($memberId);
        }

        $seoConfig   = Yii::$app->params['seo']['companyDetailAnnouncement'];
        $description = str_replace('【单位名称】', $info['companyName'], $seoConfig['description']);
        $keywords    = str_replace('【单位名称】', $info['companyName'], $seoConfig['keywords']);
        $this->setSeo([
            'title'       => str_replace('【单位名称】', $info['companyName'], $seoConfig['title']),
            'keywords'    => $keywords,
            'description' => $description,
        ]);
        Company::click($id);

        // BaiduTimeFactor::create($info['add_time'], $updateTime);
        // ToutiaoTimeFactor::create($info['add_time'], $updateTime);
        BaseCompany::getCompanyDetailTimeJs($info['add_time'], $id);

        $info = (new CompanyInformationService())->handelCompanyAnnouncementList($info, $id);

        return $this->render('companyAnnouncementList.html', [
            'info' => $info,
        ]);
    }

    /**
     * 合作单位职位页面
     * @return string
     * @throws \Exception
     */
    public function actionDetailJobList()
    {
        $id       = Yii::$app->request->get('id');
        $memberId = Yii::$app->user->id;
        $info     = Company::getDetailJobListInfo($id);
        //        $updateTime = Company::getDetailUpdateTime($id);
        if (!$info) {
            $this->notFound();
        }
        $isCooperation = BaseCompany::checkIsCooperation($id);
        if (!$isCooperation) {
            $this->notFound();
        }
        $info['userStatus'] = BaseMember::USER_STATUS_UN_LOGIN;

        if (!empty($memberId)) {
            //判断用户当前的状态（是否完成简历前三步）
            $info['userStatus'] = BaseMember::getUserResumeStatus($memberId);
        }

        $seoConfig   = Yii::$app->params['seo']['companyDetailJob'];
        $description = str_replace('【单位名称】', $info['companyName'], $seoConfig['description']);
        $keywords    = str_replace('【单位名称】', $info['companyName'], $seoConfig['keywords']);
        $this->setSeo([
            'title'       => str_replace('【单位名称】', $info['companyName'], $seoConfig['title']),
            'keywords'    => $keywords,
            'description' => $description,
        ]);
        Company::click($id);

        // BaiduTimeFactor::create($info['add_time'], $updateTime);
        // ToutiaoTimeFactor::create($info['add_time'], $updateTime);
        BaseCompany::getCompanyDetailTimeJs($info['add_time'], $id);

        $info = (new CompanyInformationService())->handelCompanyJobList($info, $id);

        return $this->render('companyJobList.html', [
            'info' => $info,
        ]);
    }

    /**
     * 合作单位-引才活动
     * @return string
     * @throws \Exception
     */
    public function actionDetailActivityList()
    {
        $id         = Yii::$app->request->get('id');
        $memberId   = Yii::$app->user->id;
        $info       = BaseCompany::getDetailActivityListInfo($id);
        $updateTime = Company::getDetailUpdateTime($id);
        if (!$info) {
            $this->notFound();
        }
        if (!$info['activityList']['page']['count']) {
            $this->redirect('/company/detail/' . $id . '.html', 301);
        }
        $isCooperation = BaseCompany::checkIsCooperation($id);
        if (!$isCooperation) {
            $this->notFound();
        }
        $info['userStatus'] = BaseMember::USER_STATUS_UN_LOGIN;

        if (!empty($memberId)) {
            //判断用户当前的状态（是否完成简历前三步）
            $info['userStatus'] = BaseMember::getUserResumeStatus($memberId);
        }

        $seoConfig   = Yii::$app->params['seo']['companyDetailActivity'];
        $description = str_replace('【单位名称】', $info['companyName'], $seoConfig['description']);
        $keywords    = str_replace('【单位名称】', $info['companyName'], $seoConfig['keywords']);
        $this->setSeo([
            'title'       => str_replace('【单位名称】', $info['companyName'], $seoConfig['title']),
            'keywords'    => $keywords,
            'description' => $description,
        ]);
        Company::click($id);

        BaiduTimeFactor::create($info['add_time'], $updateTime);
        ToutiaoTimeFactor::create($info['add_time'], $updateTime);

        $info = (new CompanyInformationService())->handelCompanyJobList($info, $id);

        return $this->render('companyActivityList.html', [
            'info' => $info,
        ]);
    }

    /**
     * 获取单位下引才活动列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetActivityList()
    {
        try {
            $searchData = Yii::$app->request->get();

            return $this->success(BaseCompany::getActivityList($searchData)['list']);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取单位下的公告列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetAnnouncementList()
    {
        try {
            $searchData = Yii::$app->request->get();
            $result     = BaseAnnouncement::getCompanyDetailList($searchData);

            // 应用特殊需求配置
            if (!empty($searchData['companyId'])) {
                $data   = [
                    'list' => $result,
                ];
                $result = (new \common\service\specialNeedService\CompanyInformationService())->handelCompanyAnnouncementList($data,
                    $searchData['companyId'])['list'];
            }

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取单位下的职位列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetJobList()
    {
        try {
            $searchData = Yii::$app->request->get();
            $jobList = BaseJob::getCompanyJobList($searchData);

            // 应用特殊逻辑处理
            if (!empty($searchData['companyId'])) {
                $jobList = (new CompanyInformationService())->handelCompanyJobList($jobList, $searchData['companyId']);
            }

            return $this->success($jobList['list']);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 收藏单位
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCollect()
    {
        try {
            $ids      = Yii::$app->request->post('companyId');
            $memberId = Yii::$app->user->id;
            $msg      = '';
            //获取返回文案
            if (count(explode(',', $ids)) == 1) {
                $isCollect = BaseCompanyCollect::checkIsCollect($memberId, $ids);
                if ($isCollect) {
                    //如是收藏状态，那此处就是取消收藏
                    $msg = '取消关注成功';
                } else {
                    $msg = '关注成功';
                }
            }
            $server = new \common\service\person\CollectService();
            $data   = [
                'type' => $server::TYPE_COMPANY,
                'ids'  => $ids,
            ];
            $server->init($data)
                ->run();

            return $this->success($msg);
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 获取收藏单位列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetCollectList()
    {
        try {
            $memberId = Yii::$app->user->id;

            $searchData = Yii::$app->request->get();

            $server = new \common\service\person\CollectService();
            $data   = [
                'memberId' => $memberId,
                'page'     => $searchData['page'],
                'pageSize' => $searchData['pageSize'],
            ];
            $list   = $server->getH5CompanyList($data);

            return $this->success($list);
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 单位搜索页面
     * @return string
     */
    public function actionSearch()
    {
        $searchData = \Yii::$app->request->get();
        if (!empty(Yii::$app->user->id)) {
            $isLogin                = BaseMember::IS_LOGIN_YES;
            $searchData['memberId'] = Yii::$app->user->id;
        } else {
            $isLogin = BaseMember::IS_LOGIN_NO;
        }

        //判断是否进入了搜索页面
        if (array_key_exists('keyword', $searchData)) {
            $isSearchPage = true;
        } else {
            $isSearchPage = false;
        }

        if ($isSearchPage) {
            //获取单位列表
            $data = Company::search($searchData);

            //获取单位类型
            $companyTypeList = BaseDictionary::getCompanyTypeList();
            //获取单位性质
            $companyNatureList = BaseDictionary::getCompanyNatureList();
            //获取工作地点
            $cityList    = BaseArea::getAreaList();
            $hotCityList = \Yii::$app->params['homePosition']['jobList']['hotCity'];

            //获取行业类别
            $industryList = BaseTrade::getTradeList();
            //单位规模
            $companyScaleList = BaseDictionary::getCompanyScaleList();
            //职位福利
            $welfareLabelList = BaseWelfareLabel::getWelfareLabelList();
            //行业类别
            $tradeList = BaseTrade::getTradeList();
            //获取广告位轮播图
            $advertList = BaseShowcase::getAdvertList();

            $companyList = $data['list'] ?: [];
            $pageSize    = $data['pageSize'] ?: 0;
            $totalNum    = $data['totalNum'] ?: 0;

            $seoConfig = Yii::$app->params['seo']['companyList'];
            $this->setSeo([
                'title'       => $seoConfig['title'],
                'keywords'    => $seoConfig['keyword'],
                'description' => $seoConfig['description'],
            ]);

            if (!empty($searchData['companyType'])) {
                $companyTypeIdList = json_encode(explode('_', $searchData['companyType']));
            }
            if (!empty($searchData['companyNature'])) {
                $companyNatureIdList = json_encode(explode('_', $searchData['companyNature']));
            }
            if (!empty($searchData['areaId'])) {
                $areaIdList = json_encode(explode('_', $searchData['areaId']));
            }

            $data = [
                'cityList'            => $cityList,
                'companyNatureList'   => $companyNatureList,
                'companyTypeList'     => $companyTypeList,
                'industryList'        => $industryList,
                'companyScaleList'    => $companyScaleList,
                'welfareLabelList'    => $welfareLabelList,
                'searchData'          => $searchData,
                'companyList'         => $companyList,
                'pageSize'            => $pageSize,
                'totalNum'            => $totalNum,
                'isLogin'             => $isLogin,
                'tradeList'           => $tradeList,
                'advertList'          => $advertList,
                'companyTypeIdList'   => $companyTypeIdList,
                'companyNatureIdList' => $companyNatureIdList,
                'areaIdList'          => $areaIdList,
                'hotCityList'         => $hotCityList,
            ];

            return $this->render('search-result.html', $data);
        } else {
            //职位列表热门搜索
            $companyNumber        = 'danweiliebiao_remensousuo_m';
            $companyHotId         = BaseHomePosition::findOneVal(['number' => $companyNumber], 'id');
            $companyHotSearchList = BaseShowcase::getByPositionConfig($companyHotId, $companyNumber);
            foreach ($companyHotSearchList as $k => $value) {
                if (strlen($value['real_target_link']) < 1) {
                    $companyHotSearchList[$k]['url'] = Url::toRoute([
                        'company/index',
                        'keyword' => $value['title'],
                    ]);
                }
            }

            return $this->render('search.html', [
                'companyHotSearchList' => $companyHotSearchList,
            ]);
        }
    }

    /**
     * @return string
     * @throws \Exception
     */
    public function actionUploadCompanyHeadBannerUrl(): string
    {
        $id         = Yii::$app->request->get('id');
        $memberId   = Yii::$app->user->id;
        $info       = Company::getDetail($id, $memberId);
        $updateTime = Company::getDetailUpdateTime($id);
        if (!$info) {
            $this->notFound();
        }
        $info['userStatus'] = BaseMember::USER_STATUS_UN_LOGIN;

        if (!empty($memberId)) {
            //判断用户当前的状态（是否完成简历前三步）
            $info['userStatus'] = BaseMember::getUserResumeStatus($memberId);
        }

        $seoConfig   = Yii::$app->params['seo']['companyDetail'];
        $description = str_replace('【单位名称】', $info['companyName'], $seoConfig['description']);
        $keywords    = str_replace('【单位名称】', $info['companyName'], $seoConfig['keywords']);
        $this->setSeo([
            'title'       => str_replace('【单位名称】', $info['companyName'], $seoConfig['title']),
            'keywords'    => $keywords,
            'description' => $description,
        ]);
        Company::click($id);

        BaiduTimeFactor::create($info['add_time'], $updateTime);
        ToutiaoTimeFactor::create($info['add_time'], $updateTime);

        return $this->render('detail.html', [
            'info' => $info,
        ]);
    }
}