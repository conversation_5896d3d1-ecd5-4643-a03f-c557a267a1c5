<?php

namespace h5\controllers;

use common\base\models\BaseMember;
use common\base\models\BaseMemberLoginForm;
use common\base\models\BaseNewResumeActivityAccept;
use common\base\models\BaseNewResumeActivityShare;
use common\libs\SmsQueue;
use common\libs\WxPublic;
use common\service\newResumeActivity\CheckShareService;
use common\service\newResumeActivity\CreateService;
use Yii;

class NewResumeActivityController extends BaseH5Controller
{
    // 分享详情页
    public function actionShareDetail()
    {
        // 这个头部需要是特殊的
        $this->layout = 'activity';

        // 登录的情况下，获取我的分享数据
        $resumeId = $this->getResumeId();

        if ($resumeId) {
            $data            = BaseNewResumeActivityAccept::getShareDetail($resumeId);
            $data['isLogin'] = 1;
        } else {
            $data = [
                'acceptAmount'  => 0,
                'reachAmount'   => 0,
                'successAmount' => 0,
                'isLogin'       => 0,
            ];
        }



        return $this->render('share-detail.html', $data);
    }

    public function actionPlacardDetail()
    {
        // 这个头部需要是特殊的
        $this->layout = 'activity';

        $token = Yii::$app->request->get('token');

        // 根据token获取海报信息
        $share = BaseNewResumeActivityShare::findOne(['token' => $token]);

        if (!$share) {
            return $this->fail('海报不存在');
        }

        $data = [
            'codeUrl' => $share->qr_code_url,
        ];

        return $this->render('placard-detail.html', $data);
    }

    public function actionCreateShare()
    {
        $resumeId = $this->getResumeId();

        try {
            $data = (new CreateService())->run($resumeId);

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionCheckShare()
    {
        // 获取分享信息
        $token = Yii::$app->request->get('token');

        if (!$token) {
            return $this->fail('分享链接错误');
        }

        try {
            $model = new CheckShareService();
            $model->run($token, Yii::$app->user->id);

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    //
    public function actionAcceptDetail()
    {
        // 这个头部需要是特殊的
        $this->layout = 'activity';
        // 获取分享信息
        $token = Yii::$app->request->get('token');

        $privacyPolicyUrl    = \Yii::$app->params['privacyPolicyUrl'];
        $serviceAgreementUrl = \Yii::$app->params['serviceAgreementUrl'];

        return $this->render('accept-detail.html', [
            'token'               => $token,
            'privacyPolicyUrl'    => $privacyPolicyUrl,
            'serviceAgreementUrl' => $serviceAgreementUrl,
        ]);
    }

    public function actionSendRegisterCode()
    {
        $mobile = Yii::$app->request->post('mobile');
        $token  = Yii::$app->request->post('token');
        // 这里先做一个图形校验
        $ticket  = Yii::$app->request->post('ticket');
        $randStr = Yii::$app->request->post('randstr');
        if (!$randStr || !$ticket) {
            return $this->fail('图形验证码验证失败');
        }
        $type = BaseMember::TYPE_PERSON;

        try {
            $model = new CheckShareService();
            $model->run($token, Yii::$app->user->id, $mobile);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }

        if (!$mobile) {
            return $this->fail('手机号不能为空');
        }

        if (!$token) {
            return $this->fail('token不能为空');
        }

        $loginForm = new BaseMemberLoginForm();

        $loginForm->mobile    = $mobile;
        $loginForm->loginType = BaseMemberLoginForm::LOGIN_TYPE_NEW_RESUME_ACTIVITY;
        $loginForm->type      = $type;
        $loginForm->smsType   = SmsQueue::TYPE_LOGIN;
        $loginForm->token     = $token;

        try {
            $loginForm->sendMobileCode();

            return $this->success('验证码已发送，请注意查收');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionRegister()
    {
        $mobile = Yii::$app->request->post('mobile');
        $code   = Yii::$app->request->post('code');
        $token  = Yii::$app->request->post('token');

        $type = BaseMember::TYPE_PERSON;

        if (!$mobile) {
            return $this->fail('手机号不能为空');
        }

        if (!$code) {
            return $this->fail('验证码不能为空');
        }

        $loginForm = new BaseMemberLoginForm();

        $loginForm->mobile    = $mobile;
        $loginForm->code      = $code;
        $loginForm->token     = $token;
        $loginForm->loginType = BaseMemberLoginForm::LOGIN_TYPE_NEW_RESUME_ACTIVITY;
        $loginForm->type      = $type;

        // 开启事务
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $data = $loginForm->validateMobileCode();

            // 事务提交
            $transaction->commit();

            $data['jumpUrl'] = '/resume/index';
            // 7天后的时间.转成这种文本 X年X月X日 23:50
            $time            = time() + 7 * 24 * 3600;
            $data['message'] = '<span>' . date('Y年m月d日',
                    $time) . ' 23:50</span>前完善简历至65%，即可免费获得VIP会员，享8+求职权益。';

            return $this->success($data);
        } catch (\Exception $e) {
            // 事务回滚
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }
}