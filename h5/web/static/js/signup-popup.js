$(function () {
    var signupPopupTemplate = `
        <div class="signup-popup-template">
            <div id="signupPopup" class="weui-popup__container">
                <div class="weui-popup__overlay"></div>
                <div class="weui-popup__modal">
                    <i class="sign-popup-close"></i>
                    <div class="signup-header">登录/注册高校人才网</div>

                    <div class="sign-popup-form form-container">
                        <div class="weui-cell mobile-cell">
                            <!-- 号段 -->
                            <div class="weui-cell__hd mobile-prefix">+86</div>

                            <ul class="mobile-prefix-option">
                                <li class="item">
                                    <div>中国大陆</div>
                                    <div>+86</div>
                                </li>
                                <li class="item">
                                    <div>非中国大陆手机号</div>
                                </li>
                            </ul>

                            <div class="weui-cell__bd">
                                <input class="weui-input" pattern="[0-9]*" placeholder="请输入手机号" type="number" id="signupMobile" maxlength="11" />
                            </div>

                            <i class="weui-icon-clear" style="display: none"></i>
                        </div>

                        <div class="weui-cell code-cell">
                            <div class="weui-cell__bd">
                                <input class="weui-input" placeholder="请输入验证码" type="number" id="signupMobileCode" maxlength="4" pattern="[0-9]*" autocomplete="one-time-code" />
                            </div>

                            <i class="weui-icon-clear" style="display: none"></i>

                            <div class="weui-cell__ft">
                                <button id="signupMobileCodeBtn" class="weui-vcode-btn">获取验证码</button>
                            </div>
                        </div>

                        <div class="account-tips">
                            <label for="signup-agreement">
                                <input id="signup-agreement" type="checkbox" class="checkbox-btn" />
                                <span class="checkbox-icon"></span>
                                我已阅读并同意
                            </label>
                            <span> <a class="signup-agreement-user" href="#">用户协议</a>和<a class="signup-agreement-privacy" href="#">隐私条款</a> </span>
                        </div>

                        <div class="form-button">
                            <button class="weui-btn weui-btn_primary" id="signupPopupBtn">注册/登录</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="guideMiniAppDialog" class="weui-popup__container">
                <div class="weui-mask"></div>
                <div class="weui-dialog">
                    <div class="weui-dialog__bd">前往微信小程序可快速登录</div>
                    <div class="weui-dialog__ft">
                        <a role="button" href="javascript:;" class="weui-dialog__btn weui-dialog__btn_default cancel">取消</a>
                        <a role="button" href="javascript:;" class="weui-dialog__btn weui-dialog__btn_primary open-mini-app">立即前往</a>
                    </div>
                </div>
            </div>

            <div id="signupAgreementDialog" class="weui-popup__container">
                <div class="weui-mask"></div>
                <div class="weui-dialog">
                    <div class="weui-dialog__bd">
                        <div class="title">服务协议及隐私保护</div>
                        <div class="content">请您阅读并同意《用户协议》 《隐私条款》</div>
                    </div>
                    <div class="weui-dialog__ft">
                        <a role="button" href="javascript:;" class="weui-dialog__btn weui-dialog__btn_default cancel">不同意</a>
                        <a role="button" href="javascript:;" class="weui-dialog__btn weui-dialog__btn_primary confirm">同意</a>
                    </div>
                </div>
            </div>
        </div>`

    var $globalContainer = $('body > .container')
    $globalContainer.append(signupPopupTemplate)

    var signupCountdownTime = 60

    var signupPopup = '#signupPopup'
    var signupPopupCancel = `${signupPopup} .sign-popup-close`

    var guideMiniAppDialog = `#guideMiniAppDialog`
    var guideMiniAppDialogCancel = `#guideMiniAppDialog .cancel`

    var signupAgreementDialog = `#signupAgreementDialog`
    var signupAgreementDialogCancel = `#signupAgreementDialog .cancel`
    var signupAgreementDialogConfirm = `#signupAgreementDialog .confirm`
    var signupAgreementDialogConfirmCallback = function () {}

    var mobilePrefix = `${signupPopup} .mobile-prefix`
    var mobilePrefixItem = `${signupPopup} .mobile-prefix-option .item`

    var $body = $('body')
    var $signupPopup = $(signupPopup)
    var $signupAgreementDialog = $(signupAgreementDialog)
    var $signupAgreement = $signupPopup.find('#signup-agreement')
    var $signupAgreementUser = $signupPopup.find('.signup-agreement-user')
    var $signupAgreementPrivacy = $signupPopup.find('.signup-agreement-privacy')
    var $guideMiniAppDialog = $(guideMiniAppDialog)
    var $openMiniAppBtn = $(guideMiniAppDialog).find('.open-mini-app')
    var $mobilePrefixOption = $signupPopup.find('.mobile-prefix-option')
    var $signupMobileCodeBtn = $('#signupMobileCodeBtn')
    var $signupMobile = $('#signupMobile')
    var $signupMobileCode = $('#signupMobileCode')
    var $signupPopupBtn = $('#signupPopupBtn')

    var signupSuccessCallback = function () {}

    var captchaAppId = ''
    var signupMobileCodeHasSend = false

    function appendCaptchaJS() {
        var captchaJSKey = '/TCaptcha.js'
        var js = $('script')
        var jsArray = Object.values(js)
        var isExist = jsArray.some(function (i) {
            var src = i.src || ''
            return src.indexOf(captchaJSKey) !== -1
        })
        if (isExist) return

        var script = document.createElement('script')
        script.setAttribute('type', 'text/javascript')
        script.setAttribute('src', 'https://turing.captcha.qcloud.com/TCaptcha.js')
        var head = document.getElementsByTagName('head')[0]
        head.appendChild(script)
    }

    function setElAttr() {
        var { schemeUrl, serviceAgreementUrl, privacyPolicyUrl } = window.global || {}
        $signupAgreementUser.attr('href', serviceAgreementUrl)
        $signupAgreementPrivacy.attr('href', privacyPolicyUrl)
        $openMiniAppBtn.attr('href', schemeUrl)
    }
    setElAttr()

    if (!isMiniapp()) {
        appendCaptchaJS()
    }

    /**
     * 注册/登录弹框start
     */
    function getCaptcha() {
        httpGet('/config/get-captcha').then(function (data) {
            captchaAppId = data.captchaAppId
        })
    }

    function showSignupPopup(callback) {
        if (isMiniapp()) {
            redirectToMiniApp('/packages/person/login')
            return
        }

        getCaptcha()
        signupSuccessCallback = callback
        $signupPopup.popup()
    }

    function closeSignupPopup() {
        $.closePopup()
    }

    function handleSignupSuccess() {
        signupSuccessCallback ? signupSuccessCallback() : window.location.reload()
    }

    $body.on('click', signupPopupCancel, function () {
        closeSignupPopup()
    })

    /**
     * 注册/登录弹框end
     */

    /**
     * 海外号段登录提示start
     */
    function showGuideMiniAppDialog() {
        $guideMiniAppDialog.fadeIn(100)
    }

    function closeGuideMiniAppDialog() {
        $guideMiniAppDialog.fadeOut(100)
    }

    $body.on('click', guideMiniAppDialogCancel, function () {
        closeGuideMiniAppDialog()
    })

    /**
     * 海外号段登录提示start
     */

    /**
     * 号段start
     */
    $signupPopup.on('click', mobilePrefix, function (e) {
        $mobilePrefixOption.show()
        e.stopPropagation()
    })

    $body.on('click', mobilePrefixItem, function () {
        $mobilePrefixOption.hide()
        const index = $(this).index()
        // 非大陆号段
        if (index === 1) {
            showGuideMiniAppDialog()
        }
    })

    $body.on('click', function () {
        $mobilePrefixOption.hide()
    })

    /**
     * 号段end
     */

    /**
     * 协议start
     */

    function handleAgreementChecked() {
        $signupAgreement.prop('checked', true)
    }

    /**
     * 展示协议弹框
     * @param {{confirmCallback: function }} options
     */
    function showAgreementDialog(option = {}) {
        $signupAgreementDialog.fadeIn(100)
        signupAgreementDialogConfirmCallback =
            option.confirmCallback ||
            function () {
                handleAgreementChecked()
                handlePopupSignup()
            }
    }

    function closeAgreementDialog() {
        $signupAgreementDialog.fadeOut(100)
    }

    $body.on('click', signupAgreementDialogCancel, function () {
        closeAgreementDialog()
    })

    $body.on('click', signupAgreementDialogConfirm, function () {
        closeAgreementDialog()
        signupAgreementDialogConfirmCallback()
    })
    /**
     * 协议end
     */

    /**
     * 登录/注册start
     */  
    function sendCodeSuccess() {
        signupCountdownTime--
        $signupMobileCodeBtn.html('<span>重新发送 </span>(' + signupCountdownTime + 's)')
        $signupMobileCodeBtn.attr('disabled', true)
        $signupMobile.attr('disabled', true)
        $signupMobileCode.focus()
        signupMobileCodeHasSend = true
        countdown = setInterval(function () {
            if (signupCountdownTime == 1) {
                clearInterval(countdown)
                $signupMobile.removeAttr('disabled')
                $signupMobileCodeBtn.removeAttr('disabled')
                $signupMobileCodeBtn.text('获取验证码')
                signupCountdownTime = 60
                return
            }
            signupCountdownTime--
            $signupMobileCodeBtn.html('<span>重新发送 </span>(' + signupCountdownTime + 's)')
        }, 1000)
    }

    function handleCaptcha(callback) {
        try {
            this.captcha = new TencentCaptcha(captchaAppId, (res) => {
                const { ret, ticket, randstr } = res
                if (ret === 0) {
                    callback &&
                        callback({
                            ticket,
                            randstr
                        })
                }
            })
            this.captcha.show()
        } catch (err) {
            console.log(err)
        }
    }

    //  开始检查发送验证码
    function sendCode() {
        let mobileDom = $signupMobile
        let mobile = mobileDom.val()
        if (!mobile) {
            $.toast('手机号不能为空', 'text')
            return
        }
        if (!isMobile(mobile)) {
            $.toast('手机号码格式错误', 'text')
            return
        }

        handleCaptcha(function (data) {
            httpPost('/home/<USER>', {
                mobile,
                mobileCode: '+86',
                ...data
            })
                .then((r) => {
                    sendCodeSuccess()
                })
                .catch((r) => {})
        })
    }

    $signupMobileCodeBtn.on('click', function () {
        sendCode()
    })

    function handlePopupSignup() {
        const isAgreement = $signupAgreement.prop('checked')
        let mobile = $signupMobile.val()
        let code = $signupMobileCode.val()

        if (!mobile) {
            $.toast('手机号不能为空', 'text')
            return
        }
        if (!isMobile(mobile)) {
            $.toast('手机号码格式错误', 'text')
            return
        }
        // 发送登录请求
        if (!signupMobileCodeHasSend) {
            $.toast('请先发送验证码', 'text')
            return
        }
        if (!code) {
            $.toast('验证码不能为空', 'text')
            return
        }
        if (!isAgreement) {
            showAgreementDialog()
            return
        }

        httpPost('/home/<USER>', {
            mobile,
            mobileCode: '+86',
            code
        })
            .then((r) => {
                handleSignupSuccess()
            })
            .catch((r) => {})
    }

    $signupPopupBtn.on('click', function () {
        handlePopupSignup()
    })

    /**
     * 登录/注册end
     */

    window.signupPopup = {
        show: showSignupPopup,
        showGuideMiniAppDialog: showGuideMiniAppDialog,
        showAgreementDialog: showAgreementDialog
    }
})
