<link rel="stylesheet" href="/static/css/resumeCommon.min.css">
<link rel="stylesheet" href="/static/css/resumeCustom.min.css">

<div class="main-title">
    <div class="tltle-left">附加信息</div>
    <img src="/static/assets/resume/header.png" alt="">
</div>


<form id="form" class="form">

    <div class="weui-cell weui-cell_select subtitle-select">
        <div class="weui-cell__hd">
            <label class="weui-label is-require">主题</label>
        </div>
        <div class="weui-cell__bd">
            <input class="weui-input weui-input__select" id="themeId" name="themeId" type="text" value="<?=$info['theme']?:''?>"
                    required data-values="<?=$info['themeId']?>" readonly placeholder="请选择">
        </div>
    </div>

    <div class="weui-cell subtitle-custom">
        <div class="weui-cell__hd">
            <label class="weui-label is-require">主题</label>
        </div>
        <div class="weui-cell__bd">
            <input class="weui-input" name="themeName" type="text" value="<?=$info['themeName']?:''?>" placeholder="请输入自定义主题" />
            <i class="weui-icon-clear" style="display: none;"></i>
        </div>
    </div>

    <div class="weui-cell custom-operate">
        <div class="weui-cell__bd">
            <div class="weui-input">
                <span class="label">若无您所需主题，请点击</span>
                <a class="subtitle-custom-btn" href="javascript:;">自定义主题</a>
            </div>
        </div>
    </div>

    <div class="weui-cell">
        <div class="weui-cell__hd">
            <label class="weui-label is-require">主题描述</label>
        </div>
        <div class="weui-cell__bd textarea">
            <div class="textarea-cell">
                        <textarea class="weui-textarea" rows="3" name="content" maxlength="500"
                                    placeholder="请填写至少50字以上的附加信息描述，更容易受到用人单位的青睐哦~" autocomplete="off"><?=$info['content']?:''?></textarea>
            </div>
            <div class="weui-textarea-counter">
                <span>0</span> / <i>500</i>
            </div>
        </div>
    </div>
    <div class="resume-container">
        <?php if(!empty($info['id'])):?>
        <button class="delete-button" id="delete">删除</button>
        <?php endif?>
        <button class="save-button" id="confirm">保存</button>
    </div>
</form>

<script>
    $(function () {
        var id = "<?= $info['id']?>"
        var addApi = '/resume/add-additional'
        var editApi = '/resume/edit-additional'
        let postApi
        var $textarea = $('.weui-textarea')


        var $themeId = $('#themeId')

        var isSubtitleCustom = "<?=$info['themeName']?:'' ?>" ? true :false
        var $subtitleSelect = $('.subtitle-select')
        var $subtitleCustom = $('.subtitle-custom')
        var $subtitleCustomOperate = $('.custom-operate')
        var $subtitleCustomOperateSpan = $subtitleCustomOperate.find('span')
        var $subtitleCustomOperateLink = $subtitleCustomOperate.find('a')

        function handelSubtitleCustomOperate(isCustom) {
            var spanText = isCustom ? '点击恢复' : '若无您所需主题，请点击'
            var linkText = isCustom ? '选择主题' : '自定义主题'
            var selectValue = isCustom ? 'hide' : 'show'
            var customValue = isCustom ? 'show' : 'hide'

            $subtitleCustom.find('input').prop('required', isCustom)
            $subtitleSelect.find('input').prop('required', !isCustom)

            $subtitleCustomOperateSpan.text(spanText)
            $subtitleCustomOperateLink.text(linkText)
            $subtitleSelect[selectValue]()
            $subtitleCustom[customValue]()
        }

        $subtitleCustomOperateLink.on('click', function () {
            isSubtitleCustom = !isSubtitleCustom
            handelSubtitleCustomOperate(isSubtitleCustom)
        })

        handelSubtitleCustomOperate(isSubtitleCustom)

        var themeIdSelector = $themeId.select({
            closeText: '取消',
            items: [
                <?php foreach($themeList as $k=>$v):?>
                {title: "<?=$v?>", value: "<?=$k?>"},
                <?php endforeach?>
        ]
        })

        textAreaLimitSubmit($textarea)

        resumeOptimization(function (formEl, formData) {

            if (formData.content.length < 50) {
                toastText('请输入至少50字的附加信息描述')
                return
            }

            if (id) {
                postApi = editApi
                formData.id = id
            } else {
                postApi = addApi
            }
            formData[isSubtitleCustom ? 'themeId' : 'themeName'] = ''

            httpPost(postApi, formData).then(function () {
                backResumeEdit()
            })
        }, function () {
            var api = '/resume/del-additional'
            httpPost(api, {id}).then(function () {
                backResumeEdit()
            })
        }, '.form', '.save-button', '.delete-button')

    })
</script>
