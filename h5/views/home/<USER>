<?php
use h5\components;
?>

<link rel="stylesheet" href="/static/css/columnSecond.min.css?t=20240410">

    <?= components\NavWidget::widget() ?>

    <?=$HF_m?>

    <?= components\CurrentLocationWidget::widget(['columnId'=>$columnId]) ?>

    <?php if($type == 1):?>
    <div class="select-nav">
        <span id="areaId"><?=$searchData['areaName']?:'工作城市'?></span>
        <span id="majorId"><?=$searchData['majorName']?:'需求专业'?></span>
        <span id="educationId"><?=$searchData['educationName']?:'学历'?></span>
        <span id="jobCategoryId"><?=$searchData['jobCategoryName']?:'职位类型'?></span>
        <span id="vipFilter" class="open-popup is-vip <?php if($searchData['isEstablishment'] || $searchData['announcementHeat']):?>is-active<?php endif?>" data-target="#vipFilterPopup">高级筛选</span>
    </div>
    <?php endif?>

    <div id="vipFilterPopup" class="weui-popup__container filter-popup__container popup-bottom">
        <div class="weui-popup__overlay"></div>
        <div class="weui-popup__modal">
            <div class="toolbar">
                <div class="toolbar-inner">
                    <a href="javascript:;" class="picker-button close-popup"></a>
                    <h1 class="title">高级筛选</h1>
                </div>
            </div>
            <div class="modal-content">
                <form action="" class="filter-form">
                    <ul class="filter-list">
                        <li class="filter-item">
                            <div class="filter-label">
                                <span class="label" data-title="编制查询说明" data-content="可查询包含编制职位的优质公告" data-attr="(单选)">编制查询</span>

                                <span class="value">捕捉每一次入编机会</span>
                            </div>

                            <div class="filter-value">
                                <label>
                                    <input type="checkbox" name="isEstablishment" value="1" />
                                    <span>含编制</span>
                                </label>
                            </div>
                        </li>

                        <li class="filter-item">
                            <div class="filter-label">
                                <span class="label" data-title="公告热度说明" data-content="根据公告的浏览、关注、投递情况等综合分析得出" data-attr="(单选)">公告热度</span>

                                <span class="value">追踪公告实时热度</span>
                            </div>

                            <div class="filter-value">
                                <?php foreach($jobHeatList as $k => $v):?>
                                <label>
                                    <input type="radio" name="announcementHeat" value="<?=$k?>" />
                                    <span><?=$v?></span>
                                </label>
                                <?php endforeach?>
                            </div>
                        </li>
                    </ul>

                    <div class="filter-operate">
                        <button class="weui-btn" type="reset">重置</button>
                        <button class="weui-btn weui-btn_primary close-popup" type="submit">确定</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <ul class="result-list">
        <?php foreach($list as $item):?>
            <li class="result-item">
                <a class="result-link" href="<?=$item['url']?>">
                    <?php if ($item['isTop']): ?>
                    <h5 class="title top-mark"><?=$item['title']?></h5>
                    <?php else: ?>
                    <h5 class="title"><?=$item['title']?></h5>
                    <?php endif; ?>
                    <div class="tips">
                        <div class="tag-view">
                            <?php if($item['jobAmount']):?><span class="tag"><?=$item['jobAmount']?>个职位</span><?php endif;?>
                            <?php if($item['recruitAmount']):?><span class="tag"><?=$item['recruitAmount']?>人</span><?php endif;?>
                            <?php if($item['clickAmount']):?><span class="view"><?=$item['clickAmount']?></span><?php endif;?>
                        </div>
                        <span class="datetime"><?=$item['refreshTime']?></span>
                    </div>
                </a>
            </li>
        <?php endforeach?>


    </ul>

    <div class="weui-loadmore" style="display: none;">
        <i class="weui-loading"></i>
        <span class="weui-loadmore__tips">正在加载</span>
    </div>

    <div class="weui-loadmore weui-loadmore_line" style="display: none;">
        <span class="weui-loadmore__tips">暂无数据</span>
    </div>

    <script>
        $(function () {
            var showcaseApiData = {
                src: "<?=$listShowcaseInfo['img']?>",
                url: "<?=$listShowcaseInfo['url']?>",
                index: <?=$listShowcaseInfo['position']?:0?>
            }

            var page = 1
            var query = {
                columnId: <?=$columnId?>,
                areaId: "<?=$searchData['areaId']?:''?>",
                majorId: "<?=$searchData['majorId']?:''?>",
                educationId: "<?=$searchData['educationId']?:''?>",
                jobCategoryId: "<?=$searchData['jobCategoryId']?:''?>",
                isEstablishment: "<?=$searchData['isEstablishment']?:'0'?>",
                announcementHeat: "<?=$searchData['announcementHeat']?:0?>"
            }
            var loading = false

            function queryString() {
                return Object.keys(query).reduce((previous, current) => {
                    if (query[current]) {
                        return previous += `${current}=${query[current]}&`
                    }
                    return previous
                }, '?').replace(/[?&]$/, '')
            }

            function updQuery(data) {
                Object.keys(data).map(key => {
                    query[key] = data[key]
                })

                window.location.href = '/column/'+<?=$homeColumnId?>+".html" + queryString()
            }

            function renderList(data) {
                return data.reduce((previous, current) => {
                    const jobAmountText = current.jobAmount ? `<span class="tag">${current.jobAmount}个职位</span>` : ''
                    const recruitAmountText = current.recruitAmount ? `<span class="tag">${current.recruitAmount}人</span>` : ''
                    return previous += `
                                <li class="result-item">
                                    <a class="result-link" href="${current.url}">
                                        <h5 class="title">${current.title}</h5>
                                        <div class="tips">
                                            <div class="tag-view">
                                                ${jobAmountText}
                                                ${recruitAmountText}
                                                <span class="view">${current.clickAmount}</span>
                                            </div>
                                            <span class="datetime">${current.refreshTime}</span>
                                        </div>
                                    </a>
                                </li>
                                `
                }, '')
            }

            function fetchData(data) {
                var isNotice = <?= $type?> === 1
                // var api = '/home/<USER>'
                var api = isNotice ? '/home/<USER>' : '/home/<USER>'

                loading = true
                $loading.hide()
                $loading.eq(0).show()
                httpGet(api, { ...query, page: page + 1 }).then(data => {
                    $loading.hide()
                    if (data.length) {
                        page += 1
                        $resultList.append(renderList(data))
                    } else {
                        $loading.eq(1).show()
                    }

                    loading = data.length === 0
                })
            }

            var areaSelector = new MobileSelect({
                trigger: '#areaId',
                title: '工作城市',
                wheels: [
                    {
                        data: [
                            <?php foreach($cityList as $k=>$city):?>
                            {
                                id: '<?= $city["k"]?>',
                                    value: '<?= $city["v"]?>',
                                childs: [
                                <?php foreach($city["children"] as $children):?>
                                {
                                    id: '<?= $children["k"]?>',
                                        value: '<?= $children["v"]?>'
                                },
                                <?php endforeach;?>
                            ]
                            },
                            <?php endforeach;?>
                        ]
                    }
                ],
                callback: function (index, data) {
                    updQuery({ areaId: data.pop().id })
                }
            })

            var majorSelector = new MobileSelect({
                trigger: '#majorId',
                title: '需求专业',
                wheels: [
                    {
                        data: [
                            <?php foreach($majorList as $major):?>
                            {
                                id: '<?= $major["k"]?>',
                                    value: '<?= $major["v"]?>',
                                childs: [
                                <?php foreach($major["children"] as $children):?>

                                {
                                    id: '<?=$children["k"]?>',
                                        value: '<?=$children["v"]?>'
                                },
                                <?php endforeach;?>
                            ]
                            },
                            <?php endforeach;?>
                        ]
                    }
                ],
                callback: function (index, data) {
                    updQuery({ majorId: data.pop().id })
                }
            })

            var educationSelector = new MobileSelect({
                trigger: '#educationId',
                title: '学历',
                wheels: [
                    {
                        data: [
                            <?php foreach($educationList as $k=>$education):?>
                            {
                                id: '<?= $education["k"]?>',
                                    value: '<?= $education["v"]?>',
                            },
                            <?php endforeach;?>
                        ]
                    }
                ],
                callback: function (index, data) {
                    updQuery({ educationId: data.pop().id })
                }
            })

            var jobCategorySelector = new MobileSelect({
                trigger: '#jobCategoryId',
                title: '职位类型',
                wheels: [
                    {
                        data: [
                            <?php foreach($jobCategoryList as $jobCategory):?>
                            {
                                id: '<?= $jobCategory["k"]?>',
                                    value: '<?= $jobCategory["v"]?>',
                                childs: [
                                <?php foreach($jobCategory["children"] as $children):?>

                                {
                                    id: '<?=$children["k"]?>',
                                        value: '<?=$children["v"]?>'
                                },
                                <?php endforeach;?>
                            ]
                            },
                            <?php endforeach;?>
                        ]
                    }
                ],
                callback: function (index, data) {
                    updQuery({ jobCategoryId: data.pop().id })
                }
            })

            var $container = $('.main-container')
            var $resultList = $('.result-list')
            var $loading = $('.weui-loadmore')

            /* vip filter popup start */
            var isLogin = <?= $isLogin?:0?>;
            var isVip = <?= $isVip?:0?>;
            /* 是否点击了重置 */
            var isReset = false

            var vipPageUrl = '/vip.html'

            var tempAnnouncementHeatRadioValue = null

            var $target = $('[data-target="#vipFilterPopup"]')
            var $vipFilterPopup = $('#vipFilterPopup')
            var $filterForm = $vipFilterPopup.find('.filter-form')
            var $filterLabel = $filterForm.find('.filter-label')

            var $announcementHeat = $filterForm.find('[name="announcementHeat"]')

            function showVipModal(title, content, description) {
                $.modal({
                    text: description ? `<div class="custom-modal__content">${content}</div><div class="custom-modal__description">${description}</div>` : content,
                    title: title,
                    buttons: [
                        {
                            text: '关闭',
                            className: 'default'
                        },
                        {
                            text: '升级VIP',
                            className: 'primary',
                            onClick: function () {
                                window.location.href = vipPageUrl
                            }
                        }
                    ]
                })
            }

            function handleShowcaseCard() {
                if (showcaseApiData.src) {
                    $resultList
                        .find('li')
                        .eq(showcaseApiData.index)
                        .after($(`<li class="result-item"><a class="showcase-link is-column-second" href="${showcaseApiData.url}"><img src="${showcaseApiData.src}" /></a></li>`))
                }
            }

            handleShowcaseCard()

            $(document).on('open', '.filter-popup__container', function () {
                var isEstablishment = query.isEstablishment
                var announcementHeat = query.announcementHeat

                tempAnnouncementHeatRadioValue = announcementHeat

                $filterForm.find(`input`).prop('checked', false)

                $filterForm.find(`input[name="isEstablishment"][value="${isEstablishment}"]`).prop('checked', true)
                $filterForm.find(`input[name="announcementHeat"][value="${announcementHeat}"]`).prop('checked', true)
            })

            $filterLabel.find('.label').on('click', function () {
                var title = $(this).data('title')
                var content = $(this).data('content')
                var description = $(this).data('description')

                showVipModal(title, content, description)
            })

            $announcementHeat.on('click', function () {
                var targetValue = $(this).val()

                if (targetValue === tempAnnouncementHeatRadioValue) {
                    $(this).prop('checked', false)
                    tempAnnouncementHeatRadioValue = null
                } else {
                    tempAnnouncementHeatRadioValue = targetValue
                }
            })

            $filterForm.find('input').on('click', function () {
                if (!isLogin) {
                    window.location.href = `/code-login?redirect=${encodeURIComponent(window.location.href)}`
                    return
                }

                if (!isVip) {
                    $(this).prop('checked', false)
                    showVipModal('提示', '开通VIP服务即可使用高级筛选功能，精准捕捉目标职位&公告', '高级筛选功能包括：职位&公告 编制查询、热度情况筛选等')
                    return
                }
            })

            $filterForm
                .on('reset', function () {
                    isReset = true
                    tempAnnouncementHeatRadioValue = null
                })
                .on('submit', function (e) {
                    e.preventDefault()

                    var formData = $(this).serializeArray()
                    var filterFormData = {
                        isEstablishment: '',
                        announcementHeat: ''
                    }

                    if (formData.length) {
                        formData.forEach(function (item) {
                            var key = item.name
                            var value = item.value

                            filterFormData[key] = value
                        })
                    }

                    updQuery(filterFormData)
                })
            /* vip filter popup end */

            $container.infinite().on('infinite', function () {
                if (loading) return

                fetchData()
            })
        });
    </script>
