<link rel="stylesheet" href="/static/css/search.min.css?v=20240925">

<div class="search-main">
    <div class="search-record" style="display: none;">
        <h4 class="search-label">历史搜索<span class="remove-btn"></span></h4>

        <div class="search-value"></div>
    </div>

    <div class="search-hot">
        <h4 class="search-label is-hot">热门搜索</h4>

        <div class="search-value">
            <?php foreach($jobHotSearchList as $k => $item):?>
            <a class="showcase-browse" href="<?=$item['url']?>" data-showcase-number="<?php echo $item['number'] ?>" data-showcase-id="<?php echo $item['id'] ?>">
                <span class="idx"><?=$k+1?></span>
                <span class="txt <?php if($k <6):?>is-hot<?php endif?>"><?=$item['title']?></span>
            </a>
            <?php endforeach?>
        </div>
    </div>
</div>

<script>
    $(function () {
        var $searchForm = $('#searchForm')
        var $searchType = $searchForm.find('input[name="type"]')
        var $searchValue = $searchForm.find('input[name="keyword"]')

        var $removeButton = $('.remove-btn')
        var $searchRecord = $('.search-record')
        var $searchRecordList = $searchRecord.find('.search-value')

        function handleRecord(data) {
            var record = null

            if (data) {
                setRecordStorage('jobs', data)
            }

            record = getRecordStorage('jobs')

            if (record.length) {
                var result = record.reduce((previous, current) => {
                    return previous += `<span>${current}</span>`
                }, '')

                $searchRecordList.html(result)
                $searchRecord.show()
            } else {
                $searchRecord.hide()
                $searchRecordList.empty()
            }
        }

        handleRecord()
        // $searchValue.focus()

        $removeButton.click(function () {
            handleRecord([])
        })

        $searchRecordList.on('click', 'span', function () {
            $searchValue.val($(this).text())
            $searchForm.submit()
        })
    })
</script>
