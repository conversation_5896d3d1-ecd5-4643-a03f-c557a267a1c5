<?php

namespace h5\components;

use common\base\models\BaseMemberMessage;
use common\helpers\UrlHelper;
use yii\base\Widget;

class FooterWidget extends Widget
{
    private $type;

    public function init()
    {
        parent::init();
        //某些页面做了静态处理，这里只选择方法名来判断
        $this->type = UrlHelper::getFooterActiveType();
    }

    public function run()
    {
        $memberId     = \Yii::$app->user->id;
        $messageCount = BaseMemberMessage::getUnreadCount($memberId);
        if($messageCount > 99){
            $messageCount = '99+';
        }
        if ($messageCount > 0) {
            $isSpecial = true;
        } else {
            $isSpecial = false;
        }

        return $this->render('footer.html', [
            'type'         => $this->type,
            'isSpecial'    => $isSpecial,
            'messageCount' => $messageCount,
        ]);
    }

}