<div class="alert-container" >
    <!-- 登录提醒 -->
    <?php if(!$isLogin):?>
    <div class="tips-bar" id="login-tar" style="display: none">
        <p class="tips-text">立即登录，解锁海量匹配职位</p>
        <a href="javascript:;" class="tips-link">5s 登录</a>
    </div>
    <?php endif?>

    <?php if($isLogin):?>
        <?php if(!$resumeCompleteStatus):?>
        <!-- 简历完善提醒-->
            <div class="tips-bar">
                <p class="tips-text">立即完善求职意向，获精准推荐</p>
                <a href="/resume/index" class="tips-link">去完善</a>
            </div>
        <?php endif?>

        <?php if($lowerComplete):?>
            <div class="tips-bar">
                <p class="tips-text">简历完整度较低！无法被单位查看</p>
                <a href="/resume/edit" class="tips-link">去完善</a>
            </div>
        <?php endif?>

    <?php endif?>

    <script>
        $(function () {
            var $loginTar = $('#login-tar')
            var $container = $('.main-container')
            var beforeScrollTopH = 0

            var $tipsBtn = $loginTar.find('.tips-link')
            $tipsBtn.on('click', function (event) {
                var href = $(this).attr('href')

                if (href.indexOf(';') > -1) {
                    event.preventDefault()
                    window.signupPopup.show()
                }
            })

            $container.on('scroll', function () {
                let aferScrollTopH = $container.scrollTop()

                if (aferScrollTopH <= 0) {
                    $loginTar.show()
                } else {
                    let diff = aferScrollTopH - beforeScrollTopH

                    if (diff === 0) {
                        $loginTar.show()
                    }

                    let scrollType = diff > 0 ? 'down' : 'up'

                    beforeScrollTopH = aferScrollTopH

                    if (scrollType === 'up') {
                        $loginTar.show()
                    } else {
                        $loginTar.hide()
                    }
                }
            })
        })
    </script>
</div>
