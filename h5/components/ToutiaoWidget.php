<?php
namespace h5\components;

use frontendPc\components\BaseWidget;

class ToutiaoWidget extends BaseWidget
{

    public $isShow = false;

    /**
     * 包括：栏目页、公告详情页、职位详情页、单位详情页、文章资讯页、seo职位中心页面。
     * ①栏目页：https://m.gaoxiaojob.com/column/*.html
     * ②公告详情页：https://m.gaoxiaojob.com/announcement/detail/*.html
     * ③职位详情页：https://m.gaoxiaojob.com/job/detail/*.html
     * ④单位详情页：https://m.gaoxiaojob.com/company/detail/*.html
     * ⑤文章资讯页：https://m.gaoxiaojob.com/news/detail/*.html
     * ⑥seo职位中心页面：https://m.gaoxiaojob.com/rczhaopin*
     */
    const ROUTER_LIST = [
        'home/column',
        'announcement/detail',
        'job/detail',
        'company/detail',
        'news/detail',
        'engine/index',
    ];

    public function init()
    {
        // 看一下是那个路由
        $route = \Yii::$app->controller->route;

        if (in_array($route, self::ROUTER_LIST)) {
            $this->isShow = true;
        }
    }

    public function run()
    {
        if (!$this->isShow) {
            return false;
        }

        return $this->render('toutiao.html', []);
    }

}