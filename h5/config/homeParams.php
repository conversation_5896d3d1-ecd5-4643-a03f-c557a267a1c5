<?php
return [
    'homePosition' => [
        'index'              => [
            //首页配置
            'showcase'      => [
                'HF_m' => [
                    'count'      => 10,
                    'needPaging' => false,
                ],
                'B1_m' => [
                    'count'      => 2,
                    'needPaging' => false,
                ],
                'B2_m' => [
                    'count'      => 2,
                    'needPaging' => false,
                ],
                'A3_m' => [
                    'count'      => 28,
                    'needPaging' => false,
                ],
                'C1_m' => [
                    'count'      => 10,
                    'needPaging' => false,
                ],
                'C4_m' => [
                    'count'      => 9,
                    'needPaging' => true,
                ],
                'C5_m' => [
                    'count'      => 9,
                    'needPaging' => true,
                ],

            ],
            'columnContent' => [
                'strategy'           => [
                    'id'       => 278,
                    'name'     => '求职攻略',
                    'count'    => '9',
                    'pageSize' => '9',
                    'type'     => 'news',
                ],
                'depthInvestigation' => [
                    'id'   => 279,
                    'name' => '深度观察',
                ],
                //以下两个被替换掉了
                //                'share'              => [
                //                    'id'       => 2,
                //                    'name'     => '职场分享',
                //                    'count'    => '9',
                //                    'pageSize' => '9',
                //                    'type'     => 'news',
                //                ],
                //                'series'             => [
                //                    'pageSize' => '9',
                //                    'count'    => '9',
                //                    'name'     => '深度观察',
                //                ],
            ],
        ],
        'column'             => [
            //一级栏目页配置
            //广告配置
            'showcase'              => [
                'HF_m'          => [
                    'count' => 1,
                ],
                'UB1_m'         => [
                    'count' => 5,
                ],
                'yijilanmuye_m' => [//暂时没有限制
                ],
            ],
            //栏目内容配置
            'columnContent'         => [
                'lineHead' => [
                    'id'    => 1,
                    'name'  => '栏目头条',
                    'count' => '3',
                    'type'  => 'announcement',
                ],
                'share'    => [
                    'id'    => 2,
                    'name'  => '职场分享',
                    'count' => '27',
                    'type'  => 'news',
                ],
            ],
            //推荐公告配置
            'recommendAnnouncement' => [
                'count' => '32',
            ],
            'columnHead'            => [
                'count' => 3,
            ],
        ],
        //页面的配置
        //职位页面(这里结构较为复杂，要满足组件以及控制器广告位获取，不要轻易修改结构)
        'jobList'            => [
            'banner'   => [
                'name'  => 'zhiweiB1_m',
                'count' => 10,
            ],
            'showcase' => [
                'zhiweituijiandanweiK1_m' => ['count' => 12],
            ],
            'hotCity'  => [
                [
                    'name' => '北京',
                    'id'   => 2,
                ],
                [
                    'name' => '上海',
                    'id'   => 802,
                ],
                [
                    'name' => '天津',
                    'id'   => 20,
                ],
                [
                    'name' => '重庆',
                    'id'   => 2324,
                ],
                [
                    'name' => '广州',
                    'id'   => 1965,
                ],
                [
                    'name' => '深圳',
                    'id'   => 1988,
                ],
                [
                    'name' => '武汉',
                    'id'   => 1710,
                ],
                [
                    'name' => '南京',
                    'id'   => 821,
                ],
                [
                    'name' => '西安',
                    'id'   => 2899,
                ],
                [
                    'name' => '成都',
                    'id'   => 2368,
                ],
                [
                    'name' => '杭州',
                    'id'   => 934,
                ],
            ],
        ],
        'jobDetail'          => [
            'showcase' => [
                'zhiweiHF_m' => ['count' => 5],
            ],
        ],
        'announcementDetail' => [
            'showcase' => [
                'announcementHF_m' => ['count' => 5],
            ],
        ],
        'companyList'        => [
            'banner' => [
                'name'  => 'danweiB1_m',
                'count' => 10,
            ],
        ],

    ],
];