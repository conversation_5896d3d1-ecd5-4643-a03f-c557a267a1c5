<?php

namespace h5\models;

use admin\models\Area;
use admin\models\ArticleColumn;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyGroupScoreSystem;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseJob;
use common\helpers\ArrayHelper;
use common\helpers\TimeHelper;
use common\models\CategoryJob;
use yii;
use yii\helpers\Url;

class HomeColumn extends BaseHomeColumn
{

    public static function getSecondColumnList($parentId)
    {
        $secondColumnList = HomeColumn::find()
            ->andWhere([
                'parent_id' => $parentId,
                'status'    => 1,
                'is_hide'   => 2,
            ])
            ->select([
                'id',
                'name',
            ])
            ->asArray()
            ->all();
        foreach ($secondColumnList as &$secondColumn) {
            $secondColumn['url'] = Url::toRoute([
                '/home/<USER>',
                'id' => $secondColumn['id'],
            ]);
        }

        return $secondColumnList;
    }

    /**
     * 获取热门地区
     * @return mixed
     */
    public static function getHotAreaColumn()
    {
        $list = \Yii::$app->params['hotAreaColumn'];
        foreach ($list as &$item) {
            $item['url'] = Url::toRoute([
                '/home/<USER>',
                'id' => $item['id'],
            ]);
        }

        return $list;
    }

    /**
     * 获取所有地区栏目（省份）
     * @return mixed
     */
    public static function getAllProvinceColumn()
    {
        $list = \Yii::$app->params['provinceColumn'];
        foreach ($list as &$item) {
            $item['url'] = Url::toRoute([
                '/home/<USER>',
                'id' => $item['id'],
            ]);
        }

        return $list;
    }

    //获取所有城市栏目
    public static function getAllCityColumn()
    {
        $allList  = [];
        $cityList = \Yii::$app->params['cityColumn'];
        foreach ($cityList as $key => $list) {
            $cityInfo          = [];
            $cityInfo['label'] = $key;
            foreach ($list as &$item) {
                $item['url'] = Url::toRoute([
                    '/home/<USER>',
                    'id' => $item['id'],
                ]);
            }
            $cityInfo['list'] = $list;
            array_push($allList, $cityInfo);
        }

        return $allList;
    }

    /**
     * @param $columnId
     * 获取推荐公告,其实就是熟悉是栏目头条的推荐公告（36条）：调用本栏目下，近7天（包含当前日）发布的  公告属性为“滚动”、“推荐”、“焦点”、“置顶”、“首头”等的公告信息（优先展示”置顶“属性公告）；按发布时间倒序排列；
     */
    public static function getHeadList($columnId)
    {
        $pageSize = Yii::$app->params['homePosition']['column']['columnHead']['count'];
        //判断是否为资讯栏目
        $homeColumn = self::findOne(['id' => $columnId]);
        if ($homeColumn->template_type == BaseHomeColumn::TEMPLATE_TYPE_NEWS) {
            //资讯栏目，需要把子类也加进去
            $columnIdList = self::find()
                ->where(['parent_id' => $columnId])
                ->select('id')
                ->column();
            if ($columnIdList) {
                array_push($columnIdList, $columnId);
                $columnId = $columnIdList;
            }
        }

        $list = Article::find()
            ->alias('a')
            ->innerJoin(['b' => ArticleAttribute::tableName()], 'b.article_id=a.id')
            ->innerJoin(['d' => ArticleColumn::tableName()], 'd.article_id=a.id')
            ->where([
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.status'    => Article::STATUS_ONLINE,
                'a.is_delete' => Article::IS_DELETE_NO,
                'd.column_id' => $columnId,
            ])
            ->andWhere([
                'b.type' => ArticleAttribute::ATTRIBUTE_COLUMN_HEAD,
            ])
            ->select('a.id,a.type,a.title,refresh_time')
            ->orderBy('b.sort_time desc')
            ->limit($pageSize)
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['url'] = Article::getDetailUrl($item['id'], $item['type']);
        }

        return $list;
    }

    /**
     * 时间倒序前提下，优先调用公告属性为“首页置顶”的信息，其次展示公告属性为“滚动””栏目置顶““首页头条”“栏目头条”的公告信息。
     * @param $columnId
     * @return array|yii\db\ActiveRecord[]
     */
    public static function getAnnouncementRecommendList($columnId)
    {
        $pageSize = 36;

        //先获取属性表id，且按要求的类型去排序
        $articleAttributeList = BaseArticleAttribute::find()
            ->alias('a')
            ->select([
                'a.id',
                'a.article_id',
                '(CASE a.type WHEN "' . BaseArticleAttribute::ATTRIBUTE_HOME_TOP . '" THEN 1 ElSE 0 END) as sort',
            ])
            ->where([
                'type' => [
                    BaseArticleAttribute::ATTRIBUTE_HOME_TOP,
                    BaseArticleAttribute::ATTRIBUTE_ROLLING,
                    BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP,
                    BaseArticleAttribute::ATTRIBUTE_COLUMN_HEAD,
                ],
            ])
            ->orderBy('sort desc')
            ->asArray()
            ->all();

        $ids        = [];
        $articleIds = [];
        foreach ($articleAttributeList as $item) {
            if (!in_array($item['article_id'], $articleIds)) {
                $articleIds[] = $item['article_id'];
                $ids[]        = $item['id'];
            }
        }

        $last7Days = TimeHelper::dayToBeginTime(date('Y-m-d', strtotime('-7 days')));
        $list      = Article::find()
            ->alias('a')
            ->select([
                'c.id',
                'a.type',
                'c.title',
                'refresh_time',
                '(CASE b.type WHEN "3" THEN 1 ElSE 0 END) as sort',
                'b.type as btype',
                'DATE_FORMAT(a.refresh_time,"%Y-%m-%d") as date',
            ])
            ->innerJoin(['b' => ArticleAttribute::tableName()], 'b.article_id=a.id')
            ->leftJoin(['c' => Announcement::tableName()], 'c.article_id=a.id')
            ->leftJoin(['d' => ArticleColumn::tableName()], 'd.article_id=a.id')
            ->where([
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.status'    => [
                    Article::STATUS_ONLINE,
                    Article::STATUS_OFFLINE,
                ],
                'a.is_delete' => Article::IS_DELETE_NO,
                'd.column_id' => $columnId,
                'a.type'      => Article::TYPE_ANNOUNCEMENT,
                'b.id'        => $ids,
            ])
            ->orderBy('date desc,sort desc,a.refresh_time desc')
            ->limit($pageSize)
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['url'] = Announcement::getDetailUrl($item['id']);
        }

        return $list;
    }

    /**
     * 时间倒序前提下，优先调用公告属性为“首页置顶”的信息，其次展示公告属性为“滚动””栏目置顶““首页头条”“栏目头条”“推荐”“焦点”的公告信息。
     * @param $columnId
     * @return array|yii\db\ActiveRecord[]
     */
    public static function getAreaAnnouncementRecommendList($columnId)
    {
        $pageSize = 36;

        //先获取属性表id，且按要求的类型去排序
        $articleAttributeList = BaseArticleAttribute::find()
            ->alias('a')
            ->select([
                'a.id',
                'a.article_id',
                '(CASE a.type WHEN "' . BaseArticleAttribute::ATTRIBUTE_HOME_TOP . '" THEN 1 ElSE 0 END) as sort',
            ])
            ->where([
                'type' => [
                    BaseArticleAttribute::ATTRIBUTE_HOME_TOP,
                    BaseArticleAttribute::ATTRIBUTE_ROLLING,
                    BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP,
                    BaseArticleAttribute::ATTRIBUTE_COLUMN_HEAD,
                    BaseArticleAttribute::ATTRIBUTE_RECOMMEND,
                    BaseArticleAttribute::ATTRIBUTE_FOCUS,
                ],
            ])
            ->orderBy('sort desc')
            ->asArray()
            ->all();

        $ids        = [];
        $articleIds = [];
        foreach ($articleAttributeList as $item) {
            if (!in_array($item['article_id'], $articleIds)) {
                $articleIds[] = $item['article_id'];
                $ids[]        = $item['id'];
            }
        }

        $last7Days = TimeHelper::dayToBeginTime(date('Y-m-d', strtotime('-7 days')));
        $list      = Article::find()
            ->alias('a')
            ->select([
                'c.id',
                'a.type',
                'c.title',
                'refresh_time',
                '(CASE b.type WHEN "3" THEN 1 ElSE 0 END) as sort',
                'b.type as btype',
                'DATE_FORMAT(a.refresh_time,"%Y-%m-%d") as date',
            ])
            ->innerJoin(['b' => ArticleAttribute::tableName()], 'b.article_id=a.id')
            ->leftJoin(['c' => Announcement::tableName()], 'c.article_id=a.id')
            ->leftJoin(['d' => ArticleColumn::tableName()], 'd.article_id=a.id')
            ->where([
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.status'    => [
                    Article::STATUS_ONLINE,
                    Article::STATUS_OFFLINE,
                ],
                'a.is_delete' => Article::IS_DELETE_NO,
                'd.column_id' => $columnId,
                'a.type'      => Article::TYPE_ANNOUNCEMENT,
                'b.id'        => $ids,
            ])
            ->orderBy('date desc,sort desc,a.refresh_time desc')
            ->limit($pageSize)
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['url'] = Announcement::getDetailUrl($item['id']);
        }

        return $list;
    }

    public static function getNewsRecommendList($columnId)
    {
        if ($columnId == BaseHomeColumn::NEWS_TOP_ID) {
            // 这里其实是所有资讯都要!
            $columnId = HomeColumn::find()
                ->select('id')
                ->where([
                    'status'    => HomeColumn::STATUS_ACTIVE,
                    'parent_id' => $columnId,
                ])
                ->column();
        }
        $pageSize = 36;

        $list = Article::find()
            ->alias('a')
            ->innerJoin(['c' => News::tableName()], 'c.article_id=a.id')
            ->innerJoin(['d' => ArticleColumn::tableName()], 'd.article_id=a.id')
            ->innerJoin(['e' => ArticleAttribute::tableName()], 'e.article_id=a.id')
            ->where([
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.status'    => Article::STATUS_ONLINE,
                'a.is_delete' => Article::IS_DELETE_NO,
                'd.column_id' => $columnId,
                'e.type'      => BaseArticleAttribute::ATTRIBUTE_RECOMMEND,
            ])
            ->select('c.id,a.type,a.title,refresh_time')
            ->orderBy('refresh_time desc')
            ->limit($pageSize)
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['url'] = News::getDetailUrl($item['id']);
        }

        return $list;
    }

    /**
     * @param $columnId
     *
     * 新规则：展示近15天发布的页面浏览量最大的前9条信息；按页面资讯浏览量倒序排列。
     */
    public static function getAnnouncementHotList($columnId)
    {
        $pageSize = 36;

        $last15Days = TimeHelper::dayToBeginTime(date('Y-m-d', strtotime('-15 days')));
        $list       = Article::find()
            ->alias('a')
            ->innerJoin(['c' => Announcement::tableName()], 'c.article_id=a.id')
            ->innerJoin(['d' => ArticleColumn::tableName()], 'd.article_id=a.id')
            ->where([
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.status'    => [
                    Article::STATUS_ONLINE,
                    Article::STATUS_OFFLINE,
                ],
                'a.is_delete' => Article::IS_DELETE_NO,
                'd.column_id' => $columnId,
                'a.type'      => Article::TYPE_ANNOUNCEMENT,
                //测试过程中发现存在脏数据，这里加个限制，更准确
            ])
            ->andWhere([
                '>=',
                'refresh_time',
                $last15Days,
            ])
            ->select('c.id,a.type,c.title,refresh_time,click,a.id as articleId')
            ->orderBy('click desc,refresh_time desc')
            ->limit($pageSize)
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['url'] = Article::getDetailUrl($item['articleId'], $item['type']);
        }

        return $list;
    }

    public static function getNewsHotList($columnId)
    {
        if ($columnId == BaseHomeColumn::NEWS_TOP_ID) {
            // 这里其实是所有资讯都要!
            $columnId = BaseHomeColumn::NEWS_ID_LIST;
        }

        $pageSize   = 36;
        $last15Days = TimeHelper::dayToBeginTime(date('Y-m-d', strtotime('-15 days')));

        $list = Article::find()
            ->alias('a')
            ->innerJoin(['c' => News::tableName()], 'c.article_id=a.id')
            ->innerJoin(['d' => ArticleColumn::tableName()], 'd.article_id=a.id')
            ->where([
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.status'    => Article::STATUS_ONLINE,
                'a.is_delete' => Article::IS_DELETE_NO,
                'd.column_id' => $columnId,
            ])
            ->andWhere([
                '>=',
                'refresh_time',
                $last15Days,
            ])
            ->select('c.id,a.type,a.title,refresh_time,click')
            ->orderBy('a.click desc,refresh_time desc')
            ->limit($pageSize)
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['url'] = News::getDetailUrl($item['id']);
        }

        return $list;
    }

    /**
     * @param $columnId
     * 最新公告和简章
     */
    public static function getNewestAnnouncementList($columnId)
    {
        // 首先找到对应栏目下面所有还生效的栏目
        $columnList = self::find()
            ->where([
                'parent_id' => $columnId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->select('id,name')
            ->orderBy('sort desc')
            ->asArray()
            ->all();

        // 这里有一个十分特殊的设置

        $level1LinkLevel2 = Yii::$app->params['level1LinkLevel2'][$columnId];
        if ($level1LinkLevel2) {
            $columnList = array_merge($columnList, $level1LinkLevel2);
        }

        $level1NotLinkLevel2 = Yii::$app->params['level1NotLinkLevel2'][$columnId];
        if ($level1NotLinkLevel2) {
            // 去掉不需要的栏目
            foreach ($columnList as $key => $item) {
                foreach ($level1NotLinkLevel2 as $level2) {
                    if ($item['id'] == $level2['id']) {
                        unset($columnList[$key]);
                    }
                }
            }
        }

        foreach ($columnList as &$item) {
            $item['list'] = self::getColumnChildrenNewsAnnouncementList($item['id']);
            $item['url']  = self::getDetailUrl($item['id']);
        }

        return $columnList;
    }

    /**
     * @param $columnId
     * 最新公告和简章
     */
    public static function getNewestNewsList($columnId)
    {
        // 首先找到对应栏目下面所有还生效的栏目
        $columnList = self::find()
            ->where([
                'parent_id' => $columnId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->select('id,name')
            ->asArray()
            ->all();

        foreach ($columnList as &$item) {
            $item['list'] = self::getSimpleNewstList($item['id'], 1, 9);
            $item['url']  = self::getDetailUrl($item['id']);
        }

        // 这里按照数量排序栏目,以免第一个栏目没有数据
        usort($columnList, function ($a, $b) {
            return count($a['list']) < count($b['list']);
        });

        $newestList = self::getTimeNewstList();
        array_unshift($columnList, [
            'id'   => 10,
            'name' => '最新动态',
            'list' => $newestList,
        ]);

        return $columnList;
    }

    public static function getSecondAnnouncementChildrenListBySearch($params)
    {
        $pageSize = isset($params['pageSize']) ? $params['pageSize'] : 9;
        $page     = isset($params['page']) ? $params['page'] : 1;

        // 这里必须来一个栏目id,因为这里是按照栏目来查询的
        $columnId = isset($params['columnId']) ? $params['columnId'] : 0;
        if (!$columnId) {
            return [];
        }
        $topId = Article::find()
            ->alias('a')
            ->select('c.id')
            // 添加固定值isTop=1
            ->innerJoin(['c' => Announcement::tableName()], 'c.article_id=a.id')
            ->innerJoin(['d' => ArticleColumn::tableName()], 'd.article_id=a.id')
            ->innerJoin(['e' => ArticleAttribute::tableName()], 'e.article_id=a.id')
            ->where([
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.status'    => Article::STATUS_ONLINE,
                'a.is_delete' => Article::IS_DELETE_NO,
                'd.column_id' => $columnId,
                'e.type'      => ArticleAttribute::ATTRIBUTE_COLUMN_TOP,
            ])
            ->orderBy('sort_time desc')
            ->limit(6)
            ->column();

        $offset = ($page - 1) * $pageSize;
        /**
         *  * [areaId] =>
         * [majorId] =>
         * [educationId] =>
         * [jobCategoryId] =>
         * 这些条件其实都是职位的,可以直接先走职位的查询?
         */

        $areaId           = isset($params['areaId']) ? $params['areaId'] : 0;
        $majorId          = isset($params['majorId']) ? $params['majorId'] : 0;
        $educationId      = isset($params['educationId']) ? $params['educationId'] : 0;
        $jobCategoryId    = isset($params['jobCategoryId']) ? $params['jobCategoryId'] : 0;
        $isEstablishment  = isset($params['isEstablishment']) ? $params['isEstablishment'] : 0;
        $announcementHeat = isset($params['announcementHeat']) ? $params['announcementHeat'] : 0;
        $query            = Article::find()
            ->alias('a')
            ->innerJoin(['c' => Announcement::tableName()], 'c.article_id=a.id')
            ->innerJoin(['d' => ArticleColumn::tableName()], 'd.article_id=a.id')
            ->innerJoin(['cc' => BaseCompany::tableName()], 'cc.id = c.company_id')
            ->leftJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()], 'cc.group_score_system_id = cgss.id')
            ->where([
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.status'    => Article::STATUS_ONLINE,
                'a.is_delete' => Article::IS_DELETE_NO,
                'd.column_id' => $columnId,
            ]);

        if ($areaId || $majorId || $educationId || $jobCategoryId) {
            // 如果没有任何一个条件,那么就直接返回空数组
            $query->innerJoin(['j' => Job::tableName()], 'j.announcement_id=c.id');
        }

        if ($areaId) {
            // 这里做一个兼容吧,可以找省份,方便以后
            $areaLevel = Area::findOneVal(['id' => $areaId], 'level');
            // 如果是等级1,就拿下面的所有城市来查询
            if ($areaLevel == 1) {
                $areaId = Area::find()
                    ->select('id')
                    ->where(['parent_id' => $areaId])
                    ->column();
            }
            $query->andWhere(['j.city_id' => $areaId]);
        }

        if ($jobCategoryId) {
            $query->andWhere(['j.job_category_id' => $jobCategoryId]);
        }

        if ($educationId) {
            $query->andWhere(['j.education_type' => $educationId]);
        }

        if ($majorId) {
            // 这里要用find_in_set了
            $query->andWhere(new yii\db\Expression("FIND_IN_SET(:major_id, major_id)", [":major_id" => $majorId]));
        }
        //是否编制
        if ($isEstablishment) {
            $query->andWhere(['c.establishment_type' => BaseAnnouncement::IS_ESTABLISHMENT_TYPE_LIST]);
        }
        //热度查询
        if ($announcementHeat) {
            $query->andWhere(['c.announcement_heat_type' => $announcementHeat]);
        }

        if ($topId) {
            // 把这些id排序在最前面
            //            $query->orderBy(new yii\db\Expression('c.id in (' . implode(',', $topId) . ') desc, a.refresh_time desc'));
            $query->orderBy(new yii\db\Expression('c.id in (' . implode(',',
                    $topId) . ') desc, a.refresh_date desc,c.is_first_release asc,cgss.score desc,c.id desc'));
        } else {
            //            $query->orderBy('a.refresh_time desc');
            $query->orderBy(' a.refresh_date desc,c.is_first_release asc,cgss.score desc,c.id desc');
        }

        $list = $query->select('c.id,a.type,a.title,a.refresh_time as refreshTime,a.click as clickAmount,c.establishment_type,announcement_heat_type')
            ->limit($pageSize)
            ->offset($offset)
            ->groupBy('c.id')
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            if (in_array($item['id'], $topId)) {
                $item['isTop'] = '1';
            } else {
                $item['isTop'] = '0';
            }
            $item['url']           = Announcement::getDetailUrl($item['id']);
            $item['jobAmount']     = BaseJob::getAnnouncementJobAmount($item['id']) ?: 0;
            $item['recruitAmount'] = BaseJob::getAnnouncementJobRecruitAmount($item['id']) ?: '若干';
            //处理发布时间
            $item['refreshTime'] = TimeHelper::formatDateByYear($item['refreshTime']);
        }

        return $list;
    }

    /**
     *
     * 这里会涉及到比较复杂的查询逻辑,譬如专业,城市,学科等等(其实和下面那个返回的数据是一样一样的,只是,输入的条件不一样,这里会让用户自己选择)
     *  [columnId] => 238
     * [areaId] =>
     * [majorId] =>
     * [educationId] =>
     * [jobCategoryId] =>
     * [page] => 1
     */
    // public static function getSimpleAnnouncementListBySearch($params)
    // {
    //     $pageSize = isset($params['pageSize']) ? $params['pageSize'] : 9;
    //     $page     = isset($params['page']) ? $params['page'] : 1;
    //
    //     // 这里必须来一个栏目id,因为这里是按照栏目来查询的
    //     $columnId = isset($params['columnId']) ? $params['columnId'] : 0;
    //     if (!$columnId) {
    //         return [];
    //     }
    //
    //     $offset = ($page - 1) * $pageSize;
    //     /**
    //      *  * [areaId] =>
    //      * [majorId] =>
    //      * [educationId] =>
    //      * [jobCategoryId] =>
    //      * 这些条件其实都是职位的,可以直接先走职位的查询?
    //      */
    //
    //     $areaId        = isset($params['areaId']) ? $params['areaId'] : 0;
    //     $majorId       = isset($params['majorId']) ? $params['majorId'] : 0;
    //     $educationId   = isset($params['educationId']) ? $params['educationId'] : 0;
    //     $jobCategoryId = isset($params['jobCategoryId']) ? $params['jobCategoryId'] : 0;
    //     $query         = Article::find()
    //         ->alias('a')
    //         ->innerJoin(['c' => Announcement::tableName()], 'c.article_id=a.id')
    //         ->innerJoin(['d' => ArticleColumn::tableName()], 'd.article_id=a.id')
    //         ->where([
    //             'a.is_show'   => Article::IS_SHOW_YES,
    //             'a.status'    => Article::STATUS_ONLINE,
    //             'a.is_delete' => Article::IS_DELETE_NO,
    //             'd.column_id' => $columnId,
    //         ]);
    //
    //     if ($areaId || $majorId || $educationId || $jobCategoryId) {
    //         // 如果没有任何一个条件,那么就直接返回空数组
    //         $query->innerJoin(['j' => Job::tableName()], 'j.announcement_id=c.id');
    //     }
    //
    //     if ($areaId) {
    //         // 这里做一个兼容吧,可以找省份,方便以后
    //         $areaLevel = Area::findOneVal(['id' => $areaId], 'level');
    //         // 如果是等级1,就拿下面的所有城市来查询
    //         if ($areaLevel == 1) {
    //             $areaId = Area::find()
    //                 ->select('id')
    //                 ->where(['parent_id' => $areaId])
    //                 ->column();
    //         }
    //         $query->andWhere(['j.city_id' => $areaId]);
    //     }
    //
    //     if ($jobCategoryId) {
    //         $query->andWhere(['j.job_category_id' => $jobCategoryId]);
    //     }
    //
    //     if ($educationId) {
    //         $query->andWhere(['j.education_type' => $educationId]);
    //     }
    //
    //     if ($majorId) {
    //         // 这里要用find_in_set了
    //         $query->andWhere(new yii\db\Expression("FIND_IN_SET(:major_id, major_id)", [":major_id" => $majorId]));
    //     }
    //
    //     $query->orderBy('a.refresh_time desc');
    //
    //     $list = $query->select('c.id,a.type,a.title,a.refresh_time as refreshTime,a.click as clickAmount')
    //         ->orderBy('a.refresh_time desc')
    //         ->limit($pageSize)
    //         ->offset($offset)
    //         ->asArray()
    //         ->all();
    //
    //     foreach ($list as &$item) {
    //         $item['url']           = Announcement::getDetailUrl($item['id']);
    //         $item['jobAmount']     = BaseJob::getAnnouncementJobAmount($item['id']) ?: 0;
    //         $item['recruitAmount'] = BaseJob::getAnnouncementJobRecruitAmount($item['id']) ?: '若干';
    //         //处理发布时间
    //         $item['refreshTime'] = TimeHelper::formatDateByYear($item['refreshTime']);
    //     }
    //
    //     return $list;
    // }

    /**
     * 一级栏目下面的二级栏目里面的最新内容
     *
     * @param $columnId
     */
    public static function getColumnChildrenNewsAnnouncementList($columnId)
    {
        // 20个,但是有一个问题,就是这里在 http://zentao.jugaocai.com/index.php?m=bug&f=view&bugID=1377&tid=kt8qth5y的时候提出了一个新的需求,有限拿设置了栏目置顶的公告,最多6个,然后20-6剩下的才拿最新更新的
        // 我们首先得拿出来这个栏目标记了栏目置顶属性的那些公告

        $topAnnouncementIds = Article::find()
            ->alias('a')
            // 添加固定值isTop=1
            ->select('c.id,a.type,a.title,refresh_time as refreshTime,click as clickAmount')
            ->addSelect(new yii\db\Expression("'1' as isTop"))
            ->innerJoin(['c' => Announcement::tableName()], 'c.article_id=a.id')
            ->innerJoin(['d' => ArticleColumn::tableName()], 'd.article_id=a.id')
            ->innerJoin(['e' => ArticleAttribute::tableName()], 'e.article_id=a.id')
            ->where([
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.status'    => Article::STATUS_ONLINE,
                'a.is_delete' => Article::IS_DELETE_NO,
                'd.column_id' => $columnId,
                'e.type'      => ArticleAttribute::ATTRIBUTE_COLUMN_TOP,
            ])
            ->limit(6)
            ->orderBy('refresh_time desc')
            ->asArray()
            ->all();

        $query = Article::find()
            ->select('c.id,a.type,a.title,a.refresh_time as refreshTime,a.click as clickAmount')
            ->alias('a')
            ->innerJoin(['c' => Announcement::tableName()], 'c.article_id=a.id')
            ->innerJoin(['cc' => BaseCompany::tableName()], 'c.company_id=cc.id')
            ->innerJoin(['d' => ArticleColumn::tableName()], 'd.article_id=a.id')
            ->innerJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()], 'cc.group_score_system_id=cgss.id')
            ->where([
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.status'    => Article::STATUS_ONLINE,
                'a.is_delete' => Article::IS_DELETE_NO,
                'd.column_id' => $columnId,
            ]);
        if (count($topAnnouncementIds)) {
            $query->andWhere([
                'not in',
                'c.id',
                array_column($topAnnouncementIds, 'id'),
            ]);
        }
        $list = $query
            //            ->orderBy('a.refresh_time desc')
            ->orderBy('a.refresh_date desc,c.is_first_release asc,cgss.score desc,c.id desc')
            ->limit(20 - count($topAnnouncementIds))
            ->asArray()
            ->all();

        $list = array_merge($topAnnouncementIds, $list);

        foreach ($list as &$item) {
            $item['url']           = Announcement::getDetailUrl($item['id']);
            $item['jobAmount']     = BaseJob::getAnnouncementJobAmount($item['id']) ?: 0;
            $item['recruitAmount'] = BaseJob::getAnnouncementJobRecruitAmount($item['id']) ?: '若干';
            //处理发布时间
            $item['refreshTime'] = TimeHelper::formatDateByYear($item['refreshTime']);
        }

        return $list;
    }

    /**
     * 废弃了,迁移到上面的方法getColumnChilrenNewsAnnouncementList
     * 这个给首页用的,按照栏目来查询
     *
     * @param $columnId
     * @param $page
     * @param $pageSize
     * @param $attribute
     * @return array|yii\db\ActiveRecord[]
     */
    public static function getSimpleAnnouncementList($columnId, $page, $pageSize, $attribute = [])
    {
        $pageSize = isset($pageSize) ? $pageSize : 9;
        $page     = isset($page) ? $page : 1;

        $offset = ($page - 1) * $pageSize;

        $query = Article::find()
            ->alias('a')
            ->innerJoin(['c' => Announcement::tableName()], 'c.article_id=a.id')
            ->innerJoin(['d' => ArticleColumn::tableName()], 'd.article_id=a.id')
            ->where([
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.status'    => Article::STATUS_ONLINE,
                'a.is_delete' => Article::IS_DELETE_NO,
                'd.column_id' => $columnId,
            ]);
        if ($attribute) {
            $query->innerJoin(['b' => ArticleAttribute::tableName()], 'b.article_id=a.id');
            $query->andWhere(['b.type' => $attribute]);
            $query->orderBy('b.sort_time desc,a.refresh_time desc');
        } else {
            $query->orderBy('a.refresh_time desc');
        }

        //
        $list = $query->select('c.id,a.type,a.title,refresh_time as refreshTime,click as clickAmount')
            ->orderBy('refresh_time desc')
            ->limit($pageSize)
            ->offset($offset)
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['url']           = Announcement::getDetailUrl($item['id']);
            $item['jobAmount']     = BaseJob::getAnnouncementJobAmount($item['id']) ?: 0;
            $item['recruitAmount'] = BaseJob::getAnnouncementJobRecruitAmount($item['id']) ?: '若干';
            //处理发布时间
            $item['refreshTime'] = TimeHelper::formatDateByYear($item['refreshTime']);
        }

        return $list;
    }

    public static function getSimpleNewstList($columnId, $page, $pageSize, $attribute = [])
    {
        $offset = ($page - 1) * $pageSize;

        $query = Article::find()
            ->alias('a')
            ->innerJoin(['c' => News::tableName()], 'c.article_id=a.id')
            ->innerJoin(['d' => ArticleColumn::tableName()], 'd.article_id=a.id')
            ->where([
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.status'    => Article::STATUS_ONLINE,
                'a.is_delete' => Article::IS_DELETE_NO,
                'd.column_id' => $columnId,
            ]);
        if ($attribute) {
            $query->innerJoin(['b' => ArticleAttribute::tableName()], 'b.article_id=a.id');
            $query->andWhere(['b.type' => $attribute]);
            $query->orderBy('b.sort_time desc,a.refresh_time desc');
        } else {
            $query->orderBy('a.refresh_time desc');
        }
        //
        $list = $query->select('c.id,a.type,a.title,refresh_time as refreshTime,click as clickAmount,cover_thumb as coverThumb')
            ->orderBy('refresh_time desc')
            ->groupBy('c.id')
            ->limit($pageSize)
            ->offset($offset)
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            //处理发布时间
            $item['refreshTime'] = TimeHelper::formatDateByYear($item['refreshTime']);
            $item['url']         = News::getDetailUrl($item['id']);
        }

        return $list;
    }

    public static function getLevel2SelectList($columnId)
    {
        // 找到栏目下面的全部工作城市,全部工作地点,全部职位类型,先把全部有效的职位公告拿出来

        // 首先找到栏目下面的全部公告
        $announcementList = Announcement::find()
            ->alias('a')
            ->innerJoin(['b' => Article::tableName()], 'b.id=a.article_id')
            ->innerJoin(['c' => ArticleColumn::tableName()], 'c.article_id=b.id')
            ->where([
                'b.is_show'   => Article::IS_SHOW_YES,
                'b.status'    => Article::STATUS_ONLINE,
                'b.is_delete' => Article::IS_DELETE_NO,
                'c.column_id' => $columnId,
            ])
            ->select('a.id')
            ->column();

        $jobList = Job::find()
            ->select('city_id,major_id,job_category_id')
            ->where(['announcement_id' => $announcementList])
            ->andWhere([
                'is_show' => Job::IS_SHOW_YES,
                'status'  => Job::STATUS_ONLINE,
            ])
            ->asArray()
            ->all();

        $cityList     = [];
        $majorList    = [];
        $categoryList = [];
        foreach ($jobList as $item) {
            if (!in_array($item['city_id'], $cityList)) {
                $cityList[] = $item['city_id'];
            }

            $majorTmpList = explode(',', $item['major_id']);
            foreach ($majorTmpList as $majorItem) {
                if (in_array($majorItem, $majorList)) {
                    continue;
                }
                $majorList[] = $majorItem;
            }
            if (!in_array($item['job_category_id'], $categoryList)) {
                $categoryList[] = $item['job_category_id'];
            }
        }

        // 分别去重
        $cityList     = array_unique($cityList);
        $majorList    = array_unique($majorList);
        $categoryList = array_unique($categoryList);

        // 城市找到城市和对对应的父级
        $cityList = Area::find()
            ->select('id as k,name as v,parent_id as parentId')
            ->where(['id' => $cityList])
            ->asArray()
            ->all();

        $parentIds = [];
        foreach ($cityList as $item) {
            if ($item['parentId'] != 0) {
                if (!in_array($item['parentId'], $parentIds)) {
                    $parentIds[] = $item['parentId'];
                }
            }
        }

        $cityList = array_merge($cityList, Area::find()
            ->select('id as k,name as v,parent_id as parentId')
            ->where(['id' => $parentIds])
            ->asArray()
            ->all());

        // 把父id也加进去
        $majorList = Major::find()
            ->select('id as k,name as v,parent_id as parentId')
            ->where(['id' => $majorList])
            ->asArray()
            ->all();

        $parentIds = [];
        foreach ($majorList as $item) {
            if ($item['parentId'] != 0) {
                if (!in_array($item['parentId'], $parentIds)) {
                    $parentIds[] = $item['parentId'];
                }
            }
        }

        $majorList = array_merge($majorList, Major::find()
            ->select('id as k,name as v,parent_id as parentId')
            ->where(['id' => $parentIds])
            ->asArray()
            ->all());

        $categoryList = CategoryJob::find()
            ->select('id as k,name as v,parent_id as parentId')
            ->where(['id' => $categoryList])
            ->asArray()
            ->all();

        $parentIds = [];
        foreach ($categoryList as $item) {
            if ($item['parentId'] != 0) {
                if (!in_array($item['parentId'], $parentIds)) {
                    $parentIds[] = $item['parentId'];
                }
            }
        }

        $categoryList = array_merge($categoryList, CategoryJob::find()
            ->select('id as k,name as v,parent_id as parentId')
            ->where(['id' => $parentIds])
            ->asArray()
            ->all());

        // 按照k去重
        $cityList     = ArrayHelper::assocUnique($cityList, 'k');
        $categoryList = ArrayHelper::assocUnique($categoryList, 'k');
        $majorList    = ArrayHelper::assocUnique($majorList, 'k');

        return [
            'cityList'     => $cityList,
            'majorList'    => $majorList,
            'categoryList' => $categoryList,
        ];
    }

    public static function getTimeNewstList($page = 1, $pageSize = 9): array
    {
        $offset = ($page - 1) * $pageSize;

        $query = Article::find()
            ->alias('a')
            ->innerJoin(['c' => News::tableName()], 'c.article_id=a.id')
            ->innerJoin(['d' => ArticleColumn::tableName()], 'd.article_id=a.id')
            ->where([
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.status'    => Article::STATUS_ONLINE,
                'a.is_delete' => Article::IS_DELETE_NO,
            ]);

        $query->orderBy('a.refresh_time desc');

        $list = $query->select('c.id,a.type,a.title,refresh_time as refreshTime,click as clickAmount,cover_thumb as coverThumb')
            ->orderBy('refresh_time desc')
            ->groupBy('c.id')
            ->limit($pageSize)
            ->offset($offset)
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            //处理发布时间
            $item['refreshTime'] = TimeHelper::formatDateByYear($item['refreshTime']);
            $item['url']         = News::getDetailUrl($item['id']);
            if (strlen($item['coverThumb']) < 1) {
                $item['coverThumb'] = 'http://img.gaoxiaojob.com/uploads/static/image/defaultH5CoverThumb.png';
            }
        }

        return $list;
    }
}