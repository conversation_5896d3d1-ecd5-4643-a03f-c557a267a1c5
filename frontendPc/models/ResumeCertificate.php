<?php

namespace frontendPc\models;

use common\base\models\BaseMember;
use common\base\models\BaseMemberActionLog;
use common\base\models\BaseResume;
use common\base\models\BaseResumeCertificate;
use common\helpers\TimeHelper;
use common\libs\Cache;
use common\models\Certificate;
use yii\base\Exception;

class ResumeCertificate extends BaseResumeCertificate
{
    /**
     * 获取证书列表
     * @param $memberId
     * @return array
     * @throws \Exception
     */
    public static function getCertificateList($memberId): array
    {
        $list = self::find()
            ->where([
                'member_id' => $memberId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->select([
                'id',
                'obtain_date as obtainDate',
                'score',
                'certificate_id as certificateId',
                'certificate_custom as certificateCustom',
            ])
            //            ->limit(self::LIMIT_NUM)
            ->orderBy('obtain_date desc')
            ->asArray()
            ->all();

        foreach ($list as $k => &$item) {
            $item['obtainDate']      = TimeHelper::formatToYearMonth($item['obtainDate']);
            $item['certificateName'] = $item['certificateId'] > 0 ? Certificate::findOneVal(['id' => $item['certificateId']],
                'name') : $item['certificateCustom'];
        }

        return $list;
    }

    /**
     * 保存资质证明
     * @param $data
     * @throws Exception
     */
    public static function saveInfo($data)
    {
        $obtainDate    = $data['obtainDate'];
        $certificateId = $data['certificateId'] ?: 0;
        $score         = $data['score'];

        //判断数据
        if (strlen($obtainDate) < 1 || (strlen($certificateId) < 1 && strlen($data['certificateCustom']) < 1)) {
            throw new Exception('数据出错!');
        }
        if (strlen($score) < 1) {
            $score = '';
        }

        //判断是新增还是修改
        if (!empty($data['id'])) {
            //修改
            $model    = self::findOne($data['id']);
            $memberId = $model->member_id;
            if (empty($model) || $model->status != self::STATUS_ACTIVE) {
                throw new Exception('该资质证明记录不存在');
            }
        } else {
            //新增
            $memberId = $data['memberId'];

            $model = new self();

            $model->resume_id = BaseMember::getMainId($memberId);
            $model->member_id = $memberId;
        }

        $model->obtain_date        = TimeHelper::formatAddDay($obtainDate);
        $model->certificate_id     = $certificateId;
        $model->score              = $score;
        $model->certificate_custom = $data['certificateCustom'];

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        //更新简历完成度表
        ResumeComplete::updateResumeCompleteInfo($memberId);
        //新增操作日志
        $log_data = [
            'content' => '保存资质证书信息，memberId：' . $memberId,
        ];
        // 写日志
        BaseMemberActionLog::log($log_data);
    }

    /**
     * 删除资质证明
     * @param $id
     * @throws Exception
     */
    public static function delCertificate($id, $memberId)
    {
        $model = self::findOne($id);
        if ($model->member_id != $memberId) {
            throw new Exception('该记录与当前用户信息不匹配');
        }

        $model->status = self::STATUS_DELETE;

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        //更新简历完成度表
        ResumeComplete::updateResumeCompleteInfo($memberId);

        //新增操作日志
        $data = [
            'content' => '删除资质证明id：' . $id,
        ];
        // 写登录日志
        BaseMemberActionLog::log($data);
    }

}