<?php

namespace frontendPc\models;

use common\base\models\BaseCompany;
use common\base\models\BaseJob;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobCollect;
use common\base\models\BaseMemberActionLog;
use common\base\models\BaseResume;
use common\helpers\ArrayHelper;
use common\helpers\StringHelper;
use frontendPc\models\ResumeEquity;
use frontendPc\models\ResumeEquitySetting;
use yii\base\Exception;
use yii\helpers\Url;

class JobCollect extends BaseJobCollect
{

    /**
     * 获取职位收藏列表
     * @param $searchData
     * @param $needPageInfo
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getList($searchData, $needPageInfo)
    {
        $onlineStatus  = BaseJob::STATUS_ONLINE;
        $offlineStatus = BaseJob::STATUS_OFFLINE;
        $deleteStatus  = BaseJob::STATUS_DELETE;

        $query = self::find()
            ->alias('jc')
            ->leftJoin(['j' => Job::tableName()], 'j.id=jc.job_id')
            ->leftJoin(['c' => Company::tableName()], 'c.id=j.company_id')
            ->where(['jc.member_id' => $searchData['memberId']])
            ->andWhere(['jc.status' => self::STATUS_ACTIVE])
            ->andWhere([
                'in',
                'j.status',
                [
                    $onlineStatus,
                    $offlineStatus,
                    $deleteStatus,
                ],
            ]);

        $query->select([
            'jc.id',
            'j.id as jobId',
            'j.status as jobStatus',
            'j.name as jobName',
            'c.id as companyId',
            'c.full_name as companyName',
            'c.is_cooperation as isCooperation',
            //            'j.release_time as publishDate',
            'j.refresh_date as publishDate',
            'j.province_id',
            'j.city_id',
            'jc.add_time as addDate',
            'j.apply_type as applyType',
        ]);
        $count = $query->count();

        // 校验是否拥有收藏查看权益
        $colllectLimit = false;
        if ($count > 50 && ResumeEquity::checkEquity($searchData['resumeId'],
                ResumeEquitySetting::ID_COLLECT_VIEW) === false) {
            $colllectLimit = true;
            $count         = 50;
        }

        $pageSize = $searchData['pageSize'] ?: \Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $searchData['page'], $pageSize);
        $offset   = $pages['offset'];
        $limit    = $pages['limit'];
        if ($colllectLimit && $offset < $count && $offset + $limit > $count) {
            $limit = $count - $offset;
        }
        if ($colllectLimit && $offset >= $count) {
            $limit = 0;
        }

        $sort = "CASE WHEN `j`.`status` = $onlineStatus then 1 WHEN `j`.`status` = $offlineStatus then 2 WHEN `j`.`status` = $deleteStatus then 3 END,id desc";
        $list = $query->offset($offset)
            ->limit($limit)
            ->orderBy('j.status desc,jc.update_time desc')
            ->asArray()
            ->orderBy($sort)
            ->all();
        if (!empty($searchData['memberId'])) {
            //获取简历信息
            $resumeInfo = BaseResume::findOne([
                'member_id' => $searchData['memberId'],
                'status'    => BaseResume::STATUS_ACTIVE,
            ]);
        }
        foreach ($list as $k => &$v) {
            $areaName         = Area::getAreaName($v['province_id']) . '-' . Area::getAreaName($v['city_id']);
            $v['areaName']    = StringHelper::subtractString($areaName, '-');
            $v['applyStatus'] = 0;
            if (!empty($searchData['memberId'])) {
                //获取用户对该职位投递情况
                $v['applyStatus'] = BaseJobApplyRecord::checkJobApplyStatus($resumeInfo->id, $v['jobId']);
            }

            $v['url'] = Url::toRoute([
                'job/detail',
                'id' => $v['jobId'],
            ]);

            $v['companyUrl'] = Url::toRoute([
                'company/detail',
                'id' => $v['companyId'],
            ]);

            //判断单位是否合作单位
            $cooperationInfo = BaseCompany::findOneVal(['id' => $v['companyId']], 'is_cooperation');
            if ($cooperationInfo == BaseCompany::COOPERATIVE_UNIT_YES) {
                $v['isCooperation'] = true;
            } else {
                $v['isCooperation'] = false;
            }

            //判断职位投递方式是否是邮箱投递
            $applyTypeArr = explode(',', $v['applyType']);

            if (in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr)) {
                $v['isEmailApply'] = true;
            } else {
                $v['isEmailApply'] = false;
            }
        }

        if ($needPageInfo) {
            return [
                'list' => $list,
                'page' => [
                    'count' => intval($count),
                    'limit' => intval($pages['limit']),
                    'page'  => intval($pages['page']),
                ],
            ];
        } else {
            return $list;
        }
    }

    /**
     * 获取收藏职位数量
     * @param $memberId
     * @return bool|int|string|null
     */
    public static function getAmount($memberId)
    {
        return intval(self::find()
            ->where(['member_id' => $memberId])
            ->andWhere(['status' => self::STATUS_ACTIVE])
            ->count());
    }

}