<link rel="stylesheet" href="/static/css/competitivePower.css">
<script src="https://img.gaoxiaojob.com/uploads/static/lib/crypto/index.js"></script>

<div id="component" v-cloak>
    <main class="el-main">
        <div class="search-header">
            <div class="recommend-container">
                <div class="title">竞争力<span>洞察</span></div>
                <div class="subheading">职位竞争力分析+公告热度查询，知己知彼，提升求职胜算</div>
                <div class="set-meal">
                    <div class="list" :class="{active: index === packageIndex}" v-for="(item, index) in orderList" :key="item.equityPackageId" @mouseenter="handlePackageEnter(index)">
                        <div class="top" v-if="item.buyTypeTxt">{{item.buyTypeTxt}}</div>
                        <div class="content">
                            <div class="day">{{item.days}}天</div>
                            <div class="del">¥{{item.originalAmount}}</div>
                            <div class="buy-price">
                                <div class="allprice"><span>¥</span>{{item.realAmount}}</div>
                                <div class="amortized">
                                    <div class="discount">{{item.discountTxt}}</div>
                                    <div class="single">(单次低至¥{{item.timesAmount}})</div>
                                </div>
                            </div>
                        </div>
                        <div class="buy-btn" @click="openpayDialog(item.equityPackageCategoryId,index)">立即购买</div>
                    </div>
                </div>
                <div class="service-agreement">
                    * 购买即表示同意<a href="/agreement/value-added-services?type=1" target="_blank"><span>《高校人才网增值服务协议》</span></a>
                </div>
            </div>
        </div>
        <div class="job-analysis">
            <div class="analysis-content">
                <div class="analysis-title">职位竞争力分析</div>
                <div class="analysis-subheading">人岗智能匹配，多维解析简历，挖掘你的竞争优势！</div>
                <div class="analysis-description">
                    <div class="left-content">
                        <div class="left-catalogue" :class="index === jobIndex ? 'active' : ''" v-for="(item, index) in jobList" :key="item.Id" @click="jobTabClick(index)">
                            <img :src="index === jobIndex ? item.img : item.imgblack" alt="" />
                            <div class="text">
                                {{item.text}}
                                <span>{{item.subheading}}</span>
                            </div>
                        </div>
                    </div>
                    <div class="right-content">
                        <div class="item" :class="index === jobIndex ? 'active' : ''" v-for="(item, index) in jobImgList">
                            <img :src="item.img" alt="" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="announcement-heat">
            <div class="announcement-content">
                <div class="announcement-title">公告热度</div>
                <div class="announcement-subheading">了解公告热度和求职者的画像分布，掌握竞争情报</div>
                <div class="announcement-description">
                    <div class="left-content">
                        <div class="item" :class="index === announcementIndex ? 'active' : ''" v-for="(item, index) in announcementImgList">
                            <img :src="item.img" alt="" />
                        </div>
                    </div>
                    <div class="right-content">
                        <div
                            class="right-catalogue"
                            :class="index === announcementIndex ? 'active' : ''"
                            v-for="(item, index) in announcementList"
                            :key="item.Id"
                            @click="announcementTabClick(index)"
                        >
                            <img :src="index === announcementIndex ? item.img : item.imgblack" alt="" />
                            <div class="text">
                                {{item.text}}
                                <span>{{item.subheading}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="evaluate-wrapper">
            <div class="main-wrapper">
                <div class="wrapper-title">用户评价</div>
                <div class="wrapper-desc">1.3w+会员正在使用，好评如潮</div>
                <div class="evaluate-content">
                    <div class="list" v-for="item in evaluateList">
                        <div class="avatar">
                            <img :src="item.avatar" alt="" />
                        </div>
                        <div class="name">{{item.name}}</div>
                        <div class="ask">{{item.ask}}</div>
                        <div class="content">{{item.content}}</div>
                        <div class="tag">
                            <span v-for="tag in item.tag">{{tag}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="search-footer">
            <div class="search-content">
                <div class="search-title">服务说明</div>
                <div class="text-content">
                    <div class="left-text">
                        <p>* 请在本页面通过官方支付方式——微信扫码支付，并向唯一官方收款方“高校人才网”完成服务费用的支付。请勿尝试任何私下转账方式。</p>
                        <p>* “竞争力洞察”包含 职位竞争力分析&公告热度 2项服务。套餐购买后立即生效。</p>
                        <p>* 服务期内，按自然日，每项服务可使用的次数上限为10次/天，不累计；过期未使用则权益失效。</p>
                        <p>* 本报告结合高校人才网大数据生成，内容可能随求职动态而产生变化，结果仅供参考。</p>
                        <p>* 若同时购买了竞争力洞察套餐和VIP套餐，职位竞争力分析 & 公告热度 2项服务有效时长不会进行叠加处理，请按需购买。</p>
                        <p>* 本产品为线上虚拟服务，不支持退款。</p>
                        <p>
                            * 购买即表示同意<a href="/agreement/value-added-services?type=1" target="_blank"><span>《高校人才网增值服务协议》</span></a>
                        </p>
                    </div>
<!--                    <div class="right-text">-->
<!--                    </div>-->
                </div>
            </div>
        </div>

        <div v-if="showFooterFixed" class="fixed-wrapper">
            <div class="main-wrapper">
                <div class="buy-info">
                    竞争力洞察
                    <div class="single-price">单次低至<span>{{currentPackage.timesAmount}}</span>元，即刻解锁极速求职特权</div>
                </div>
                <div class="aside">
                    <div class="detail">
                        <div class="price">{{currentPackage.realAmount}}</div>
                        元/{{currentPackage.days}}天
                        <div class="original-price">{{currentPackage.originalAmount}}元</div>
                    </div>
                    <button class="pay" @click="openBuyDialog">立即开通</button>
                </div>
            </div>
        </div>
    </main>
</div>

<script>
    $(function () {
        const component = {
            data() {
                return {
                    packageIndex: 0,
                    currentPackage: {},
                    jobIndex: 0,
                    announcementIndex: 0,
                    orderList: [],
                    timer: null,
                    orderLoading: false,
                    jobList: [
                        {
                            img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/resume.png',
                            imgblack: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/resume-black.png',
                            id: '1',
                            text: '简历匹配度分析',
                            subheading: '结合求职意向，一键解析你与岗位的匹配度'
                        },
                        {
                            img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/jobicon.png',
                            imgblack: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/jobicon-black.png',
                            id: '2',
                            text: '职位热度分析',
                            subheading: '火爆or冷门，求职热度看得见，职位“捡漏”也能轻松上岸'
                        },
                        {
                            img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/person.png',
                            imgblack: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/person-black.png',
                            id: '3',
                            text: ' 竞争者能力分布 ',
                            subheading: '直观展示你的竞争优劣形势，让投递更有底气'
                        }
                    ],
                    announcementList: [
                        {
                            img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/heat-data.png',
                            imgblack: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/heatdata-black.png',
                            id: '1',
                            text: '综合热度',
                            subheading: '基于感兴趣人群分析，定位同类公告热度对比，助你求职决策'
                        },
                        {
                            img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/compete-active.png',
                            imgblack: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/compete.png',
                            id: '2',
                            text: '竞争态势&热度趋势',
                            subheading: '随时掌握关注热度趋势，洞悉投递竞争情况，让求职更有把握'
                        },
                        {
                            img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/job-analysis.png',
                            imgblack: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/job-black.png',
                            id: '3',
                            text: '求职者画像分析',
                            subheading: '是竞争对手也是圈内同行，速览未来从业者画像'
                        }
                    ],
                    jobImgList: [
                        {
                            img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/mate.png'
                        },
                        {
                            img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/job-heat.png'
                        },
                        {
                            img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/ability.png'
                        }
                    ],
                    announcementImgList: [
                        {
                            img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/heat.png'
                        },
                        {
                            img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/trend.png'
                        },
                        {
                            img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/job-draw.png'
                        }
                    ],

                    evaluateList: [
                        {
                            avatar: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/avatar-1.png',
                            name: '林**',
                            ask: '教务管理丨博士丨工作5年',
                            content: '竞争力分析报告真不错，可以看到自己的学历、专业、意向城市跟申请的职位匹不匹配，适合拿来检查简历投的对不对，自己的核心竞争力怎么样。',
                            tag: ['真不错']
                        },
                        {
                            avatar: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/avatar-2.png',
                            name: '宋**',
                            ask: '互联网后端开发丨硕士丨工作7年',
                            content: '现在投递简历前，都习惯查一下。可以看到热度趋势还有投递竞争比啥的，大概能了解自己有没有机会，有些我以为热度不高，结果还是很多人投。',
                            tag: ['性价比高', '分析维度多']
                        },
                        {
                            avatar: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/avatar-3.png',
                            name: '欧阳**',
                            ask: '学科教师丨博士丨工作2年',
                            content: '每天都能查10次，看到喜欢的公告就查一下，看看竞争情况怎么样，现在投简历会不会扎堆，如果能再根据报告结果加上一些求职建议就更好了。',
                            tag: ['性价比高', '体验好']
                        },
                        {
                            avatar: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/avatar-4.png',
                            name: '刘**',
                            ask: '科研助理丨硕士丨应届生',
                            content: '买了半个月的试了一下，里面的信息还蛮有用的，一个是可以看到自己和岗位的匹配情况，还有就是自己在人群中的位置，可以知道自己的优势和短板。',
                            tag: ['直观', '有参考价值']
                        }
                    ],
                    showFooterFixed: false
                }
            },
            methods: {
                jobTabClick(index) {
                    this.jobIndex = index
                    // 清空定时器
                    clearInterval(this.jobTimer)
                    // 重新设置定时器
                    this.setJobCarousel()
                },
                announcementTabClick(index) {
                    this.announcementIndex = index
                    // 清空定时器
                    clearInterval(this.announcementTimer)
                    // 重新设置定时器
                    this.setAnnouncementCarousel()
                },
                openpayDialog(id, index, position=1) {
                    //-------------用户付费转化数据埋点-开始---------------
                    // let productName = $('.list:eq('+index+') .day').text()
                    // //用户付费转化数据埋点
                    // let logData = {
                    //     params : { productId:id,productName:productName },
                    //     actionType : '1',
                    //     actionId : '10020002'
                    // }
                    // this.payBuriedPoint(logData)
                    let uuid = "<?=$data['uuid']?>"
                    //-------------用户付费转化数据埋点-结束---------------

                    let api = '/api/person/resume-equity-package/get-popup-package-list';
                    window.globalComponents.PayDialogAlertComponent.show(api, id, index, uuid, position)
                },
                show(id) {
                    this.orderLoading = true
                    // 请求数据
                    httpGet('/api/person/resume-equity-package/get-buy-package-list?equityPackageCategoryId=' +
                        id).then((r) => {
                        this.orderList = r.list
                        this.currentPackage = r.list[0]
                        this.orderLoading = false
                    })
                },
                setJobCarousel() {
                    // 设置轮播
                    let _this = this
                    this.jobTimer = setInterval(function () {
                        _this.jobIndex++
                        if (_this.jobIndex > 2) {
                            _this.jobIndex = 0
                        }
                    }, 5000)

                },
                setAnnouncementCarousel() {
                    // 设置轮播
                    let _this = this
                    this.announcementTimer = setInterval(function () {
                        _this.announcementIndex++
                        if (_this.announcementIndex > 2) {
                            _this.announcementIndex = 0
                        }
                    }, 5000)

                },
                //-------------用户付费转化数据埋点-开始---------------
                // payBuriedPoint(data) {
                //     const jsonString = JSON.stringify(data);
                //
                //     let configKey = '123abcgaoxiaorencaiwang'
                //     let code = CryptoJS.MD5(configKey).toString()
                //     let iv = CryptoJS.enc.Utf8.parse(code.substring(0,16))
                //     let key = CryptoJS.enc.Utf8.parse(code.substring(16))
                //
                //     let encryptString = CryptoJS.AES.encrypt(jsonString,key,{mode:CryptoJS.mode.ECB,iv:iv});
                //     let img = new Image()
                //     img.src = `3.gif?data=`+encodeURIComponent(encryptString)
                // },
                //-------------用户付费转化数据埋点-结束---------------

                scroll() {
                    const _this = this
                    window.addEventListener('scroll', function () {
                        const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
                        const showHeight = document.body.clientHeight
                        _this.showFooterFixed = scrollTop > showHeight * 1
                    })
                },

                handlePackageEnter(index) {
                    this.packageIndex = index
                    this.currentPackage = this.orderList[index]
                },

                openBuyDialog() {
                    const index = this.packageIndex
                    const id = this.currentPackage.equityPackageCategoryId
                    this.openpayDialog(id, index, 2)
                }
            },
            mounted() {
                let that = this
                this.show(2)
                this.setJobCarousel()
                this.setAnnouncementCarousel()

                this.scroll()

                //-------------用户付费转化数据埋点-开始---------------
                window.onbeforeunload = function (){
                   httpGet("/showcase-browse-log/update-competitive-power-view-point-log?uuid=<?=$data['uuid']?>")
                }
                //-------------用户付费转化数据埋点-结束---------------
            },
        }

        Vue.createApp(component).use(ElementPlus).mount('#component')
    })
</script>

<?= \frontendPc\components\DialogPaymentSuccess::widget() ?>
<?= \frontendPc\components\DialogResumePayWidget::widget() ?>
