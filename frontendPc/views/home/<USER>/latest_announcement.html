<div class="release-container w">
    <div class="common-title component">
        <h2>最新公告&简章</h2>
        <div class="data">
            <div class="amount">
                共<span>
                    {{latestAnnouncementTotal}}
                </span>则公告
            </div>
        </div>
    </div>

    <div class="type-select">
        <el-form :model="latestAnnouncementForm" inline>
            <?php  foreach ($tapList as $key => $item) {?>
            <el-form-item>
                <el-select @change="() => handleLatestAnnouncement()"
                    filterable placeholder="<?php echo $item[0]['v'];?>" clearable
                           v-model="<?php echo 'latestAnnouncementForm.'.$key?>" class="m-2" size="small">
                    <?php foreach ($item as $i => $value) { ?>
                    <el-option label="<?php echo $value['v'];?>" value="<?php echo $value['k'];?>"></el-option>
                    <?php } ?>
                </el-select>
            </el-form-item>
            <?php }?>
        </el-form>

        <div class="superior-filter-global" :class="{active: hasSuperiorAnnouncementFilter}" @click="handleSuperior(2)">
            高级筛选
            <div class="superior-tips">含编制、热度等会员专属筛选特权</div>
        </div>
    </div>

    <div class="announcement-content">
        <!-- 标题 -->
        <div class="recruit-information recruit-title">
            <span class="date">日期</span>
            <span class="title">公告标题</span>
            <span class="place">地点</span>
            <span class="job-quantity">职位数量</span>
            <span class="recruit-quantity">招聘人数</span>
            <span class="closing-date">截止日期</span>
        </div>

        <!-- 内容 -->
        <?= frontendPc\components\LatestAnnouncementWidget::widget(['columnId'=>$columnId]) ?>
    </div>

    <el-pagination background :layout="paginationLayout" :current-page="latestAnnouncementForm.page"
                   :page-size="latestAnnouncementPageSize"
                   :total="latestAnnouncementTotal" @current-change="(val) => handleLatestAnnouncement(val)">
    </el-pagination>
</div>