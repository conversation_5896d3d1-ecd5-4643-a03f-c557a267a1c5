#LT {
	position: fixed;
	border: 1px #C2C2C2 solid;
	width: 30px;
	top: 40%;
	left: 370px;
	background-color: #FFF;
	text-align: center;
	padding-bottom: 8px;
	font-size:12px;
}

#LT A {
	text-decoration: none;
	color: #625351;
}

#LT A:HOVER {
	color: #fff;
	background-color: #1258AD
}

#LT .c {
	color: #fff;
	background-color: #1258AD
}

#LT .e {
	color: #fff;
	background-color: #1258AD
}

#LT .z {
	color: #fff;
	background-color: red
}

#LT .f {
	color: #fff;
	background-color: #1258AD
}

#LT .g {
	color: #fff;
	background-color: #1258AD
}

#LT span {
	line-height: 28px;
	border-bottom: 1px #C2C2C2 dashed;
	padding-bottom: 6px;
}
/*LT样式*/

 /*左侧咨询�&#65533;*/
#side-bar li { list-style: none;}
#side-bar sub, sup { font-size: 83%;}
#side-bar code, kbd, pre, samp { font-family: inherit;}
#side-bar q:after, q:before { content: none;}
#side-bar html { -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;}
#side-bar *, *:after, *:before { -moz-box-sizing: border-box; -webkit-box-sizing: border-box; -o-box-sizing: content-box; box-sizing: border-box;}
#side-bar input, select, textarea { font-family: inherit; font-size: inherit; font-weight: inherit;}
#side-bar button, input[type=button], input[type=reset], input[type=submit] { cursor: pointer; -webkit-appearance: button;}
#side-bar button[disabled], input[disabled] { cursor: default;}
#side-bar img { image-rendering: optimizeQuality; -ms-interpolation-mode: bicubic;}
#side-bar p{ line-height:24px}
#side-bar lable { cursor: pointer;}
#side-bar .fl { float: left;}
#side-bar .fr { float: right;}
#side-bar .tac { text-align: center;}
#side-bar .tal { text-align: left;}
#side-bar .tar { text-align: right;}
#side-bar .vat { vertical-align: top;}
#side-bar .vam { vertical-align: middle;}
#side-bar .clear-fix:after { content: ''; display: block; height: 0; visibility: hidden; clear: both;}
#side-bar .clear-fix { zoom: 1; clear: both;}
#side-bar .hide { display: none;}
#side-bar .ellipsis { overflow: hidden; text-overflow: ellipsis; -o-text-overflow: ellipsis; white-space: nowrap;}
#side-bar .inline-block { display: inline-block;*display:inline; zoom: 1;}
#side-bar .ime-disabled { -webkit-ime-mode: disabled; ime-mode: disabled;}
#side-bar body { font: 12px Verdana, Arial, "\5FAE\8F6F\96C5\9ED1"; line-height: 1;*line-height:1.2;}
#side-bar a, a:hover { text-decoration: none;}

.side-pannel { position: fixed; right: 0%; z-index: 99;;}
.side-bar { border: 1px solid #E7E7E7; border-bottom: none; background: #fff; top:450px;}
.side-bar a { display: block; cursor: pointer; width: 60px; height: 60px; border-bottom: 1px solid #E7E7E7; position: relative; color: #676767; text-align: center; }
.side-bar a s { display: block; margin-left: auto; margin-right: auto;}
.side-bar a span { font-size: 12px; line-height: 20px;}

.side-bar a.gotop s { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);}
.side-bar a.text { padding-top: 22px;}
.side-bar a.qr s { position: absolute; top: 50%; left: 18px;}
.side-bar a.qr i { width: 277px; height: 259px; position: absolute; left: -277px; top: 50%; margin-top: -120px; display: none; background-position: 0 0;}
.side-bar a.qr:hover i { display: block;}
.side-bar a.qr i { background-image: url(../images/weixin.png);}

.g-icon-top { height: 20px; width: 35px; background-position: 0 -33px;}
.g-icon-survey1 { height: 23px; width: 26px; margin-top: -11px; background-position: 0 -53px;}
.side-bar a.survey:hover s { height: 23px; width: 26px; margin-top: -11px; background-position: -26px -53px;}
.g-icon-qq1 { height: 20px; width: 22px; margin-top: -10px; background-position: -35px -33px;}
.g-icon-weibo1 { height: 25px; width: 27px; margin-top: -12px; background-position: -72px 0;}
.g-icon-weibo2, .side-bar a.weibo:hover s { height: 25px; width: 27px; margin-top: -12px; background-position: -72px -25px;}
.g-icon-qr1 { height: 33px; width: 36px; margin-top: -16px; background-position: -36px 0;}
.g-icon-qr2, .side-bar a.qr:hover s { height: 33px; width: 36px; margin-top: -16px; background-position: 0 0;}
/* 新增1 */
.g-icon-phone1 { height: 25px; width: 24px; margin-top: -12px; background-position: 47.7px 38px;}
.g-icon-phone2, .side-bar a.phone:hover s { height: 25px; width: 25px; margin-top: -12px; background-position: 26px 38px;}
.side-bar a.phone i { width: 277px; height: 259px; position: absolute; left: -277px; top: 50%; margin-top: -120px; display: none; background-position: 0 0;}
.side-bar a.phone:hover i { display: block;}
.side-bar a.phone i { background-image: url(../images/phone2020.png);}

/* 新增1 */
/* 新增2 */
.g-icon-weinxin1 { height: 33px; width: 36px; margin-top: -16px; background-position: -36px 0;}
.g-icon-weinxin2, .side-bar a.weinxin:hover s { height: 33px; width: 36px; margin-top: -16px; background-position: 0 0;}
.side-bar a.weinxin i { width: 277px; height: 259px; position: absolute; left: -277px; top: 50%; margin-top: -120px; display: none; background-position: 0 0;}
.side-bar a.weinxin:hover i { display: block;}
.side-bar a.weinxin i { background-image: url(../images/weixin.png);}

/* 新增2 */
.side-bar a.weinxin:hover s, .g-icon-weinxin1, .g-icon-weinxin2, .side-bar a.phone:hover s, .g-icon-phone1, .g-icon-phone2,.g-icon-qq1, .g-icon-qr1, .g-icon-qr2, .g-icon-survey1, .g-icon-survey2, .g-icon-top, .g-icon-weibo1, .g-icon-weibo2,.side-bar a.qr:hover s, .side-bar a.survey:hover s, .side-bar a.weibo:hover s{background-image: url(../images/sprite.png)}
 
 