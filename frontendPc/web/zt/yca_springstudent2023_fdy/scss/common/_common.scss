@use './variables' as *;

* {
    margin: 0;
    padding: 0;
}

a {
    text-decoration: none;
}

li {
    list-style: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: normal;
}

body {
    font-size: $font-size-sm;
}

img {
    display: block;
    width: 100%;
    height: auto;
    -webkit-user-drag: none;
    object-fit: contain;
}

/* footer start */
.footer-container {
    width: 100%;
    height: $footer-height;
    overflow: hidden;
    background-color: #1f2329;
    display: flex;
    justify-content: center;
    align-items: center;
    display: -webkit-flex;
    -webkit-justify-content: center;
    -webkit-align-items: center;

    .footer-content-box {
        width: 1300px;
        height: 170px;
        margin: 0 auto;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        display: -webkit-flex;
        -webkit-flex-direction: row;
        -webkit-justify-content: space-between;
        -webkit-align-items: center;
        overflow: hidden;
    }

    .footer-left {
        width: 960px;
    }

    .footer-nav {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        display: -webkit-flex;
        -webkit-flex-direction: row;
        -webkit-justify-content: space-between;
        -webkit-align-items: center;

        li {
            a {
                color: #fff;
                font-size: $font-size-sm;
            }
        }
    }

    .footer-left {
        p {
            font-size: $font-size-sm;
            color: #838687;
            line-height: 30px;
            text-align: left;
        }

        .num-one {
            margin-top: 30px;
        }
    }

    .footer-right {
        width: 200px;
        height: 150px;
        overflow: hidden;
        border-left: 1px solid #fff;
        padding-left: 50px;
        text-align: left;

        img {
            width: 150px;
            height: 40px;
            padding-top: 60px;
            box-sizing: content-box;
            object-fit: contain;
        }

        p {
            color: #838687;
            font-size: $font-size-sm;
            line-height: 40px;
        }
    }

    .footer-enroll {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        display: -webkit-flex;
        -webkit-flex-direction: row;
        -webkit-justify-content: space-between;
        -webkit-align-items: center;

        li {
            img {
                width: 50px;
                height: 50px;
            }
        }
    }
}

/* footer end */

.header-container {
    background-color: #1a1a1a;

    .header-content {
        height: 60px;
        width: 1200px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0 auto;
    }

    .company-logo {
        width: 128px;
        height: 35px;
        background: url('//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png') no-repeat center/contain;
    }

    nav {
        display: flex;
        height: 100%;

        a {
            position: relative;
            padding: 0 35px;
            text-align: center;
            color: #fff;
            font-size: 16px;
            display: flex;
            align-items: center;

            &:last-child {
                border-right: 0;
            }
        }
    }
}

.main-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #2a3fcc url(../assets/bg.png) no-repeat center/cover;
    .nav-bar {
        display: flex;
        width: 1200px;
        padding: 7px;
        background-color: #2a5afa;
        border-radius: 10px;
        border: 1px solid #54b5fe;
        margin-top: -30px;
        a {
            width: 290px;
            text-align: center;
            color: $color-white;
            font-weight: bold;
            font-size: 20px;
            padding: 18px 0;
        }
        .nav-active {
            background-color: #033dcf;
            border: 1px solid #00f9f0;
            border-radius: 10px;
        }
    }
}
