<?php

namespace haiWai\controllers;

use common\service\abroadColumn\QiuxianService;

/**
 * 求贤公告
 */
class RecruitAnnouncementController extends BaseHaiWaiController
{
    /**
     * 求贤公告页面渲染
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetAll()
    {
        $data = (new QiuxianService())->getAll();

        return $this->success($data);
    }

    public function actionGetAnnouncementList()
    {
        $searchData = \Yii::$app->request->get();
        $data       = (new QiuxianService())->getAnnouncementList($searchData);

        return $this->success($data);
    }

    public function actionGetJobList()
    {
        $searchData = \Yii::$app->request->get();
        $data       = (new QiuxianService())->getJobList($searchData);

        return $this->success($data);
    }

}