<?php

namespace hai<PERSON>ai\controllers;

use common\service\abroadColumn\OverseasTalentService;
use haiWai\models\Seo;

/**
 * 出海引才
 */
class OverseasTalentController extends BaseHaiWaiController
{
    /**
     * 获取页面所有信息
     * @return \yii\console\Response|\yii\web\Response
     * @throws \yii\base\Exception
     */
    public function actionGetAll()
    {
        $data = (new OverseasTalentService())->getAll();

        return $this->success($data);
    }

    /**
     * 查询获取活动列表
     * @throws \yii\base\Exception
     */
    public function actionGetActivityList()
    {
        $searchData = \Yii::$app->request->get();
        $seoInfo    = Seo::getSeoUrl($searchData, Seo::TYPE_CHUHAI);
        if ($seoInfo == false) {
            return $this->result('');
        }
        $data                 = (new OverseasTalentService())->getActivityList($seoInfo['params']);
        $data['searchParams'] = $seoInfo['searchList'];

        return $this->success($data);
    }

}