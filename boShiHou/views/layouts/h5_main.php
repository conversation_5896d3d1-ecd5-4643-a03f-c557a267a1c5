<?php
/* @var $this \yii\web\View */

/* @var $content string */

use common\libs\WxMiniApp;
use h5\components;

use yii\helpers\Html;


?>
<?php $this->beginPage(); ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title><?= Html::encode($this->title); ?></title>
    <?php $this->head() ?>
    <meta name="format-detection" content="telephone=no"/>
    <meta name="applicable-device" content="mobile">
    <?php if (Yii::$app->params['toutiaoFactor']): ?>
        <?= Yii::$app->params["toutiaoFactor"] ?>
    <?php endif; ?>
    <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/h5_static/favicon.ico" type="image/x-icon">

    <link rel="stylesheet" href="//img.gaoxiaojob.com/uploads/h5Static/lib/weui/css/index.min.css">
    <link rel="stylesheet" href="//img.gaoxiaojob.com/uploads/h5Static/lib/swiper/swiper.min.css">
    <link rel="stylesheet" href="//img.gaoxiaojob.com/uploads/h5Static/lib/mobileSelect/mobileSelect.min.css">
    <link rel="stylesheet" href="/h5_static/css/layout.min.css?t=20240808">
    <script src="//img.gaoxiaojob.com/uploads/h5Static/lib/weui/js/zepto.min.js"></script>
    <script src="//img.gaoxiaojob.com/uploads/h5Static/lib/weui/js/zepto.weui.js"></script>
    <script src="//img.gaoxiaojob.com/uploads/h5Static/lib/weui/js/php.js"></script>
    <script src="//img.gaoxiaojob.com/uploads/h5Static/lib/swiper/swiper.min.js"></script>
    <script src="//img.gaoxiaojob.com/uploads/h5Static/lib/mobileSelect/mobileSelect.min.js"></script>
    <script src="//img.gaoxiaojob.com/uploads/h5Static/lib/axios/axios.min.js"></script>
    <script src="//img.gaoxiaojob.com/uploads/h5Static/lib/qs/qs.min.js"></script>
    <script src="//img.gaoxiaojob.com/uploads/h5Static/lib/clipboard/clipboard.min.js"></script>
    <script src="/h5_static/js/ajax.js"></script>
    <script src="/h5_static/js/backtop.js"></script>
    <script src="https://res.wx.qq.com/open/js/jweixin-1.4.0.js"></script>
    <script src="/h5_static/js/utils.js?v=4"></script>
    <script src="/h5_static/js/validation.js"></script>
    <script src="/h5_static/js/signup-popup.js"></script>
    <script src="/h5_static/js/clearable.js"></script>
</head>

<body>
<?php $this->beginBody(); ?>
<div class="container">
    <?= boShiHou\components\H5HeaderWidget::widget() ?>
    <!--查询是否有底部栏 2.7.1需求-->
    <?php $footerType = false?>
        <section class="main-container <?php if (!empty($footerType)): ?>has-tabbar<?php endif ?>">
        <?= $content; ?>
    </section>

</div>
<!--时间因子-->
<?php if (Yii::$app->params['baiduTimeFactor']): ?>
    <?= Yii::$app->params["baiduTimeFactor"] ?>
<?php endif; ?>
<?php $this->endBody(); ?>


<script>
    /* 是否开启统计 */
    <?php if (Yii::$app->params['bdtj']): ?>
    var _hmt = _hmt || []
    ;(function () {
        var hm = document.createElement('script')
        hm.src = 'https://hm.baidu.com/hm.js?<?=Yii::$app->params["bdtj"] ?>'
        var s = document.getElementsByTagName('script')[0]
        s.parentNode.insertBefore(hm, s)
    })()
    <?php endif; ?>

    /* token */
    var _maq = _maq || []
    var token = "<?php echo Yii::$app->params['showcaseBrowse']['token'];?>"
    _maq.push(['_setAccount', token]);

    window.global = {
        schemeUrl: '<?= Yii::$app->params['wx']['personMiniApp']['homeUrl'];?>',
        serviceAgreementUrl: '<?= Yii::$app->params['serviceAgreementUrl'];?>',
        privacyPolicyUrl: '<?= Yii::$app->params['privacyPolicyUrl'];?>'
    }
</script>
<script src="/h5_static/js/showcase.js"></script>

</body>

</html>
<?php $this->endPage(); ?>
