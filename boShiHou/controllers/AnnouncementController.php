<?php
/**
 * create user：shannon
 * create time：2024/9/14 下午2:15
 */
namespace boShiHou\controllers;

use common\helpers\UrlHelper;
use common\libs\BaiduTimeFactor;
use common\libs\ToutiaoTimeFactor;
use common\service\boShiHouColumn\AnnouncementService;
use common\service\CommonService;
use Yii;

class AnnouncementController extends BaseBoShiHouController
{
    /**
     * 博士后公告列表
     */
    public function actionIndex()
    {
        //如果携带单位类型  未登录直接301重定向到PC登录页
        if (Yii::$app->user->isGuest && !empty(Yii::$app->request->get('companyType'))) {
            // 拿到来源(没有来源的情况)
            $referer = Yii::$app->request->referrer;
            if (!$referer) {
                Yii::$app->response->setStatusCode(503)
                    ->send();
                exit();
            }
            // echo Yii::$app->view->renderFile('@frontendPc/views/home/<USER>');
            // $this->redirect(UrlHelper::createPcPersonLoginPath(), 301);
        }
        $service = new AnnouncementService();
        $result  = $service->setPlatform(CommonService::PLATFORM_WEB)
            ->run();
        $this->setSeo($result['searchData']['seo']);

        BaiduTimeFactor::createMetaTagWithHour(8);
        ToutiaoTimeFactor::createMetaTagWithHour(8);

        return $this->render('index.html', ['data' => $result]);
    }
}