// clearable start
$(function () {
    function getClear() {
        return this.parents('.weui-cell').find('.weui-icon-clear')
    }

    function handleClear() {
        var $input = $(this)
        var value = $input.val()
        var $clear = getClear.call($input)

        if (value.length) {
            if ($clear.is(':hidden')) {
                $clear.fadeIn()
            }
        } else {
            $clear.fadeOut()
        }
    }

    $('.weui-input').on('focus', function () {
        handleClear.call(this)
    })

    $('.weui-input').on('input', function () {
        handleClear.call(this)
    })

    $('.weui-input').on('blur', function () {
        getClear.call($(this)).fadeOut()
    })

    $('.weui-icon-clear').on('click', function () {
        $(this).fadeOut()
        $(this).parent().find('.weui-input').val('')
    })

    $('.weui-icon-password').on('click', function () {
        var $input = $(this).parent().find('.weui-input')
        var isPassword = $input.attr('type') === 'password'

        $input.attr('type', isPassword ? 'text' : 'password')
        $(this).toggleClass('is-show')
    })
})
// clearable end
