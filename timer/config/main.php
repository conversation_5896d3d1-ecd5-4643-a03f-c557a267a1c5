<?php
$params = array_merge(require __DIR__ . '/../../common/config/params.php',
    require __DIR__ . '/../../common/config/params-local.php', require __DIR__ . '/params.php',
    require __DIR__ . '/params-local.php');

return [
    'id'                  => 'app-timer',
    'basePath'            => dirname(__DIR__),
    'bootstrap'           => ['log'],
    'controllerNamespace' => 'timer\controllers',
    'aliases'             => [
        '@bower' => '@vendor/bower-asset',
        '@npm'   => '@vendor/npm-asset',
    ],
    'components'          => [
        'log' => [
            'targets' => [
                [
                    'class'  => 'yii\log\FileTarget',
                    'levels' => [
                        'error',
                        'warning',
                    ],
                ],
            ],
        ],
        'urlManager'   => [
            'enablePrettyUrl' => true,
            'showScriptName'  => false,
            'rules'           => [
                // 职位详情,做成伪静态
                'job/detail/<id:\d+>.html'                     => 'job/detail',
                'company/detail/<id:\d+>.html'                 => 'company/detail',
                'news/detail/<id:\d+>.html'                    => 'news/detail',
                'daily/detail/<id:\d+>.html'                   => 'daily-announcement-summary/detail',
                'announcement/detail/<id:\d+>.html'            => 'announcement/detail',
                'column/<id:\d+>.html'                         => 'home/column',
                // 兼容之前的栏目页面链接
                'zhaopin/<level1:\w+>/?'                       => 'home/old-column',
                'zhaopin/<level1:\w+>/index.html'              => 'home/old-column',
                'zhaopin/<level1:\w+>/<level2:\w+>/?'          => 'home/old-column',
                'zhaopin/<level1:\w+>/<level2:\w+>/index.html' => 'home/old-column',
                'qiuzhi/?'                                     => 'home/old-news',
                'qiuzhi/<level1:\w+>/?'                        => 'home/old-news',
                'renshi/<level1:\w+>/?'                        => 'home/old-news',
                // 'zhaopin/<level1:\w+>/<level2:\w+>/<id:\d+>.html'              => 'home/old-article',
                // 'qiuzhi/<level1:\w+>/<level2:\w+>/<id:\d+>.html'               => 'home/old-article',
                // 'zhaopin/<level1:\w+>/<level2:\w+>/<level3:\w+>/<id:\d+>.html' => 'home/old-article',
                '1.gif'                                        => 'showcase-browse-log/add-showcase-browse-log',
                'region.html'                                  => 'home/region',
                'major.html'                                   => 'home/major',
                'daily.html'                                   => 'home/daily',
                'search'                                       => 'home/search',
                'plus/search.php'                              => 'home/search',
                'data/sitemap.html'                            => 'home/sitemap',
                'zhaopin/xuqiuxueke/'                          => 'home/major',
                'plus/count.php'                               => 'home/old-count',
                'plus/list.php'                                => 'home/old-tcolumn',

            ],
        ],
    ],
    'params'              => $params,
];
