<?php

namespace timer\controllers;

use common\base\models\BaseMember;
use common\base\models\BaseMemberMessage;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEquity;
use common\base\models\BaseResumeEquityActionRecord;
use common\base\models\BaseResumeEquityPackage;
use common\base\models\BaseResumeEquityPackageCategorySetting;
use common\base\models\BaseResumeEquityPackageDetail;
use common\base\models\BaseResumeEquitySetting;
use common\base\models\BaseResumeOrder;
use common\base\models\BaseResumeTopConfig;
use common\libs\SmsQueue;
use common\libs\WxWork;
use queue\Producer;

/**
 * 处理简历权益即将过期提醒
 */
class ResumeEquityServiceAboutController extends BaseTimerController
{
    //服务过期提醒日期
    private $about_days = [
        7,
        3,
    ];

    /**
     * 简历权益即将过期提醒
     * php timer_yii resume-equity-service-about/run
     */
    public function actionRun()
    {
        //1、计算出要提醒的日期
        if (empty($this->about_days) || !is_array($this->about_days)) {
            exit('不需要提醒！');
        }
        $expire_time = [];
        foreach ($this->about_days as $day) {
            $item_date               = date('Y-m-d', strtotime("+{$day} day"));
            $expire_time[$item_date] = $day;
        }
        //2、查看权益套餐包的日期的在提醒的日期里数据
        $data = BaseResumeEquityPackage::find()
            ->select([
                'resume_id',
                'DATE_FORMAT(expire_time, "%Y-%m-%d") as expire_date',
                'package_category_id',
            ])
            ->where([
                'in',
                'DATE_FORMAT(expire_time, "%Y-%m-%d")',
                array_keys($expire_time),
            ])
            ->andWhere(['expire_status' => BaseResumeEquityPackage::STATUS_EXPIRE])
            ->groupBy('resume_id,package_category_id')
            ->asArray()
            ->all();

        foreach ($data as $item) {
            $resumeId = $item['resume_id'];
            //增加判断只有代开通的权益，如果是那就不提醒-即看看有没有自主完成的订单
            if (BaseResumeOrder::find()
                ->where([
                    'resume_id' => $resumeId,
                    'status'    => BaseResumeOrder::STATUS_PAID,
                ])
                ->andWhere([
                    '<>',
                    'platform',
                    BaseResumeOrder::PLATFORM_ADMIN,
                ])
                ->exists()) {
                continue;
            }
            // 查询手机号
            $memberId            = BaseResume::findOneVal(['id' => $resumeId], 'member_id');
            $mobile              = BaseMember::findOneVal(['id' => $memberId], 'mobile');
            $mobileCode          = BaseMember::findOneVal(['id' => $memberId], 'mobile_code');
            $packageCategoryInfo = BaseResumeEquityPackageCategorySetting::findOne($item['package_category_id']);
            // 发送短信
            Producer::sms($mobile, BaseMember::TYPE_PERSON, SmsQueue::TYPE_RESUME_EQUITY_SERVICE_ABOUT, $mobileCode,
                json_encode([
                    'day'                   => $expire_time[$item['expire_date']],
                    'package_category_name' => $packageCategoryInfo->name,
                ]));
            //站内信-系统消息
            BaseMemberMessage::send($memberId, BaseMemberMessage::TYPE_RESUME_SYSTEM, '服务到期提醒',
                '用户您好！您所购买的' . $packageCategoryInfo->name . $expire_time[$item['expire_date']] . '天后将过期，请及时使用资源。');
        }
    }
}