<?php

namespace timer\controllers;

use admin\models\Company;
use admin\models\Resume;
use admin\models\RuleAnnouncement;
use admin\models\RuleJob;
use common\base\BaseActiveRecord;
use common\base\models\BaseActivityForm;
use common\base\models\BaseActivityFormIntentionOption;
use common\base\models\BaseActivityFormOptionSign;
use common\base\models\BaseActivityFormRegistrationForm;
use common\base\models\BaseAdmin;
use common\base\models\BaseAdminJobInvite;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementAreaRelation;
use common\base\models\BaseAnnouncementClickTotalDaily;
use common\base\models\BaseAnnouncementExtra;
use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseArticleClickLog;
use common\base\models\BaseArticleColumn;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyGroup;
use common\base\models\BaseCompanyGroupRelation;
use common\base\models\BaseCompanyGroupScoreSystem;
use common\base\models\BaseCompanyPackageChangeLog;
use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseCompanyPackageSystemConfig;
use common\base\models\BaseCompanyResumeLibrary;
use common\base\models\BaseCompanyStatData;
use common\base\models\BaseDictionary;
use common\base\models\BaseHomePosition;
use common\base\models\BaseHwActivity;
use common\base\models\BaseHwActivityCompany;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobApplyRecordExtra;
use common\base\models\BaseJobClickLog;
use common\base\models\BaseJobColumn;
use common\base\models\BaseJobExtra;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\base\models\BaseMemberActionLog;
use common\base\models\BaseOffSiteJobApply;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAttachment;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeJobFootprint;
use common\base\models\BaseResumeLibraryInviteLog;
use common\base\models\BaseResumeStatData;
use common\base\models\BaseResumeTagRelation;
use common\base\models\BaseResumeWork;
use common\base\models\BaseShowcase;
use common\base\models\BaseUploadForm;
use common\helpers\FileHelper;
use common\helpers\TimeHelper;
use common\helpers\UUIDHelper;
use common\libs\Aliyun\Oss;
use common\libs\Cache;
use common\libs\ColumnAuto\AnnouncementAutoClassify;
use common\libs\Excel;
use common\service\announcement\ClickTotalService;
use common\service\chat\ChatApplication;
use common\service\match\MatchCompleteService;
use common\service\meilisearch\resume\ResumeLibraryDocumentService;
use common\service\resume\ResumeCacheService;
use common\service\stat\StateApplication;
use common\service\stat\Tmp;
use h5\models\HomeColumn;
use queue\MeilisearchJob;
use queue\Producer;
use Yii;
use yii\base\Exception;
use yii\db\conditions\AndCondition;
use yii\db\Expression;

/**
 *
 */
class ScriptController extends BaseTimerController
{

    public function actionSendResumeDate()
    {
        try {
            $app    = StateApplication::getInstance();
            $params = ['complete' => 40];
            $app->resumeList($params);
        } catch (Exception $e) {
            self::log($e->getMessage());
        }
    }

    // php ./timer_yii script/tmp-announcement
    public function actionTmpAnnouncement()
    {
        $list = BaseAnnouncement::find()
            ->select('id')
            ->where(['status' => 1])
            ->orderBy('id desc')
            ->limit(10000)
            ->asArray()
            ->all();

        foreach ($list as $item) {
            $model = new AnnouncementAutoClassify($item['id']);

            $model->run();

            self::log($item['id']);
        }
    }

    // php timer_yii script/tmp
    public function actionTmp()
    {
        try {
            $app = new Tmp();
            $app->getMasterTalentExport();
        } catch (Exception $e) {
            self::log($e->getMessage());
        }
    }

    public function actionUpdateJobFootprint()
    {
        $key = 'SCRIPT:JOB:FOOTPRINT:ID';
        // 找到职位点击日志里面全部含有memberId的
        $value = Cache::get($key);
        $id    = $value ?: 0;
        $list  = BaseJobClickLog::find()
            ->select([
                'id',
                'add_time',
                'member_id',
                'job_id',
            ])
            ->where([
                '>',
                'member_id',
                0,
            ])
            ->andWhere([
                '>',
                'id',
                $id,
            ])
            ->asArray()
            ->limit(10000)
            ->all();

        self::log('开始更新:' . count($list) . '条数据');
        foreach ($list as $k => $item) {
            // 更新的百分比
            $percent = round(($k + 1) / count($list) * 100, 2);
            self::log('更新进度:' . $percent . '%');
            $date     = date('Y-m-d', strtotime($item['add_time']));
            $resumeId = BaseResume::findOneVal([
                'member_id' => $item['member_id'],
            ], 'id');
            if (!$resumeId) {
                self::log('简历不存在:' . $item['member_id']);
                Cache::set($key, $item['id']);
                continue;
            }
            $model = BaseResumeJobFootprint::findOne([
                'resume_id' => $resumeId,
                'date'      => $date,
                'job_id'    => $item['job_id'],
            ]);

            if (!$model) {
                $model            = new BaseResumeJobFootprint();
                $model->resume_id = $resumeId;
                $model->date      = $date;
                $model->last_time = $item['add_time'];
                $model->job_id    = $item['job_id'];
                $model->save();
                self::log('新增成功:' . $item['id'] . '-' . $item['member_id'] . '-' . $item['job_id']);
            } else {
                // 对比一下时间是否比旧的时间大
                $oldTime = strtotime($model->last_time);
                $newTime = strtotime($item['add_time']);
                if ($newTime <= $oldTime) {
                    self::log('时间不对:' . $item['id'] . '-' . $item['member_id'] . '-' . $item['job_id']);
                } else {
                    $model->last_time = $item['add_time'];
                    $model->save();
                    self::log('更新成功:' . $item['id'] . '-' . $item['member_id'] . '-' . $item['job_id']);
                }
                Cache::set($key, $item['id']);
            }
        }
    }

    public function actionUpdateApplyPlatformTwo()
    {
        //获取平台为0的投递记录
        $data = BaseJobApplyRecord::find()
            ->select([
                'id',
                'platform',
                'announcement_id',
                'job_id',
                'resume_id',
                'add_time',
            ])
            ->where(['platform' => 0])
            ->asArray()
            ->all();
        //循环处理
        $i = 1;
        foreach ($data as $item) {
            $resume_info = BaseResume::findOne($item['resume_id']);
            $info        = BaseMemberActionLog::find()
                ->andWhere(['member_id' => $resume_info['member_id']])
                ->andWhere(['is_login' => 1])
                ->andWhere([
                    '<',
                    'add_time',
                    $item['add_time'],
                ])
                ->orderBy('add_time desc')
                ->asArray()
                ->one();
            $model       = BaseJobApplyRecord::findOne($item['id']);
            if ($info) {
                if ($info['platform'] == 'PC') {
                    $model->platform = 1;
                } else {
                    $model->platform = 2;
                }
                $model->save();
                self::log('序号' . $i++ . '简历ID：' . $resume_info['id'] . '投递记录ID：' . $item['id'] . '平台：' . $model->platform);
            }
            echo '执行结束';
        }
    }

    public function actionMiniJobTag()
    {
        //获取所有职位列表
        $key     = 'SCRIPT:MINIJOBTAG:ID';
        $id      = Cache::get($key);
        $jobList = BaseJob::find()
            ->select([
                'id',
            ])
            ->andFilterWhere([
                '>',
                'id',
                $id,
            ])
            ->limit(10000)
            ->orderBy('id')
            ->asArray()
            ->all();
        foreach ($jobList as $k => $item) {
            self::log('开始小程序规则调用职位ID：' . $item['id']);
            //调用小程序职位规则
            $model     = new RuleJob();
            $res       = $model->exec($item['id']);
            $job_model = BaseJob::findOne($item['id']);
            if ($res) {
                $job_model->is_miniapp = BaseJob::IS_MINIAPP_YES;
            } else {
                $job_model->is_miniapp = BaseJob::IS_MINIAPP_NO;
            }
            //写入表
            $job_model->save();
            Cache::set($key, $item['id']);
            self::log('结束小程序规则调用职位ID：' . $item['id'] . '，职位is_miniapp变成：' . $job_model->is_miniapp);
        }
    }

    /***
     * 修复数据-公告是否被小程序调用
     * php timer_yii script/mini-announcement-tag
     */
    public function actionMiniAnnouncementTag()
    {
        $key = 'SCRIPT:MINIANNOUNCEMENTTAG:ID';
        $id  = Cache::get($key);
        //获取所有公告列表
        $announcementList = BaseAnnouncement::find()
            ->select([
                'id',
            ])
            ->andFilterWhere([
                '>',
                'id',
                $id,
            ])
            ->limit(10000)
            ->orderBy('id')
            ->asArray()
            ->all();
        foreach ($announcementList as $k => $item) {
            self::log('开始小程序规则调用公告ID：' . $item['id']);
            //调用小程序公告规则
            $model              = new RuleAnnouncement();
            $res                = $model->exec($item['id']);
            $announcement_model = BaseAnnouncement::findOne($item['id']);
            if ($res) {
                $announcement_model->is_miniapp = BaseAnnouncement::IS_MINIAPP_YES;
            } else {
                $announcement_model->is_miniapp = BaseAnnouncement::IS_MINIAPP_NO;
            }
            //写入表
            $announcement_model->save();
            Cache::set($key, $item['id']);
            self::log('结束小程序规则调用公告ID：' . $item['id'] . '，公告is_miniapp变成：' . $announcement_model->is_miniapp);
        }
    }

    /***
     * 修复表resume_stat_data投递统计字段数据错误问题
     * php timer_yii script/update-apply-total
     */
    public function actionUpdateApplyTotal()
    {
        //获取平台为0的投递记录
        $key = Cache::get('update_apply_total') ?: 0;
        //查询简历
        $data = BaseResume::find()
            ->select([
                'id',
                'member_id',
            ])
            ->andWhere([
                '>',
                'id',
                $key,
            ])
            ->andWhere(['status' => 1])
            ->orderBy('id asc')
            ->asArray()
            ->all();
        //循环处理
        foreach ($data as $item) {
            $model = BaseResumeStatData::findOne($item['id']);
            if ($model) {
                $model->on_site_apply_amount  = BaseJobApplyRecord::find()
                    ->where([
                        'resume_id'     => $item['id'],
                        'delivery_type' => 2,
                    ])
                    ->count();
                $model->off_site_apply_amount = BaseJobApplyRecord::find()
                    ->where([
                        'resume_id'     => $item['id'],
                        'delivery_type' => 1,
                        'delivery_way'  => [
                            1,
                            2,
                            3,
                        ],
                    ])
                    ->count();
                $model->save();
                self::log('简历ID：' . $item['id'] . '站内投递总数：' . $model->on_site_apply_amount . ';站外投递总数：' . $model->off_site_apply_amount);
                Cache::set('update_apply_total', $item['id']);
            }
        }
    }

    /**
     * 修复历史数据-投递总表的匹配度字段
     * php timer_yii script/update-apply-record-match
     */
    public function actionUpdateApplyRecordMatch()
    {
        $key    = 'UpdateApplyRecordMatch';
        $id     = Cache::get($key) ?: 0;
        $limit  = 10000;
        $data   = BaseJobApplyRecord::find()
            ->select([
                'id',
                'job_id',
                'resume_id',
                'match_complete',
            ])
            ->andWhere(['match_complete' => 0])
            ->andWhere([
                '>',
                'id',
                $id,
            ])
            ->andWhere([
                '>',
                'job_id',
                0,
            ])
            ->limit($limit)
            ->asArray()
            ->all();
        $lastId = 0;
        foreach ($data as $item) {
            $service = new MatchCompleteService();
            $init    = [
                'job_id'    => $item['job_id'],
                'resume_id' => $item['resume_id'],
            ];

            $service_result = $service->setRuleKey()
                ->setProject(MatchCompleteService::PROJECT_TYPE_2)
                ->init($init)
                ->run();
            if (isset($service_result['rule_id']) && $service_result['rule_id'] > 0) {
                $model = BaseJobApplyRecord::findOne($item['id']);
                if ($model) {
                    $model->match_complete = $service_result['rule_id'];
                    $model->save();
                    self::log('投递记录ID：' . $item['id'] . '匹配度：' . $service_result['rule_id']);
                }
            }

            $lastId = $item['id'];
        }

        Cache::set($key, $lastId);
    }

    /**
     * 同步人才uuid数据
     * php timer_yii script/update-resume-uuid
     */
    public function actionUpdateResumeUuid()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $resumeList = BaseResume::find()
                ->select([
                    'id',
                ])
                ->where([
                    'uuid' => '',
                ])
                ->orderBy('add_time asc,id asc')
                ->limit('1000')
                ->asArray()
                ->all();
            $count      = count($resumeList);
            self::log("数据共{$count}条\n");

            foreach ($resumeList as $item) {
                $model = BaseResume::findOne($item['id']);
                self::log("开始处理人才id:{$model['id']}\n");

                $model->uuid = UUIDHelper::encrypt(UUIDHelper::TYPE_PERSON, $item['id']);

                if (!$model->save()) {
                    throw new Exception($model->getFirstErrorsMessage());
                }
                self::log("更新人才Id:{$model['id']}成功\n");
            }

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            var_dump($e->getMessage());
        }
    }

    /**
     * 同步单位uuid数据
     * php timer_yii script/update-company-uuid
     */
    public function actionUpdateCompanyUuid()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $resumeList = BaseCompany::find()
                ->select([
                    'id',
                ])
                ->where([
                    'uuid' => '',
                ])
                ->orderBy('add_time asc,id asc')
                ->limit('1000')
                ->asArray()
                ->all();
            $count      = count($resumeList);
            self::log("数据共{$count}条\n");

            foreach ($resumeList as $item) {
                $model = BaseCompany::findOne($item['id']);
                self::log("开始处理人才id:{$model['id']}\n");

                $model->uuid = UUIDHelper::encrypt(UUIDHelper::TYPE_COMPANY, $item['id']);
                if (!$model->save()) {
                    throw new Exception($model->getFirstErrorsMessage());
                }
                self::log("更新人才Id:{$model['id']}成功\n");
            }

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            var_dump($e->getMessage());
        }
    }

    /**
     * 同步职位uuid数据
     * php timer_yii script/update-job-uuid
     */
    public function actionUpdateJobUuid()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $resumeList = BaseJob::find()
                ->select([
                    'id',
                ])
                ->where([
                    'uuid' => '',
                ])
                ->orderBy('add_time asc,id asc')
                ->limit('1000')
                ->asArray()
                ->all();
            $count      = count($resumeList);
            self::log("数据共{$count}条\n");

            foreach ($resumeList as $item) {
                $model = BaseJob::findOne($item['id']);
                self::log("开始处理人才id:{$model['id']}\n");

                $model->uuid = UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $item['id']);
                if (!$model->save()) {
                    throw new Exception($model->getFirstErrorsMessage());
                }
                self::log("更新人才Id:{$model['id']}成功\n");
            }

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            var_dump($e->getMessage());
        }
    }

    /**
     * 同步公告uuid数据
     * php timer_yii script/update-announcement-uuid
     */
    public function actionUpdateAnnouncementUuid()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $resumeList = BaseAnnouncement::find()
                ->select([
                    'id',
                ])
                ->where([
                    'uuid' => '',
                ])
                ->orderBy('add_time asc,id asc')
                ->limit('1000')
                ->asArray()
                ->all();
            $count      = count($resumeList);
            self::log("数据共{$count}条\n");

            foreach ($resumeList as $item) {
                $model = BaseAnnouncement::findOne($item['id']);
                self::log("开始处理人才id:{$model['id']}\n");

                $model->uuid = UUIDHelper::encrypt(UUIDHelper::TYPE_ANNOUNCEMENT, $item['id']);
                if (!$model->save()) {
                    throw new Exception($model->getFirstErrorsMessage());
                }
                self::log("更新人才Id:{$model['id']}成功\n");
            }
            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            var_dump($e->getMessage());
        }
    }

    // 删除超过 8 份的附件简历
    // php timer_yii script/delete-resume-att
    public function actionDeleteResumeAtt()
    {
        // select count(*) as total, resume_id
        // from resume_attachment
        // where status = 1
        // group by resume_id
        // having total > 8
        $list = BaseResumeAttachment::find()
            ->select([
                'count(*) as total',
                'resume_id',
            ])
            ->where([
                'status' => BaseResumeAttachment::STATUS_ACTIVE,
            ])
            ->groupBy('resume_id')
            ->having([
                '>',
                'total',
                8,
            ])
            ->asArray()
            ->all();

        // 循环
        foreach ($list as $item) {
            // 找到超过 8 份的附件简历，按照时间倒叙排列，删除前 8 份以外的
            $list2 = BaseResumeAttachment::find()
                ->where([
                    'status'    => BaseResumeAttachment::STATUS_ACTIVE,
                    'resume_id' => $item['resume_id'],
                ])
                ->orderBy('add_time desc')
                ->offset(8)
                ->asArray()
                ->all();

            // 循环删除
            foreach ($list2 as $item2) {
                self::log('删除附件简历ID:' . $item2['id'] . '，文件名:' . $item2['file_name'] . '，文件地址:' . $item2['file_url']);
                $model         = BaseResumeAttachment::findOne($item2['id']);
                $model->status = BaseResumeAttachment::STATUS_DELETE;
                $model->save();
            }
        }
    }

    // 删除已经被用户删除了并且没有投递过的简历的文件，占据太多空间
    // php timer_yii script/clean-up-invalid-resumes
    public function actionCleanUpInvalidResumes()
    {
        // 首先找到所有被用户删除的附件简历
        $list = BaseResumeAttachment::find()
            ->where([
                'status' => BaseResumeAttachment::STATUS_DELETE,
            ])
            ->andWhere([
                '>',
                'resume_id',
                0,
            ])
            ->asArray()
            ->all();

        // 循环去找投递
        $totalSize = 0;
        foreach ($list as $item) {
            $isApplyOnline  = BaseJobApply::find()
                ->where([
                    'resume_attachment_id' => $item['id'],
                ])
                ->exists();
            $isApplyOffline = BaseOffSiteJobApply::find()
                ->where([
                    'resume_attachment_id' => $item['id'],
                ])
                ->exists();
            // 两个都没有，输出链接
            if (!$isApplyOnline && !$isApplyOffline) {
                $realPath = Yii::getAlias('@resume') . '/' . $item['file_url'];
                if (!is_file($realPath)) {
                    self::log('文件不存在:' . $realPath);
                    continue;
                }

                // 读取文件大小
                $size      = filesize($realPath);
                $totalSize += $size;
                // 转成 m 方便看
                $mTotalSize = round($totalSize / 1024 / 1024, 2);
                // 输出总大小，k/mb/g
                self::log('文件地址:' . $realPath . '，文件大小:' . $size . '，总大小:' . $mTotalSize);

                // 判断在oss有
                $oss = new Oss();
                if ($oss->isResumeAttachmentExist($item['file_url'])) {
                    unlink($realPath);
                    // 如果$item['file_url']是doc或者docx，就找到对于的pdf
                    $pathInfo = pathinfo($item['file_url']);
                    if (in_array($pathInfo['extension'], [
                        'doc',
                        'docx',
                    ])) {
                        $pdfPath = str_replace('.' . $pathInfo['extension'], '.pdf', $realPath);
                        if (is_file($pdfPath)) {
                            $pdfSize    = filesize($pdfPath);
                            $totalSize  += $pdfSize;
                            $mTotalSize = round($totalSize / 1024 / 1024, 2);
                            self::log('文件地址:' . $pdfPath . '，文件大小:' . $pdfSize . '，总大小:' . $mTotalSize);
                        }
                    }
                    unlink($pdfPath);
                } else {
                    self::log('oss不存在:' . $item['file_url']);
                }
                // self::log('删除附件简历ID:' . $item['id'] . '，文件名:' . $item['file_name'] . '，文件地址:' . $item['file_url']);
            }
        }
    }

    /**
     * 同步用户活动表单场次签到信息
     * php timer_yii script/update-activity-form-option-sign
     */
    public function actionUpdateActivityFormOptionSign()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $list = BaseActivityFormRegistrationForm::find()
                ->select([
                    'id',
                    'add_time',
                    'option_ids',
                    'activity_form_id',
                    'resume_id',
                ])
                ->where([
                    'status' => BaseActiveRecord::STATUS_ACTIVE,
                ])
                ->orderBy('add_time asc,id asc')
                ->asArray()
                ->all();

            foreach ($list as $item) {
                self::log("开始报名表单id:{$item['id']}\n");

                $optionIds = explode(',', $item['option_ids']);
                foreach ($optionIds as $id) {
                    $signModel = BaseActivityFormOptionSign::findOne([
                        'option_id' => $id,
                        'resume_id' => $item['resume_id'],
                    ]);
                    if (!$signModel && $id > 0 && $item['resume_id'] > 0) {
                        $serialNumber = BaseActivityFormOptionSign::find()
                                            ->select([
                                                'serial_number',
                                            ])
                                            ->where([
                                                'option_id' => $id,
                                            ])
                                            ->orderBy('serial_number desc')
                                            ->asArray()
                                            ->one()['serial_number'] + 1;
                        $signData     = [
                            'activityFormId'     => $item['activity_form_id'],
                            'optionId'           => $id,
                            'registrationFormId' => $item['id'],
                            'resumeId'           => $item['resume_id'],
                            'serialNumber'       => $serialNumber,
                            'signTime'           => TimeHelper::ZERO_TIME,
                            'addTime'            => $item['add_time'],
                        ];
                        BaseActivityFormOptionSign::setNewActivityFormOptionSign($signData);
                    }
                }
                self::log("更新报名表单Id:{$item['id']}成功\n");
            }
            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            var_dump($e->getMessage());
        }
    }

    /**
     * 同步活动表单场次签到链接和二维码
     * php timer_yii script/update-activity-form-intention-option-sign
     */
    public function actionUpdateActivityFormIntentionOptionSign()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $list = BaseActivityFormIntentionOption::find()
                ->alias('o')
                ->leftJoin(['f' => BaseActivityForm::tableName()], 'f.id = o.activity_form_id')
                ->select([
                    'o.id',
                ])
                ->where([
                    'f.statuus'  => BaseActiveRecord::STATUS_ACTIVE,
                    'o.sign_s'   => BaseActiveRecord::STATUS_ACTIVE,
                    'o.sign_url' => '',
                ])
                ->asArray()
                ->all();

            foreach ($list as $item) {
                self::log("开始处理活动选项id:{$item['id']}\n");
                $model                = BaseActivityFormIntentionOption::findOne($item['id']);
                $uploadModel          = new BaseUploadForm();
                $codeData             = $uploadModel->getActivityFormOptionSign($item['id']);
                $model->sign_url      = $codeData['signUrl'];
                $model->sign_code_url = FileHelper::getFullUrl($codeData['path']);
                if (!$model->save()) {
                    throw new Exception($model->getFirstErrorsMessage());
                }
                self::log("更新活动选项Id:{$item['id']}成功\n");
            }
            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            var_dump($e->getMessage());
        }
    }

    /***
     * 将栏目530下的公告同步一下职位去到530下
     * php timer_yii script/update-announcement-job-column
     */
    public function actionUpdateAnnouncementJobColumn()
    {
        //获取530下的公告ID
        $announcementIds = BaseArticleColumn::find()
            ->alias('c')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.article_id=c.article_id')
            ->select(['a.id'])
            ->where(['c.column_id' => 530])
            ->column();
        //去除重复与空值
        $announcementIds = array_unique(array_filter($announcementIds));
        //循环处理推入队列
        foreach ($announcementIds as $item) {
            //推入队列
            Producer::afterAnnouncementUpdateJob($item);
        }
    }

    /***
     * 处理277栏目下的公告
     * php timer_yii script/update-announcement-column
     */
    public function actionUpdateAnnouncementColumn()
    {
        //获取单位类型为1，2，3，4的单位ID
        $companyIds = BaseCompany::find()
            ->select(['id'])
            ->where([
                'type' => [
                    1,
                    2,
                    3,
                    4,
                ],
            ])
            ->column();
        //获取$companyIds下的公告ID
        $announcementIds = BaseAnnouncement::find()
            ->alias('a')
            ->leftJoin(['ar' => BaseArticle::tableName()], 'a.article_id=ar.id')
            ->select(['id'])
            ->where([
                'company_id' => $companyIds,
                'ar.status'  => [
                    1,
                    2,
                ],
            ])
            ->column();
        //循环处理推入队列
        foreach ($announcementIds as $item) {
            //推入队列
            Producer::afterAnnouncementUpdateJob($item);
        }
    }

    /**
     * 处理历史投递数据去到统计表
     * php timer_yii script/update-job-apply-count
     */
    public function actionUpdateJobApplyCount()
    {
        //获取最大的ID
        //获取投递数据进行处理
        $key = Cache::get('update_job_apply_count2');
        if ($key == 1) {
            exit('已经处理完毕');
        }
        if ($key <= 0) {
            $max_id = BaseJobApplyRecord::find()
                ->asArray()
                ->max('id');
            Cache::set('update_job_apply_count2', $max_id);
            $key = $max_id;
        }
        $data = BaseJobApplyRecord::find()
            ->select([
                'id',
                'job_id',
                'resume_id',
                'platform',
            ])
            ->andWhere([
                'delivery_way' => [
                    1,
                    2,
                    3,
                ],
            ])
            ->andWhere([
                '<',
                'id',
                $key,
            ])
            ->orderBy('id desc')
            ->limit(5000)
            ->asArray()
            ->all();

        foreach ($data as $item) {
            self::log('开始处理ID：' . $item['id']);
            BaseJobApplyRecordExtra::JobApplyCount($item['job_id'], $item['resume_id'], $item['platform']);
            Cache::set('update_job_apply_count2', $item['id']);
            self::log('处理ID：' . $item['id'] . '成功');
        }
        self::log('结束');
    }

    /**
     * 处理历史投递数据去到统计表
     * php timer_yii script/update-job-apply-interview
     */
    public function actionUpdateJobApplyInterview()
    {
        //获取最大的ID
        //获取投递数据进行处理
        $key = Cache::get('update_job_apply_interview2');
        if ($key == 1) {
            exit('已经处理完毕');
        }
        if ($key <= 0) {
            $max_id = BaseJobApply::find()
                ->asArray()
                ->max('id');
            Cache::set('update_job_apply_interview2', $max_id);
            $key = $max_id;
        }
        $data = BaseJobApply::find()
            ->select([
                'is_invitation',
                'job_id',
                'id',
            ])
            ->andWhere([
                '<',
                'id',
                $key,
            ])
            ->orderBy('id desc')
            ->limit(100000)
            ->asArray()
            ->all();

        foreach ($data as $item) {
            if ($item['is_invitation'] > 0) {
                self::log('开始处理ID：' . $item['id']);
                BaseJobApplyRecordExtra::jobApplyUpdateInterview($item['job_id']);
                self::log('处理ID：' . $item['id'] . '成功');
            }
            Cache::set('update_job_apply_interview2', $item['id']);
        }
        self::log('结束');
    }


    // 删除6个月没有登录的用户，并且没有投递过的简历
    // php timer_yii script/clean-up-invalid
    public function actionCleanUpInvalid()
    {
        // 首先找到所有被用户删除的附件简历
        $resumeList = BaseMember::find()
            ->alias('a')
            ->innerJoin(['b' => BaseResume::tableName()], 'a.id=b.member_id')
            ->select([
                'b.id',
                'last_active_time',
            ])
            ->andWhere([
                '<',
                'last_active_time',
                date('Y-m-d H:i:s', strtotime('-6 month')),
            ])
            ->andWhere([
                'a.type' => 1,
            ])
            ->asArray()
            ->all();

        $totalSize = 0;
        self::log('一共' . count($resumeList) . '个求职者');
        foreach ($resumeList as $resume) {
            self::log('开始处理求职者:' . $resume['id'] . '最近活跃时间' . $resume['last_active_time']);
            // 找到全部附件材料
            $list = BaseResumeAttachment::find()
                ->where([
                    'resume_id' => $resume['id'],
                ])
                ->asArray()
                ->all();
            foreach ($list as $item) {
                $isApplyOnline  = BaseJobApply::find()
                    ->where([
                        'resume_attachment_id' => $item['id'],
                    ])
                    ->exists();
                $isApplyOffline = BaseOffSiteJobApply::find()
                    ->where([
                        'resume_attachment_id' => $item['id'],
                    ])
                    ->exists();
                // 两个都没有，输出链接
                if (!$isApplyOnline && !$isApplyOffline) {
                    $realPath = Yii::getAlias('@resume') . '/' . $item['file_url'];
                    if (!is_file($realPath)) {
                        self::log('文件不存在:' . $realPath);
                        continue;
                    }

                    // 读取文件大小
                    $size      = filesize($realPath);
                    $totalSize += $size;
                    // 转成 m 方便看
                    $mTotalSize = round($totalSize / 1024 / 1024, 2);
                    // 输出总大小，k/mb/g
                    self::log('文件地址:' . $realPath . '，文件大小:' . $size . '，总大小:' . $mTotalSize);

                    // 判断在oss有
                    $oss = new Oss();
                    if ($oss->isResumeAttachmentExist($item['file_url'])) {
                        unlink($realPath);
                        // 如果$item['file_url']是doc或者docx，就找到对于的pdf
                        $pathInfo = pathinfo($item['file_url']);
                        if (in_array($pathInfo['extension'], [
                            'doc',
                            'docx',
                        ])) {
                            $pdfPath = str_replace('.' . $pathInfo['extension'], '.pdf', $realPath);
                            if (is_file($pdfPath)) {
                                $pdfSize    = filesize($pdfPath);
                                $totalSize  += $pdfSize;
                                $mTotalSize = round($totalSize / 1024 / 1024, 2);
                                self::log('文件地址:' . $pdfPath . '，文件大小:' . $pdfSize . '，总大小:' . $mTotalSize);
                            }
                        }

                        unlink($pdfPath);

                        // 把文件也设置为删除
                        $attModel         = BaseResumeAttachment::findOne($item['id']);
                        $attModel->status = BaseResumeAttachment::STATUS_DELETE;
                        $attModel->save();
                    } else {
                        self::log('oss不存在:' . $item['file_url']);
                    }
                } else {
                    self::log('有投递记录:' . $item['id'] . '-' . $item['file_url']);
                }
            }
        }
    }

    /**
     * 更新company_package_config表字段cycle_resume_download_amount、cycle_chat_amount
     * php timer_yii script/update-company-package-filed
     */
    public function actionUpdateCompanyPackageFiled()
    {
        //获取company_package_config表resume_download_amount、chat_amount字段大于0的数据且有效时间大于当前时间
        $list = BaseCompanyPackageConfig::find()
            ->select([
                'id',
                'company_id',
                'resume_download_amount',
                'chat_amount',
                'expire_time',
                'effect_time',
                'cycle_resume_download_amount',
                'cycle_chat_amount',
            ])
            ->andWhere([
                'or',
                [
                    '>',
                    'resume_download_amount',
                    0,
                ],
                [
                    '>',
                    'chat_amount',
                    0,
                ],
            ])
            ->andWhere([
                '>',
                'expire_time',
                date('Y-m-d H:i:s'),
            ])
            //            ->andWhere(['company_id' => 48742])
            ->asArray()
            ->all();

        //循环
        foreach ($list as $item) {
            //开始日志
            self::log('开始处理公司ID：' . $item['company_id']);
            //记录一下原始数据
            self::log('原始数据：' . json_encode($item));
            //获取增加日志-简历下载次数
            $resumeDownloadAmount = BaseCompanyPackageChangeLog::find()
                ->where([
                    'company_id' => $item['company_id'],
                    'type'       => 9,
                    'identify'   => 1,
                ])
                ->andWhere([
                    '>',
                    'add_time',
                    $item['effect_time'],
                ])
                ->sum('change_amount');
            //获取增加日志-聊天次数
            $chatAmount = BaseCompanyPackageChangeLog::find()
                ->where([
                    'company_id' => $item['company_id'],
                    'type'       => 12,
                    'identify'   => 1,
                ])
                ->andWhere([
                    '>',
                    'add_time',
                    $item['effect_time'],
                ])
                ->sum('change_amount');
            //更新company_package_config表字段cycle_resume_download_amount、cycle_chat_amount
            $model                               = BaseCompanyPackageConfig::findOne($item['id']);
            $model->cycle_resume_download_amount = $resumeDownloadAmount;
            $model->cycle_chat_amount            = $chatAmount;
            $model->save();
            //结束日志
            self::log('结束处理公司ID：' . $item['company_id']);
        }
    }

    /**
     * @throws Exception
     * php timer_yii script/tmp-announcement-click
     */
    public function actionTmpAnnouncementClick()
    {
        //        TMP:CLICK:ARTICLE
        //将队列中的数据取出来
        $key = 'TMP:CLICK:ARTICLE';
        // 循环1w次
        for ($i = 0; $i < 100000; $i++) {
            self::log('开始处理,第' . $i . '次');
            $data = Cache::rPop($key);
            if (empty($data)) {
                break;
            }
            self::log('数据：' . $data);
            $data    = json_decode($data, true);
            $service = new ClickTotalService();
            $service->initData($data)
                ->run();

            self::log('结束处理,第' . $i . '次');
            self::log('处理百分比' . ($i / 100000 * 100) . '%');
        }

        // $data = Cache::rPop($key);
        // while (!empty($data)) {
        //     $data    = json_decode($data, true);
        //     $service = new ClickTotalService();
        //     $service->initData($data)
        //         ->run();
        //     $data = Cache::rPop($key);
        // }
    }

    /**
     * 更新职位投递热度类型
     * php timer_yii script/update-job-apply-heat
     */
    public function actionUpdateJobApplyHeat()
    {
        $key = 'SCRIPT:JOB:APPLY_HEAT:ID';

        $id     = Cache::get($key) ?: 0;
        $limit  = 10000;
        $data   = BaseJob::find()
            ->select([
                'id',
            ])
            ->where(['apply_heat_type' => 0])
            ->andWhere([
                '>',
                'id',
                $id,
            ])
            ->limit($limit)
            ->asArray()
            ->all();
        $lastId = 0;
        $total  = count($data);
        self::log('开始更新:' . $total . '条数据');
        foreach ($data as $k => $item) {
            $applyHeatType = BaseJob::getApplyHeatType($item['id']);
            if (!empty($applyHeatType)) {
                $jobModel                  = BaseJob::findOne($item['id']);
                $jobModel->apply_heat_type = $applyHeatType;
                $jobModel->save();
                self::log('职位ID：' . $item['id'] . '，更新投递热度类型为：' . $applyHeatType . '百分比为' . ($k / $total * 100) . '%');
            }

            $lastId = $item['id'];
        }

        Cache::set($key, $lastId);
    }

    /**
     * 更新公告的热度类型
     * php timer_yii script/update-announcement-heat
     */
    public function actionUpdateAnnouncementHeat()
    {
        $key    = 'SCRIPT:ANNOUNCEMENT:HEAT:ID';
        $id     = Cache::get($key) ?: 0;
        $limit  = 10000;
        $data   = BaseAnnouncement::find()
            ->select([
                'id',
            ])
            ->where(['announcement_heat_type' => 0])
            ->andWhere([
                '>',
                'id',
                $id,
            ])
            ->limit($limit)
            ->asArray()
            ->all();
        $lastId = 0;
        $total  = count($data);
        self::log('开始更新:' . $total . '条数据');
        foreach ($data as $k => $item) {
            $heatType = BaseAnnouncement::getHeatType($item['id']);
            if (!empty($heatType)) {
                $announcementModel                         = BaseAnnouncement::findOne($item['id']);
                $announcementModel->announcement_heat_type = $heatType;
                $announcementModel->save();
                self::log('公告ID：' . $item['id'] . '，更新热度类型为：' . $heatType . '百分比为' . ($k / $total * 100) . '%');
            }

            $lastId = $item['id'];
        }

        Cache::set($key, $lastId);
    }

    /**
     * 更新公告的编制类型
     * php timer_yii script/update-announcement-establishment-type
     */
    public function actionUpdateAnnouncementEstablishmentType()
    {
        $key = 'SCRIPT:ANNOUNCEMENT:ESTABLISHMENT:ID';

        $id     = Cache::get($key) ?: 0;
        $limit  = 10000;
        $data   = BaseAnnouncement::find()
            ->select([
                'id',
            ])
            ->where(['establishment_type' => 0])
            ->andWhere([
                '>',
                'id',
                $id,
            ])
            ->limit($limit)
            ->asArray()
            ->all();
        $lastId = 0;
        $total  = count($data);
        self::log('开始更新:' . $total . '条数据');
        foreach ($data as $k => $item) {
            self::log('开始执行公告ID：' . $item['id']);
            $establishmentType = BaseAnnouncement::getEstablishmentType($item['id']);
            if (!empty($establishmentType)) {
                $announcementModel                     = BaseAnnouncement::findOne($item['id']);
                $announcementModel->establishment_type = $establishmentType;
                $announcementModel->save();
                self::log('公告ID：' . $item['id'] . '，更新编制类型类型为：' . $establishmentType . '百分比为' . ($k / $total * 100) . '%');
            }

            $lastId = $item['id'];
        }

        Cache::set($key, $lastId);
    }

    /**
     * 更新菜单数据
     * 执行 menu.sql 脚本中的 SQL 语句，支持动态ID分配
     * php timer_yii script/update-menu
     */
    public function actionUpdateMenu()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            self::log('开始执行菜单更新脚本');

            // 执行菜单插入和更新操作
            $this->executeMenuOperations();

            $transaction->commit();
            self::log('菜单更新脚本执行完成');
        } catch (Exception $e) {
            $transaction->rollBack();
            self::log('菜单更新脚本执行失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 执行菜单操作，支持动态ID分配
     */
    private function executeMenuOperations()
    {
        // 存储新插入记录的ID映射
        $idMapping = [];

        // 1. 执行插入操作
        self::log('开始执行菜单插入操作');
        $insertedIds = $this->executeMenuInserts();

        // 建立ID映射关系
        $idMapping = [
            'content_publish'        => $insertedIds['content_publish'] ?? null,
            'announcement_add'       => $insertedIds['announcement_add'] ?? null,
            'job_add'                => $insertedIds['job_add'] ?? null,
            'job_audit'              => $insertedIds['job_audit'] ?? null,
            'advertising_management' => $insertedIds['advertising_management'] ?? null,
            'news_publish'           => $insertedIds['news_publish'] ?? null,
        ];

        self::log('ID映射关系: ' . json_encode($idMapping));

        // 2. 执行更新操作
        self::log('开始执行菜单更新操作');
        $this->executeMenuUpdates($idMapping);

        self::log('菜单操作执行完成');
    }

    /**
     * 执行菜单插入操作
     *
     * @return array 返回插入的ID映射
     */
    private function executeMenuInserts(): array
    {
        $insertedIds = [];

        // 插入"内容发布"菜单
        $sql = "INSERT INTO `admin_menu` (`add_time`, `update_time`, `status`, `name`, `parent_id`, `level`, `key`, `is_route`)
                VALUES ('2025-05-13 09:00:44', '0000-00-00 00:00:00', 1, '内容发布', 0, 1, 'add', 0)";

        $result                         = Yii::$app->db->createCommand($sql)
            ->execute();
        $contentPublishId               = Yii::$app->db->getLastInsertID();
        $insertedIds['content_publish'] = $contentPublishId;
        self::log("插入'内容发布'菜单，ID: {$contentPublishId}");

        // 插入"新增公告"菜单
        $sql = "INSERT INTO `admin_menu` (`add_time`, `update_time`, `status`, `name`, `parent_id`, `level`, `key`, `is_route`)
                VALUES ('2025-05-13 09:04:11', '0000-00-00 00:00:00', 1, '新增公告', {$contentPublishId}, 2, 'announcementAdd', 0)";

        $result                          = Yii::$app->db->createCommand($sql)
            ->execute();
        $announcementAddId               = Yii::$app->db->getLastInsertID();
        $insertedIds['announcement_add'] = $announcementAddId;
        self::log("插入'新增公告'菜单，ID: {$announcementAddId}");

        // 插入"新增职位"菜单
        $sql = "INSERT INTO `admin_menu` (`add_time`, `update_time`, `status`, `name`, `parent_id`, `level`, `key`, `is_route`)
                VALUES ('2025-05-13 09:04:32', '0000-00-00 00:00:00', 1, '新增职位', {$contentPublishId}, 2, 'jobAdd', 0)";

        $result                 = Yii::$app->db->createCommand($sql)
            ->execute();
        $jobAddId               = Yii::$app->db->getLastInsertID();
        $insertedIds['job_add'] = $jobAddId;
        self::log("插入'新增职位'菜单，ID: {$jobAddId}");

        // 插入"职位审核"菜单
        $sql = "INSERT INTO `admin_menu` (`add_time`, `update_time`, `status`, `name`, `parent_id`, `level`, `key`, `is_route`)
                VALUES ('2025-05-13 09:12:34', '0000-00-00 00:00:00', 1, '职位审核', 26, 2, 'cmsJobAuditList', 0)";

        $result                   = Yii::$app->db->createCommand($sql)
            ->execute();
        $jobAuditId               = Yii::$app->db->getLastInsertID();
        $insertedIds['job_audit'] = $jobAuditId;
        self::log("插入'职位审核'菜单，ID: {$jobAuditId}");

        // 插入"广告管理"菜单
        $sql = "INSERT INTO `admin_menu` (`add_time`, `update_time`, `status`, `name`, `parent_id`, `level`, `key`, `is_route`)
                VALUES ('2025-05-13 09:19:02', '0000-00-00 00:00:00', 1, '广告管理', 0, 1, 'advertising', 0)";

        $result                                = Yii::$app->db->createCommand($sql)
            ->execute();
        $advertisingId                         = Yii::$app->db->getLastInsertID();
        $insertedIds['advertising_management'] = $advertisingId;
        self::log("插入'广告管理'菜单，ID: {$advertisingId}");

        // 插入"发布资讯"菜单
        $sql = "INSERT INTO `admin_menu` (`add_time`, `update_time`, `status`, `name`, `parent_id`, `level`, `key`, `is_route`)
                VALUES ('2025-05-13 09:48:13', '0000-00-00 00:00:00', 1, '发布资讯', 131, 2, 'addNews', 0)";

        $result                      = Yii::$app->db->createCommand($sql)
            ->execute();
        $newsPublishId               = Yii::$app->db->getLastInsertID();
        $insertedIds['news_publish'] = $newsPublishId;
        self::log("插入'发布资讯'菜单，ID: {$newsPublishId}");

        return $insertedIds;
    }

    /**
     * 执行菜单更新操作
     *
     * @param array $idMapping ID映射关系
     */
    private function executeMenuUpdates(array $idMapping)
    {
        // 获取新插入的广告管理菜单ID
        $advertisingId = $idMapping['advertising_management'];

        // 更新操作数组
        $updateOperations = [
            [
                'description' => '更新栏目管理菜单(ID:39)',
                'sql'         => "UPDATE `admin_menu` SET `add_time` = '2021-11-11 14:42:12', `update_time` = '0000-00-00 00:00:00', `status` = 1, `name` = '栏目管理', `parent_id` = 45, `level` = 2, `key` = 'cmsColumnList', `is_route` = 0 WHERE `id` = 39",
            ],
            [
                'description' => '更新栏目管理菜单(ID:41)',
                'sql'         => "UPDATE `admin_menu` SET `add_time` = '2021-11-12 22:09:38', `update_time` = '0000-00-00 00:00:00', `status` = 1, `name` = '栏目管理', `parent_id` = 45, `level` = 2, `key` = 'column', `is_route` = 0 WHERE `id` = 41",
            ],
            [
                'description' => '更新公告查询菜单(ID:27)',
                'sql'         => "UPDATE `admin_menu` SET `add_time` = '2021-10-27 19:44:12', `update_time` = '2021-10-27 19:44:12', `status` = 1, `name` = '公告查询', `parent_id` = 26, `level` = 2, `key` = 'cmsAnnouncementList', `is_route` = 0 WHERE `id` = 27",
            ],
            [
                'description' => '更新职位管理菜单(ID:60)',
                'sql'         => "UPDATE `admin_menu` SET `add_time` = '2021-12-21 09:48:12', `update_time` = '0000-00-00 00:00:00', `status` = 1, `name` = '职位管理', `parent_id` = 26, `level` = 2, `key` = 'cmsJobList', `is_route` = 0 WHERE `id` = 60",
            ],
            [
                'description' => '更新广告查询菜单(ID:43)',
                'sql'         => "UPDATE `admin_menu` SET `add_time` = '2021-11-15 09:51:22', `update_time` = '0000-00-00 00:00:00', `status` = 1, `name` = '广告查询', `parent_id` = {$advertisingId}, `level` = 2, `key` = 'cmsAdvertisingList', `is_route` = 0 WHERE `id` = 43",
            ],
            [
                'description' => '更新广告位管理菜单(ID:44)',
                'sql'         => "UPDATE `admin_menu` SET `add_time` = '2021-11-15 17:29:12', `update_time` = '0000-00-00 00:00:00', `status` = 1, `name` = '广告位管理', `parent_id` = {$advertisingId}, `level` = 2, `key` = 'cmsAdvertisingPositionList', `is_route` = 0 WHERE `id` = 44",
            ],
            [
                'description' => '更新小程序广告管理菜单(ID:86)',
                'sql'         => "UPDATE `admin_menu` SET `add_time` = '2023-07-04 18:03:08', `update_time` = '0000-00-00 00:00:00', `status` = 1, `name` = '小程序广告管理', `parent_id` = {$advertisingId}, `level` = 2, `key` = 'cmsMiniAdvertisingList', `is_route` = 0 WHERE `id` = 86",
            ],
            [
                'description' => '更新高才海外广告管理菜单(ID:110)',
                'sql'         => "UPDATE `admin_menu` SET `add_time` = '2024-07-30 16:30:06', `update_time` = '0000-00-00 00:00:00', `status` = 1, `name` = '高才海外广告管理', `parent_id` = {$advertisingId}, `level` = 2, `key` = 'abroadAdvertising', `is_route` = 0 WHERE `id` = 110",
            ],
            [
                'description' => '更新高才博士后广告管理菜单(ID:116)',
                'sql'         => "UPDATE `admin_menu` SET `add_time` = '2024-11-11 18:41:23', `update_time` = '0000-00-00 00:00:00', `status` = 1, `name` = '高才博士后广告管理', `parent_id` = {$advertisingId}, `level` = 2, `key` = 'cmsBoShiHouList', `is_route` = 0 WHERE `id` = 116",
            ],
            [
                'description' => '更新活动管理菜单(ID:108)',
                'sql'         => "UPDATE `admin_menu` SET `add_time` = '2024-07-30 16:29:41', `update_time` = '0000-00-00 00:00:00', `status` = 1, `name` = '活动管理', `parent_id` = 0, `level` = 1, `key` = 'abroad', `is_route` = 0 WHERE `id` = 108",
            ],
            [
                'description' => '更新活动查询菜单(ID:109)',
                'sql'         => "UPDATE `admin_menu` SET `add_time` = '2024-07-30 16:29:54', `update_time` = '0000-00-00 00:00:00', `status` = 1, `name` = '活动查询', `parent_id` = 108, `level` = 2, `key` = 'abroadActivity', `is_route` = 0 WHERE `id` = 109",
            ],
            [
                'description' => '更新专场查询菜单(ID:118)',
                'sql'         => "UPDATE `admin_menu` SET `add_time` = '2025-03-25 18:43:15', `update_time` = '0000-00-00 00:00:00', `status` = 1, `name` = '专场查询', `parent_id` = 108, `level` = 2, `key` = 'specialActivity', `is_route` = 0 WHERE `id` = 118",
            ],
            [
                'description' => '更新资讯管理菜单(ID:53)',
                'sql'         => "UPDATE `admin_menu` SET `add_time` = '2021-12-13 19:46:24', `update_time` = '0000-00-00 00:00:00', `status` = 1, `name` = '资讯管理', `parent_id` = 131, `level` = 2, `key` = 'cmsNewsList', `is_route` = 0 WHERE `id` = 53",
            ],
            [
                'description' => '更新添加资讯菜单(ID:54)',
                'sql'         => "UPDATE `admin_menu` SET `add_time` = '2021-12-14 17:17:07', `update_time` = '0000-00-00 00:00:00', `status` = 1, `name` = '添加资讯', `parent_id` = 131, `level` = 2, `key` = 'cmsNewsAdd', `is_route` = 0 WHERE `id` = 54",
            ],
            [
                'description' => '更新编辑资讯菜单(ID:55)',
                'sql'         => "UPDATE `admin_menu` SET `add_time` = '2021-12-16 10:20:06', `update_time` = '0000-00-00 00:00:00', `status` = 1, `name` = '编辑资讯', `parent_id` = 131, `level` = 2, `key` = 'cmsNewsEdit', `is_route` = 0 WHERE `id` = 55",
            ],
            [
                'description' => '更新话题管理菜单(ID:57)',
                'sql'         => "UPDATE `admin_menu` SET `add_time` = '2021-12-17 09:47:16', `update_time` = '0000-00-00 00:00:00', `status` = 1, `name` = '话题管理', `parent_id` = 131, `level` = 2, `key` = 'cmsTopic', `is_route` = 0 WHERE `id` = 57",
            ],
            [
                'description' => '更新友情链接菜单(ID:91)',
                'sql'         => "UPDATE `admin_menu` SET `add_time` = '2023-09-14 16:21:34', `update_time` = '0000-00-00 00:00:00', `status` = 1, `name` = '友情链接', `parent_id` = 75, `level` = 2, `key` = 'configurationFriendLinkConfig', `is_route` = 0 WHERE `id` = 91",
            ],
        ];

        $totalUpdates = count($updateOperations);
        self::log("开始执行 {$totalUpdates} 个更新操作");

        foreach ($updateOperations as $index => $operation) {
            self::log("执行更新: " . $operation['description']);

            $result = Yii::$app->db->createCommand($operation['sql'])
                ->execute();

            self::log("更新结果: 影响行数 {$result}");

            // 计算进度
            $progress = round(($index + 1) / $totalUpdates * 100, 2);
            self::log("更新进度: {$progress}%");
        }

        self::log("所有更新操作执行完成");
    }

    /*
     * 更新所有简历去到redis缓存
     * php timer_yii script/update-resume-redis
     */
    public function actionUpdateResumeRedis()
    {
        $id = Cache::get('update_resume_redis') ?: 10000000000;
        //获取所有简历数据
        $list = BaseResume::find()
            ->select('id')
            ->where(['status' => 1])
            ->andWhere([
                '<',
                'id',
                $id,
            ])
            ->limit(1000)
            ->orderBy('id desc')
            ->asArray()
            ->column();
        if (empty($list)) {
            exit('已经处理完毕');
        }
        //循环
        foreach ($list as $item) {
            //获取学历里面是否有存在海外学历
            $education_bool = BaseResumeEducation::find()
                ->andWhere([
                    'resume_id' => $item,
                    'status'    => BaseResumeEducation::STATUS_ACTIVE,
                    'is_abroad' => BaseResumeEducation::IS_ABROAD_YES,
                ])
                ->asArray()
                ->exists();

            // 获取工作经历里面是否有存在海外工作经历
            $work_bool = BaseResumeWork::find()
                ->andWhere([
                    'resume_id' => $item,
                    'status'    => BaseResumeWork::STATUS_ACTIVE,
                    'is_abroad' => BaseResumeWork::IS_ABROAD_YES,
                ])
                ->asArray()
                ->exists();
            //其中一个为true则为true
            $is_abroad = $education_bool || $work_bool ? BaseResume::IS_ABROAD_YES : BaseResume::IS_ABROAD_NO;
            $info      = BaseResume::findOne($item);
            if ($info->is_abroad != $is_abroad) {
                $info->is_abroad = $is_abroad;
                $info->save();
            }
            ResumeCacheService::setAll($item);
            self::log("简历ID:" . $item . "执行成功");
            Cache::set('update_resume_redis', $item);
        }
    }

    /**
     *
     * php timer_yii script/get-rk
     */
    public function actionGetRk()
    {
        $disciplines = [
            'RS01' => [
                '一级学科中文' => '理学',
                '二级学科'     => [
                    [
                        '二级学科中文' => '数学',
                        '二级学科英文' => 'RS0101',
                    ],
                    [
                        '二级学科中文' => '物理学',
                        '二级学科英文' => 'RS0102',
                    ],
                    [
                        '二级学科中文' => '化学',
                        '二级学科英文' => 'RS0103',
                    ],
                    [
                        '二级学科中文' => '地球科学',
                        '二级学科英文' => 'RS0104',
                    ],
                    [
                        '二级学科中文' => '地理学',
                        '二级学科英文' => 'RS0105',
                    ],
                    [
                        '二级学科中文' => '生态学',
                        '二级学科英文' => 'RS0106',
                    ],
                    [
                        '二级学科中文' => '海洋科学',
                        '二级学科英文' => 'RS0107',
                    ],
                    [
                        '二级学科中文' => '大气科学',
                        '二级学科英文' => 'RS0108',
                    ],
                ],
            ],
            'RS02' => [
                '一级学科中文' => '工学',
                '二级学科'     => [
                    [
                        '二级学科中文' => '机械工程',
                        '二级学科英文' => 'RS0201',
                    ],
                    [
                        '二级学科中文' => '电力电子工程',
                        '二级学科英文' => 'RS0202',
                    ],
                    [
                        '二级学科中文' => '控制科学与工程',
                        '二级学科英文' => 'RS0205',
                    ],
                    [
                        '二级学科中文' => '通信工程',
                        '二级学科英文' => 'RS0206',
                    ],
                    [
                        '二级学科中文' => '仪器科学',
                        '二级学科英文' => 'RS0207',
                    ],
                    [
                        '二级学科中文' => '生物医学工程',
                        '二级学科英文' => 'RS0208',
                    ],
                    [
                        '二级学科中文' => '计算机科学与工程',
                        '二级学科英文' => 'RS0210',
                    ],
                    [
                        '二级学科中文' => '土木工程',
                        '二级学科英文' => 'RS0211',
                    ],
                    [
                        '二级学科中文' => '化学工程',
                        '二级学科英文' => 'RS0212',
                    ],
                    [
                        '二级学科中文' => '材料科学与工程',
                        '二级学科英文' => 'RS0213',
                    ],
                    [
                        '二级学科中文' => '纳米科学与技术',
                        '二级学科英文' => 'RS0214',
                    ],
                    [
                        '二级学科中文' => '能源科学与工程',
                        '二级学科英文' => 'RS0215',
                    ],
                    [
                        '二级学科中文' => '环境科学与工程',
                        '二级学科英文' => 'RS0216',
                    ],
                    [
                        '二级学科中文' => '水资源工程',
                        '二级学科英文' => 'RS0217',
                    ],
                    [
                        '二级学科中文' => '食品科学与工程',
                        '二级学科英文' => 'RS0219',
                    ],
                    [
                        '二级学科中文' => '生物工程',
                        '二级学科英文' => 'RS0220',
                    ],
                    [
                        '二级学科中文' => '航空航天工程',
                        '二级学科英文' => 'RS0221',
                    ],
                    [
                        '二级学科中文' => '船舶与海洋工程',
                        '二级学科英文' => 'RS0222',
                    ],
                    [
                        '二级学科中文' => '交通运输工程',
                        '二级学科英文' => 'RS0223',
                    ],
                    [
                        '二级学科中文' => '遥感技术',
                        '二级学科英文' => 'RS0224',
                    ],
                    [
                        '二级学科中文' => '矿业工程',
                        '二级学科英文' => 'RS0226',
                    ],
                    [
                        '二级学科中文' => '冶金工程',
                        '二级学科英文' => 'RS0227',
                    ],
                    [
                        '二级学科中文' => '纺织科学与工程',
                        '二级学科英文' => 'RS0228',
                    ],
                ],
            ],
            'RS03' => [
                '一级学科中文' => '生命科学',
                '二级学科'     => [
                    [
                        '二级学科中文' => '生物学',
                        '二级学科英文' => 'RS0301',
                    ],
                    [
                        '二级学科中文' => '基础医学',
                        '二级学科英文' => 'RS0302',
                    ],
                    [
                        '二级学科中文' => '农学',
                        '二级学科英文' => 'RS0303',
                    ],
                    [
                        '二级学科中文' => '兽医学',
                        '二级学科英文' => 'RS0304',
                    ],
                ],
            ],
            'RS04' => [
                '一级学科中文' => '医学',
                '二级学科'     => [
                    [
                        '二级学科中文' => '临床医学',
                        '二级学科英文' => 'RS0401',
                    ],
                    [
                        '二级学科中文' => '公共卫生',
                        '二级学科英文' => 'RS0402',
                    ],
                    [
                        '二级学科中文' => '口腔医学',
                        '二级学科英文' => 'RS0403',
                    ],
                    [
                        '二级学科中文' => '护理学',
                        '二级学科英文' => 'RS0404',
                    ],
                    [
                        '二级学科中文' => '医学技术',
                        '二级学科英文' => 'RS0405',
                    ],
                    [
                        '二级学科中文' => '药学',
                        '二级学科英文' => 'RS0406',
                    ],
                ],
            ],
            'RS05' => [
                '一级学科中文' => '社会科学',
                '二级学科'     => [
                    [
                        '二级学科中文' => '经济学',
                        '二级学科英文' => 'RS0501',
                    ],
                    [
                        '二级学科中文' => '统计学',
                        '二级学科英文' => 'RS0502',
                    ],
                    [
                        '二级学科中文' => '法学',
                        '二级学科英文' => 'RS0503',
                    ],
                    [
                        '二级学科中文' => '政治学',
                        '二级学科英文' => 'RS0504',
                    ],
                    [
                        '二级学科中文' => '社会学',
                        '二级学科英文' => 'RS0505',
                    ],
                    [
                        '二级学科中文' => '教育学',
                        '二级学科英文' => 'RS0506',
                    ],
                    [
                        '二级学科中文' => '新闻传播学',
                        '二级学科英文' => 'RS0507',
                    ],
                    [
                        '二级学科中文' => '心理学',
                        '二级学科英文' => 'RS0508',
                    ],
                    [
                        '二级学科中文' => '工商管理',
                        '二级学科英文' => 'RS0509',
                    ],
                    [
                        '二级学科中文' => '金融学',
                        '二级学科英文' => 'RS0510',
                    ],
                    [
                        '二级学科中文' => '管理学',
                        '二级学科英文' => 'RS0511',
                    ],
                    [
                        '二级学科中文' => '公共管理',
                        '二级学科英文' => 'RS0512',
                    ],
                    [
                        '二级学科中文' => '旅游休闲管理',
                        '二级学科英文' => 'RS0513',
                    ],
                    [
                        '二级学科中文' => '图书情报科学',
                        '二级学科英文' => 'RS0515',
                    ],
                ],
            ],
        ];

        $baseKey = 'TMP:RS:2023';

        foreach ($disciplines as $k1 => $discipline) {
            foreach ($discipline['二级学科'] as $item) {
                // 停2秒
                self::log('开始处理:' . $item['二级学科英文']);
                $data = [
                    'level1'     => $k1,
                    'level1Name' => $discipline['一级学科中文'],
                    'level2'     => $item['二级学科英文'],
                    'level2Name' => $item['二级学科中文'],
                ];
                // 去掉RS
                // $code   = str_replace('RS', '', $item['二级学科英文']);
                $apiUrl           = 'https://www.shanghairanking.cn/api/pub/v1/gras/rank?year=2023&subj_code=' . $item['二级学科英文'];
                $client           = new \GuzzleHttp\Client();
                $res              = $client->request('GET', $apiUrl);
                $body             = $res->getBody();
                $realKey          = $baseKey . ':' . $item['二级学科英文'];
                $body             = json_decode($body, true);
                $body['baseData'] = $data;
                $body             = json_encode($body);
                Cache::set($realKey, $body);
                sleep(2);
            }
        }
    }

    /**
     * 更新邀请数据
     * php timer_yii script/update-invite-data
     */
    public function actionUpdateInviteData()
    {
        //单位邀约
        //        SELECT
        //rl.resume_id,
        //rl.add_time,
        //rl.job_id,
        //j.`name`,
        //j.delivery_way,
        //a.delivery_way,
        //rl.is_apply,
        //jr.add_time
        //FROM
        //resume_library_invite_log rl
        //LEFT JOIN job j ON rl.job_id = j.id
        //LEFT JOIN job_apply_record jr ON rl.job_id = jr.job_id AND rl.resume_id = jr.resume_id
        //LEFT JOIN announcement a ON a.id=j.announcement_id
        //WHERE
        //rl.is_apply = 2
        //AND
        //jr.add_time>rl.add_time
        //AND
        //jr.resume_id=rl.resume_id
        //AND
        //jr.job_id=rl.job_id
        //GROUP BY
        //rl.id

        $companyData = BaseResumeLibraryInviteLog::find()
            ->alias('rl')
            ->leftJoin(['j' => BaseJob::tableName()], 'rl.job_id=j.id')
            ->leftJoin(['jr' => BaseJobApplyRecord::tableName()], 'rl.job_id=jr.job_id AND rl.resume_id=jr.resume_id')
            ->select([
                'rl.id',
                'rl.resume_id',
                'rl.add_time',
                'rl.job_id',
                'rl.is_apply',
                'jr.add_time',
            ])
            ->where([
                'rl.is_apply' => 2,
            ])
            ->andWhere([
                '>',
                'jr.add_time',
                new Expression('rl.add_time'),
            ])
            ->groupBy('rl.id')
            ->asArray()
            ->all();
        //更新这些数据is_apply=1
        foreach ($companyData as $item) {
            $model           = BaseResumeLibraryInviteLog::findOne($item['id']);
            $model->is_apply = 1;
            $model->save();
        }

        //运营邀约
        $adminData = BaseAdminJobInvite::find()
            ->alias('aj')
            ->leftJoin(['jr' => BaseJobApplyRecord::tableName()], 'aj.job_id=jr.job_id AND aj.resume_id=jr.resume_id')
            ->select([
                'aj.id',
                'aj.resume_id',
                'aj.add_time',
                'aj.job_id',
                'jr.add_time',
            ])
            ->where([
                'aj.is_apply' => 2,
            ])
            ->andWhere([
                '>',
                'jr.add_time',
                new Expression('aj.add_time'),
            ])
            ->asArray()
            ->all();
        //更新这些数据is_apply=1
        foreach ($adminData as $item) {
            $model           = BaseAdminJobInvite::findOne($item['id']);
            $model->is_apply = 1;
            $model->save();
        }
        //结束
        self::log('结束');
    }

    /**
     * 修复聊天室数据
     * php timer_yii script/fix-chat-session
     */
    public function actionFixChatSession()
    {
        $sql = "SELECT `b`.`id`               AS `roomId`,
       `b`.`resume_member_id` AS `resumeMemberId`,
       `b`.`company_id`       AS `companyId`,
       `b`.`resume_id`        AS `resumeId`,
       `b`.`current_job_id`   AS `jobId`,
       `b`.`uuid`             AS `chatId`,
       `a`.`is_top`           AS `isTop`,
       `a`.`unread_amount`    AS `unreadAmount`,
       `creator_type`         AS `creatorType`,
       `talk_progress`        AS `talkProgress`
FROM `chat_room_session` `a`
         RIGHT JOIN `chat_room` `b` ON a.chat_room_id = b.id
WHERE a.id is null";

        $list = Yii::$app->db->createCommand($sql)
            ->queryAll();

        $app = ChatApplication::getInstance();

        foreach ($list as $item) {
            $chatId = $item['roomId'];
            if (empty($chatId)) {
                self::log('聊天室ID为空');
                continue;
            }

            // 开启事务
            $transaction = \Yii::$app->db->beginTransaction();

            try {
                $app->fixChatSession($chatId);

                // 提交事务
                $transaction->commit();
                // 数据修复成功
                self::log('聊天室ID：' . $chatId . '，修复成功');
            } catch (\Exception $e) {
                // 回滚事务
                $transaction->rollBack();
                self::log('聊天室ID：' . $chatId . '，修复失败' . $e->getMessage());
            }
        }
    }

    // 替换广告位链接  找到
    // .html#announcement   ->  _a.html
    // .html#introduce  -> .html
    /**
     * php timer_yii script/replace-showcase
     */
    public function actionReplaceShowcase()
    {
        $list = BaseShowcase::find()
            ->select([
                'id',
                'target_link',
            ])
            ->where([
                'like',
                'target_link',
                '#announcement',
            ])
            ->asArray()
            ->all();

        self::log('找到' . count($list) . '条公告相关数据');
        foreach ($list as $item) {
            $model = BaseShowcase::findOne($item['id']);
            self::log('旧链接' . $item['target_link']);
            $newLink            = str_replace('.html#announcement', '_a.html', $item['target_link']);
            $model->target_link = $newLink;

            self::log('新链接' . $model->target_link);
            $model->save();
            self::log('ID：' . $item['id'] . '，公告链接更新成功');
        }

        $list = BaseShowcase::find()
            ->select([
                'id',
                'target_link',
            ])
            ->where([
                'like',
                'target_link',
                '#introduce',
            ])
            ->asArray()
            ->all();

        self::log('找到' . count($list) . '条主页相关数据');

        foreach ($list as $item) {
            $model = BaseShowcase::findOne($item['id']);
            self::log('旧链接' . $item['target_link']);
            $newLink            = str_replace('.html#introduce', '.html', $item['target_link']);
            $model->target_link = $newLink;
            self::log('新链接' . $model->target_link);
            $model->save();
            self::log('ID：' . $item['id'] . '，主页链接更新成功');
        }
    }

    /**
     * 更新简历表的人才库字段
     * php timer_yii script/update-resume-talent-pool
     */
    public function actionUpdateResumeTalentPool()
    {
        //获取完整度大于40的简历
        $list = BaseResume::find()
            ->select([
                'id',
            ])
            ->where([
                '>',
                'complete',
                40,
            ])
            ->andWhere([
                'is_resume_library' => 2,
            ])
            ->orderBy('complete desc')
            ->asArray()
            ->all();
        foreach ($list as $item) {
            (new \common\service\resume\ResumeLibraryService())->setResumeId($item['id'])
                ->setOparetion(\common\service\resume\ResumeLibraryService::OPERATION_UPDATE_FILTER_RESUME_LIBRARY)
                ->run();
            self::log('简历ID：' . $item['id'] . '，更新成功');
        }
    }

    /**
     * 添加公告到Meilisearch
     * php timer_yii script/meilisearch-announcement-add
     */
    public function actionMeilisearchAnnouncementAdd()
    {
        $list = BaseAnnouncement::find()
            ->select('id')
            // id > 35877
            ->where([
                '>',
                'id',
                0,
            ])
            ->asArray()
            ->column();

        $service = new \common\service\meilisearch\announcement\AddService();
        foreach ($list as $item) {
            $id = $item;
            try {
                $service->saveById($id);
                self::log('公告ID：' . $id . '，添加成功');
            } catch (\Exception $e) {
                self::log('公告ID：' . $id . '，添加失败' . $e->getMessage());
            }
        }
    }

    /**
     * 更新简历表的简历类型字段
     * php timer_yii script/update-resume-type
     */
    public function actionUpdateResumeType()
    {
        $id = Cache::get('update_resume_type') ?: 0;
        //获取简历类型为0的简历
        $list  = BaseResume::find()
            ->select([
                'id',
                'title_id',
                'complete',
            ])
            ->andWhere([
                '>',
                'id',
                $id,
            ])
            ->andWhere([
                '>=',
                'complete',
                40,
            ])

            //            ->andWhere([
            //                'between',
            //                'id',
            //                216000,
            //                216175,
            //            ])
            //            ->andWhere(['id' => 216172])
            ->orderBy('id asc')
            ->limit(5000)
            ->asArray()
            ->all();
        $count = count($list);
        $i     = 0;
        foreach ($list as $item) {
            //其中一个为true则为true
            $resumeId = $item['id'];
            $resume   = BaseResume::findOne($resumeId);
            // 获取正高级+副高级职称总数组
            $seniorTitleList = array_column(array_merge(\Yii::$app->params['seniorTitle'],
                \Yii::$app->params['deputySeniorTitle']), 'code');
            //获取中级职称总数组
            $intermediateTitle = array_column(\Yii::$app->params['intermediateTitle'], 'code');

            // 先转成数组,方便判断
            $titleList = explode(',', $item['title_id']);
            // 找到全部学历
            $educationList = BaseResumeEducation::getAllEducationCode($resumeId);
            // 找到全部专业
            //        $allMajorList = BaseResumeEducation::getAllMajorId($resumeId);

            //判断是否有留学(本科以上)或者海外工作经历
            $hasStudyAbroad = BaseResumeEducation::checkHasAbroad($resumeId);
            $hasWorkAbroad  = BaseResumeWork::checkHasAbroad($resumeId);
            $isDoctor       = false;
            $isMaster       = false;
            //判断是否有博士学历
            if (in_array(BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE, $educationList)) {
                $isDoctor = true;
            } elseif (in_array(BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE, $educationList)) {
                $isMaster = true;
            }
            //判断用户是否拥有高级/副高级职称
            $isSeniorTitle       = false;
            $isIntermediateTitle = false;
            foreach ($titleList as $title) {
                if (in_array($title, $seniorTitleList)) {
                    $isSeniorTitle = true;
                } elseif (in_array($title, $intermediateTitle)) {
                    $isIntermediateTitle = true;
                }
            }
            //判断用户是否稀缺专业博士
            $isRareDoctor = BaseResumeEducation::checkRareDoctor($resumeId);
            //判断是否为985/211博士
            $isProjectSchoolDoctor = BaseResumeEducation::checkProjectSchoolDoctor($resumeId);

            //开始判断属于哪种类型
            if ($isSeniorTitle || ($isDoctor && ($hasStudyAbroad || $hasWorkAbroad)) || $isRareDoctor || $isProjectSchoolDoctor) {
                //精英简历
                $resumeType = BaseResume::RESUME_TYPE_ELITE;
            } elseif ($isDoctor || ($isMaster && $isIntermediateTitle) || ($isMaster && $hasStudyAbroad)) {
                //优质简历
                $resumeType = BaseResume::RESUME_TYPE_HIGH_QUALITY;
            } else {
                $resumeType = BaseResume::RESUME_TYPE_ORDINARY;
            }
            //更新meiliSearch
            $txt = '类型未更新';
            if ($resume->resume_type != $resumeType) {
                $txt                 = '类型更新成功，前：' . $resume->resume_type . ',后：' . $resumeType;
                $resume->resume_type = $resumeType;
                $resume->save();
            }
            //简历缓存重新设置
            // ResumeCacheService::setAll($resumeId);
            ResumeCacheService::setInfo($resumeId);
            ++$i;
            Cache::set('update_resume_type', $resumeId, 10800);
            self::log('简历ID：' . $resumeId . '，' . $txt . ',百分比：' . (round(($i / $count), 2) * 100) . '%');
        }
    }

    /**
     * 更新简历表的简历类型字段
     * php timer_yii script/update-resume-type-meili
     */
    public function actionUpdateResumeTypeMeili()
    {
        $id = Cache::get('update_resume_type_meili') ?: 0;
        //获取简历类型为0的简历
        $list  = BaseResume::find()
            ->select([
                'id',
            ])
            ->andWhere([
                '>=',
                'id',
                0,
            ])
            ->andWhere([
                '>',
                'id',
                $id,
            ])
            ->andWhere([
                '>=',
                'complete',
                40,
            ])
            ->orderBy('id asc')
            // ->limit(10000)
            ->asArray()
            ->all();
        $count = count($list);

        $i           = 0;
        self::$count = $count;

        foreach ($list as $k => $item) {
            // 每1w个休息10s

            //其中一个为true则为true
            $resumeId = $item['id'];
            //简历缓存重新设置
            (new ResumeLibraryDocumentService())->addOrUpdateDocument($resumeId);

            // sleep 1秒
            Cache::set('update_resume_type_meili', $resumeId);
            ++$i;

            $log = self::getPercentText($i);
            self::log('简历ID：' . $resumeId . '，' . $log);
            if ($k % 10000 == 0) {
                sleep(10);
            }
        }
    }

    /**
     * 添加公告到Meilisearch
     * php timer_yii script/meilisearch-simple-announcement-add
     */
    public function actionMeilisearchSimpleAnnouncementAdd()
    {
        $key  = 'SCRIPT:MEILISEARCH:SimpleAnnouncement:ID';
        $id   = Cache::get($key) ?: 0;
        $list = BaseAnnouncement::find()
            ->select('id')
            // id > 35877
            ->where([
                '>',
                'id',
                $id,
            ])
            ->limit(2000)
            ->asArray()
            ->column();

        $service = new \common\service\meilisearch\announcement\SimpleAddService();
        foreach ($list as $item) {
            $id = $item;
            try {
                $service->saveById($id);
                self::log('公告ID：' . $id . '，添加成功');
            } catch (\Exception $e) {
                self::log('公告ID：' . $id . '，添加失败' . $e->getMessage());
            }
            Cache::set($key, $id);
        }
    }

    /**
     * 添加单位到Meilisearch
     * php timer_yii script/meilisearch-company-add
     */
    public function actionMeilisearchCompanyAdd()
    {
        $list = BaseCompany::find()
            ->select('id')
            // id > 35877
            ->where([
                '>',
                'id',
                0,
            ])
            ->andWhere([
                'status'  => 1,
                'is_hide' => [
                    0,
                    2,
                ],
            ])
            ->orderBy('id')
            ->asArray()
            ->column();

        $service = new \common\service\meilisearch\company\AddService();
        $service->deleteAll();
        $count = count($list);
        foreach ($list as $k => $item) {
            $id = $item;
            try {
                $service->saveById($id);
                // 百分比  n/总数 成功
                self::log((round(($k / $count), 2) * 100) . '%' . '单位ID：' . $id . '，添加成功');
            } catch (\Exception $e) {
                self::log('单位ID：' . $id . '，添加失败' . $e->getMessage());
            }
        }
    }

    /**
     * 修复自主录入投递数据 正式环境41条
     * php timer_yii script/fix-job-apply-record
     */
    public function actionFixJobApplyRecord()
    {
        //获取自主录入投递数据
        $list = BaseOffSiteJobApply::find()
            ->alias('os')
            ->leftJoin(['jr' => BaseJobApplyRecord::tableName()], 'os.id=jr.apply_site_id')
            ->select([
                'os.id',
                'os.resume_id',
                'os.job_id',
                'os.add_time',
            ])
            ->where([
                'jr.id' => null,
            ])
            ->asArray()
            ->all();
        foreach ($list as $item) {
            //确保没有
            $info = BaseJobApplyRecord::findOne(['apply_site_id' => $item['id']]);
            if ($info) {
                continue;
            }
            $model                = new BaseJobApplyRecord();
            $model->delivery_way  = 99;
            $model->delivery_type = 1;
            $model->apply_site_id = $item['id'];
            $model->resume_id     = $item['resume_id'];
            $model->add_time      = $item['add_time'];
            $model->update_time   = $item['add_time'];
            $model->source        = 3;
            $model->platform      = 1;
            $model->save();
            self::log('自主录入投递ID：' . $item['id'] . '，数据同步到投递记录主表成功');
        }
    }

    // 栏目优化版本添加了部分海外四级栏目 https://365.kdocs.cn/l/cpz2RVfPG627
    // 对应的拼音
    /**
     * 香港    xianggang
     * 澳门    aomen
     * 新加坡    xinjiapo
     * 首尔    shouer
     * 东京    dongjing
     * 仙台    xiantai
     * 大阪    daban
     * 京都    jingdu
     * 墨尔本    moerben
     * 悉尼    xini
     * 奥克兰    aokelan
     * 巴黎    bali
     * 柏林    bailin
     * 慕尼黑    munihei
     * 伦敦    lundun
     * 曼彻斯特    manchesite
     * 牛津    niujin
     * 剑桥    jianqiao
     * 爱丁堡    aidingbao
     * 苏黎世    sulishi
     * 洛桑    luosang
     * 斯德哥尔摩    sidegeermo
     * 代尔夫特    daierfute
     * 鲁汶    luwen
     * 米兰    milan
     * 哥本哈根    gebenhagen
     * 莫斯科    mosike
     * 洛杉矶    luoshanji
     * 旧金山    jiujinshan
     * 波士顿    boshidun
     * 纽约    niuyue
     * 多伦多    duolunduo
     * 韩国    hanguo
     * 日本    riben
     * 澳大利亚    aodaliya
     * 新西兰    xinxilan
     * 法国    faguo
     * 德国    deguo
     * 英国    yingguo
     * 瑞士    ruishi
     * 瑞典    ruidian
     * 荷兰    helan
     * 比利时    bilishi
     * 意大利    yidali
     * 丹麦    danmai
     * 俄罗斯    eluosi
     * 美国    meiguo
     * 加拿大    jianada
     */
    // php ./timer_yii script/add-area-level4
    public function actionAddAreaLevel4()
    {
        $spellConfig = [
            '香港'       => 'xianggang',
            '澳门'       => 'aomen',
            '新加坡'     => 'xinjiapo',
            '首尔'       => 'shouer',
            '东京'       => 'dongjing',
            '仙台'       => 'xiantai',
            '大阪'       => 'daban',
            '京都'       => 'jingdu',
            '墨尔本'     => 'moerben',
            '悉尼'       => 'xini',
            '奥克兰'     => 'aokelan',
            '巴黎'       => 'bali',
            '柏林'       => 'bailin',
            '慕尼黑'     => 'munihei',
            '伦敦'       => 'lundun',
            '曼彻斯特'   => 'manchesite',
            '牛津'       => 'niujin',
            '剑桥'       => 'jianqiao',
            '爱丁堡'     => 'aidingbao',
            '苏黎世'     => 'sulishi',
            '洛桑'       => 'luosang',
            '斯德哥尔摩' => 'sidegeermo',
            '代尔夫特'   => 'daierfute',
            '鲁汶'       => 'luwen',
            '米兰'       => 'milan',
            '哥本哈根'   => 'gebenhagen',
            '莫斯科'     => 'mosike',
            '洛杉矶'     => 'luoshanji',
            '旧金山'     => 'jiujinshan',
            '波士顿'     => 'boshidun',
            '纽约'       => 'niuyue',
            '多伦多'     => 'duolunduo',
            '韩国'       => 'hanguo',
            '日本'       => 'riben',
            '澳大利亚'   => 'aodaliya',
            '新西兰'     => 'xinxilan',
            '法国'       => 'faguo',
            '德国'       => 'deguo',
            '英国'       => 'yingguo',
            '瑞士'       => 'ruishi',
            '瑞典'       => 'ruidian',
            '荷兰'       => 'helan',
            '比利时'     => 'bilishi',
            '意大利'     => 'yidali',
            '丹麦'       => 'danmai',
            '俄罗斯'     => 'eluosi',
            '美国'       => 'meiguo',
            '加拿大'     => 'jianada',

        ];

        // 找到柏林然后把spell改成 bolin
        $area = BaseArea::findOne(['name' => '柏林']);
        if ($area) {
            $area->spell = 'bolin';
            $area->save();
        }

        // 首先是找到俄罗斯联邦改成俄罗斯
        $area = BaseArea::findOne(['name' => '俄罗斯联邦']);
        if (!$area) {
            $area = BaseArea::findOne(['name' => '俄罗斯']);
        }

        if (!$area) {
            self::log('没有找到俄罗斯');
            exit;
        }

        if ($area) {
            $area->name      = '俄罗斯';
            $area->full_name = '俄罗斯';
            $area->save();
        }

        $list = [
            [
                'name' => '日本',
                'list' => [
                    '东京',
                    '仙台',
                    '大阪',
                    '京都',
                ],
            ],
            [
                'name' => '韩国',
                'list' => [
                    '首尔',
                ],
            ],
            [
                'name' => '新加坡',
                'list' => [
                    '新加坡',
                ],
            ],
            [
                'name' => '加拿大',
                'list' => [
                    '多伦多',
                ],
            ],
            [
                'name' => '美国',
                'list' => [
                    '洛杉矶',
                    '旧金山',
                    '波士顿',
                    '纽约',
                ],
            ],
            [
                'name' => '英国',
                'list' => [
                    '伦敦',
                    '曼彻斯特',
                    '牛津',
                    '剑桥',
                    '爱丁堡',
                ],
            ],
            [
                'name' => '荷兰',
                'list' => [
                    '代尔夫特',
                ],
            ],
            [
                'name' => '比利时',
                'list' => [
                    '鲁汶',
                ],
            ],
            [
                'name' => '法国',
                'list' => [
                    '巴黎',
                ],
            ],
            [
                'name' => '德国',
                'list' => [
                    '柏林',
                    '慕尼黑',
                ],
            ],
            [
                'name' => '瑞士',
                'list' => [
                    '苏黎世',
                    '洛桑',
                ],
            ],
            [
                'name' => '丹麦',
                'list' => [
                    '哥本哈根',
                ],
            ],
            [
                'name' => '瑞典',
                'list' => [
                    '斯德哥尔摩',
                ],
            ],
            [
                'name' => '意大利',
                'list' => [
                    '米兰',
                ],
            ],
            [
                'name' => '俄罗斯',
                'list' => [
                    '莫斯科',
                ],
            ],
            [
                'name' => '澳大利亚',
                'list' => [
                    '墨尔本',
                    '悉尼',
                ],
            ],
            [
                'name' => '新西兰',
                'list' => [
                    '奥克兰',
                ],
            ],
        ];

        foreach ($list as $item) {
            $parentArea = BaseArea::findOne(['name' => $item['name']]);
            if (!$parentArea) {
                self::log('没有找到' . $item['name']);
                continue;
            }
            $parentArea->spell = $spellConfig[$item['name']];
            $parentArea->save();

            $parentId = $parentArea->id;

            foreach ($item['list'] as $name) {
                $model            = new BaseArea();
                $model->name      = $name;
                $model->parent_id = $parentId;
                $model->full_name = $parentArea->full_name . ',' . $name;
                $model->spell     = $spellConfig[$name];
                $model->is_china  = $parentArea->is_china;
                $model->level     = 4;
                $model->save();
            }
        }
    }

    /**
     * 同职位公告的中间表数据
     * php timer_yii script/job-announcement-middle-add
     */
    public function actionJobAnnouncementMiddleAdd()
    {
        $id = Cache::get('job_announcement_middle_add_new') ?: 0;
        //获取在线、下线的公告职位数据
        $list  = BaseAnnouncement::find()
            ->select(['id'])
            ->where([
                'status' => [
                    BaseAnnouncement::STATUS_ONLINE,
                    BaseAnnouncement::STATUS_OFFLINE,
                ],
            ])
            ->andWhere([
                '>',
                'id',
                $id,
            ])
            ->orderBy('id asc')
            ->limit(10000)
            ->asArray()
            ->all();
        $count = count($list);
        foreach ($list as $k => $item) {
            try {
                $model = (new AnnouncementAutoClassify($item['id']));
                $model->updateJobAnnouncementAmount();
                $model->updateJobAnnouncementRelation();
            } catch (\Exception $e) {
            }
            // 百分比  n/总数 成功
            self::log((round((($k + 1) / $count), 2) * 100) . '%' . ',公告ID：' . $item['id'] . '，处理成功');
            Cache::set('job_announcement_middle_add_new', $item['id']);
        }
        self::log('共找到' . $count . '条职位数据');
    }

    /**
     * 处理
     * SELECT
     * re.id,
     * re.add_time,
     * re.`status`,
     * re.update_time,
     * re.resume_id,
     * re.major_id,
     * re.major_custom,
     * re.major_id_level_1,
     * re.major_id_level_2,
     * re.major_id_level_3,
     * m.source_type
     * FROM
     * `resume_education` re LEFT JOIN member m ON re.member_id=m.id
     * WHERE
     * re.major_id != major_id_level_3
     * AND re.major_id!= 716
     * AND re.major_id_level_3!=0
     * --    AND re.major_id=0
     * ORDER BY re.add_time ASC
     *
     * php timer_yii script/update-resume-education-major
     */

    public function actionUpdateResumeEducationMajor()
    {
        //获取简历学历数据
        $data = BaseResumeEducation::find()
            ->select([
                'id',
                'major_id',
            ])
            ->where('major_id!=major_id_level_3')
            ->andWhere([
                '!=',
                'major_id',
                716,
            ])
            ->andWhere([
                '!=',
                'major_id_level_3',
                0,
            ])
            ->andWhere([
                'major_id' => 0,
            ])
            ->asArray()
            ->all();

        $count = count($data);
        if ($count != 9) {
            self::log('条数不对');
            exit();
        }

        foreach ($data as $item) {
            $education_info = BaseResumeEducation::findOne($item['id']);
            if ($item['major_id'] == 0) {
                $education_info->major_id_level_1 = 0;
                $education_info->major_id_level_2 = 0;
                $education_info->major_id_level_3 = 0;
                $education_info->save();
                self::log('ID：' . $item['id'] . '，更新成功');
            }
            //            $major_info = BaseMajor::findOne($item['major_id']);
            //            self::log('原始数据：' . json_encode($item));
            //            if ($major_info && $education_info) {
            //                $parent_major_info                = BaseMajor::findOne($major_info->parent_id);
            //                $education_info->major_id_level_1 = $parent_major_info->parent_id;
            //                $education_info->major_id_level_2 = $major_info->parent_id;
            //                $education_info->major_id_level_3 = $item['major_id'];
            //                $education_info->save();
            //                self::log('ID：' . $item['id'] . '，更新成功');
            //            }
        }

        self::log('更新完成');
    }

    // php timer_yii script/update-mobile
    public function actionUpdateMobile()
    {
        // 找到mobile不为空的
        $list = BaseMember::find()
            ->where([
                '!=',
                'mobile',
                '',
            ])
            ->andWhere(['type' => 1])
            ->asArray()
            ->all();

        foreach ($list as $k => $item) {
            $model = BaseMember::findOne($item['id']);
            // 用id+138xxx最终合并成一个新手机号，需要符合手机号的格式，并且前面用13800000，后面看id的长度来处理
            $mobile        = '138' . str_pad($model->id, 8, '0', STR_PAD_LEFT);
            $model->mobile = $mobile;
            $model->save();
            $pencent = round(($k + 1) / count($list) * 100, 2);
            self::log($pencent . '% ,' . 'ID：' . $item['id'] . '，更新成功手机号为' . $mobile);
        }
    }

    // php timer_yii script/rematch-column
    public function actionRematchColumn()
    {
        $key   = 'SCRIPT:REMATCH:FIELD:COLUMN';
        $maxId = BaseAnnouncement::find()
            ->select('id')
            ->where([
                'status' => BaseAnnouncement::STATUS_ONLINE,
            ])
            ->orderBy('id desc')
            ->limit(1)
            ->scalar();
        $id    = Cache::get($key) ?: $maxId;
        // 找到全部在线的公告
        $list = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'a.article_id=b.id')
            ->select([
                'a.id',
            ])
            ->where([
                'a.status' => BaseAnnouncement::STATUS_ONLINE,
                'b.status' => BaseAnnouncement::STATUS_ONLINE,
            ])
            ->andWhere([
                '<',
                'a.id',
                $id,
            ])
            ->asArray()
            ->limit(1000)
            ->orderBy('a.id desc')
            ->all();

        foreach ($list as $k => $item) {
            $timeBegin = microtime(true);
            $model     = new AnnouncementAutoClassify($item['id']);
            $model->run();
            Cache::set($key, $item['id']);
            $endTime = microtime(true);
            // 百分比
            $percent = round(($k + 1) / count($list) * 100, 2);
            self::log($percent . '%  公告ID：' . $item['id'] . '，匹配成功' . '用时' . ($endTime - $timeBegin) . '秒');
        }
    }

    /**
     * 更新群组的单位数量
     * php timer_yii script/update-group-company-count
     */
    public function actionUpdateGroupCompanyCount()
    {
        $cooperationId    = BaseCompanyGroupScoreSystem::getSystemScoreId(BaseCompanyGroup::COOPERATIVE_GROUP_ID);
        $nonCooperationId = BaseCompanyGroupScoreSystem::getSystemScoreId(BaseCompanyGroup::COOPERATIVE_GROUP_NON_ID);
        //合作单位
        BaseCompany::updateAll(['group_score_system_id' => $cooperationId], [
            'is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
        ]);
        //非合作单位
        BaseCompany::updateAll(['group_score_system_id' => $nonCooperationId], [
            'is_cooperation' => BaseCompany::COOPERATIVE_UNIT_NO,
        ]);

        $list = BaseCompanyGroup::find()
            ->select(['id'])
            ->asArray()
            ->all();

        foreach ($list as $item) {
            $count                 = BaseCompanyGroupRelation::find()
                ->where(['group_id' => $item['id']])
                ->count();
            $model                 = BaseCompanyGroup::findOne($item['id']);
            $model->company_number = $count;
            $model->save();

            self::log('群组ID：' . $item['id'] . '，单位数量更新成功，数量：' . $count);
        }
    }

    /**
     * 更新公司默认分组
     * @return void
     * @throws Exception
     * php timer_yii script/update-company-default-group
     */
    public function actionUpdateCompanyDefaultGroup()
    {
        $i = BaseCompany::find()
            ->where(['is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES])
            ->count();
        foreach (BaseCompany::find()
            ->where(['is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES])
            ->select('id')
            ->batch(10000) as $list) {
            foreach ($list as $item) {
                $model             = new BaseCompanyGroupRelation();
                $model->company_id = $item['id'];
                $model->group_id   = BaseCompanyGroup::COOPERATIVE_GROUP_ID;
                $model->save();
                self::log('H剩余数量:' . ($i--));
            }
        }
        self::log('合作单位执行完毕');
        $j = BaseCompany::find()
            ->where(['is_cooperation' => BaseCompany::COOPERATIVE_UNIT_NO])
            ->count();
        foreach (BaseCompany::find()
            ->where(['is_cooperation' => BaseCompany::COOPERATIVE_UNIT_NO])
            ->select('id')
            ->batch(10000) as $list) {
            foreach ($list as $item) {
                $model             = new BaseCompanyGroupRelation();
                $model->company_id = $item['id'];
                $model->group_id   = BaseCompanyGroup::COOPERATIVE_GROUP_NON_ID;
                $model->save();
                self::log('F剩余数量:' . ($j--));
            }
        }

        self::log('非合作执行完毕');
    }

    /** 更新简历表三个地区字段
     * @return void
     * @throws \yii\db\Exception
     */
    public function actionUpdateHouseholdRegister()
    {
        $provinceIds = BaseArea::find()
            ->select('id')
            ->where([
                'level'    => 1,
                'is_china' => BaseArea::IS_CHINA_YES,
            ])
            ->column();

        $householdResumeList = BaseResume::find()
            ->where(['household_register_id' => $provinceIds])
            ->orWhere(['residence' => $provinceIds])
            ->orWhere(['native_place_area_id' => $provinceIds])
            ->select(['id'])
            ->asArray()
            ->column();
        self::log('需要更新户籍数据：' . count($householdResumeList) . '条');

        foreach ($householdResumeList as $resumeId) {
            $model = BaseResume::findOne($resumeId);
            if (in_array($model->household_register_id, $provinceIds)) {
                $model->household_register_id = 0;
            }
            if (in_array($model->residence, $provinceIds)) {
                $model->residence = '';
            }
            if (in_array($model->native_place_area_id, $provinceIds)) {
                $model->native_place_area_id = '';
            }
            if (!$model->save()) {
                self::log('更新户籍数据失败，resumeId = ：' . $resumeId);
                self::log($model->getFirstErrorsMessage());
            } else {
                self::log('更新户籍数据成功，resumeId = ：' . $resumeId);
            }
        }
    }

    /***
     * 补充meilisearch
     * php timer_yii script/meilisearch-job-add
     */
    public function actionMeilisearchJobAdd()
    {
        $id = Cache::get('meilisearch_job_add_new') ?: 0;
        //获取搜索在线下线职位
        $jobIds = BaseJob::find()
            ->select('id')
            ->where([
                'status' => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->where([
                '>',
                'id',
                $id,
            ])
            ->orderBy('id asc')
            ->limit(10000)
            ->column();
        foreach ($jobIds as $jobId) {
            Producer::meilisearch($jobId, MeilisearchJob::TYPE_JOB);
            self::log('已处理，职位ID：' . $jobId);
            Cache::set('meilisearch_job_add_new', $jobId);
        }
    }

    /**
     * 禅道864
     * @return void
     *  php timer_yii script/export-company-615
     */
    public function actionExportCompany615()
    {
        // 应单位——广州商学院（ID：20000615）要求，导出该单位简历库满足以下条件的人才：
        // 36≤年龄≤60，且硕士或博士阶段所学专业一级学科为“体育学”。
        $companyId        = 615;
        $resumeIds        = BaseCompanyResumeLibrary::find()
            ->alias('crl')
            ->select('crl.resume_id')
            ->leftJoin(['r' => BaseResume::tableName()], 'crl.resume_id=r.id')
            ->leftJoin(['e' => BaseResumeEducation::tableName()], 'r.id=e.resume_id and e.status=1')
            ->where(['crl.company_id' => $companyId])
            ->andWhere([
                'between',
                'age',
                30,
                60,
            ])
            ->andWhere([
                'e.education_id' => [
                    3,
                    4,
                ],
            ])
            ->andWhere([
                'e.major_id_level_2' => 27,
            ])
            ->groupBy('e.resume_id')
            ->column();
        $header           = [
            '序号',
            '姓名',
            '性别',
            '年龄',
            '手机号',
            '最高学历',
        ];
        $data             = [];
        $educationNumber3 = 3;
        $educationNumber4 = 3;
        $workNumber       = 7;
        //博士就读学校1	博士专业1	博士就读学校2	博士专业2
        for ($i = 0; $i < $educationNumber4; $i++) {
            $header[] = '博士就读学校' . ($i + 1);
            $header[] = '博士专业' . ($i + 1);
        }
        //硕士就读学校1	硕士专业1  硕士就读学校2	硕士专业2
        for ($j = 0; $j < $educationNumber3; $j++) {
            $header[] = '硕士就读学校' . ($j + 1);
            $header[] = '硕士专业' . ($j + 1);
        }
        //时间段1	单位名称1	职位名称1  时间段2	单位名称2	职位名称2
        for ($k = 0; $k < $workNumber; $k++) {
            $header[] = '时间段' . ($k + 1);
            $header[] = '单位名称' . ($k + 1);
            $header[] = '职位名称' . ($k + 1);
        }

        foreach ($resumeIds as $key => $resumeId) {
            //获取简历信息
            $resumeInfo = BaseResume::findOne($resumeId);
            $memberInfo = BaseMember::findOne($resumeInfo->member_id);
            $item       = [];
            $item[]     = $key + 1;
            $item[]     = $resumeInfo->name;
            $item[]     = $resumeInfo->gender == 1 ? '男' : '女';
            $item[]     = $resumeInfo->age;
            $item[]     = $memberInfo->mobile;
            $item[]     = BaseDictionary::getEducationName($resumeInfo->top_education_code);
            //获取人的博士
            $educationData4      = BaseResumeEducation::find()
                ->where([
                    'education_id' => 4,
                    'status'       => 1,
                    'resume_id'    => $resumeId,
                ])
                ->asArray()
                ->all();
            $educationDataCount4 = count($educationData4);
            foreach ($educationData4 as $educationItem) {
                $item[] = $educationItem['school'];
                $item[] = BaseMajor::getMajorName($educationItem['major_id']);
            }
            for ($i = 0; $i < $educationNumber4 - $educationDataCount4; $i++) {
                $item[] = '';
                $item[] = '';
            }

            //获取人的硕士
            $educationData3      = BaseResumeEducation::find()
                ->where([
                    'education_id' => 3,
                    'status'       => 1,
                    'resume_id'    => $resumeId,
                ])
                ->asArray()
                ->all();
            $educationDataCount3 = count($educationData3);
            foreach ($educationData3 as $educationItem) {
                $item[] = $educationItem['school'];
                $item[] = BaseMajor::getMajorName($educationItem['major_id']);
            }
            for ($i = 0; $i < $educationNumber3 - $educationDataCount3; $i++) {
                $item[] = '';
                $item[] = '';
            }
            //工作经历------有几段就展示几段；按各段工作经历开始时间倒序依次展示
            $workData      = BaseResumeWork::find()
                ->where([
                    'status'    => 1,
                    'resume_id' => $resumeId,
                ])
                ->orderBy('begin_date desc')
                ->asArray()
                ->all();
            $workDataCount = count($workData);
            foreach ($workData as $workItem) {
                $item[] = $workItem['begin_date'] . ' - ' . $workItem['end_date'];
                $item[] = $workItem['company'];
                $item[] = $workItem['job_name'];
            }
            for ($m = 0; $m < $workNumber - $workDataCount; $m++) {
                $item[] = '';
                $item[] = '';
                $item[] = '';
            }

            $data[] = $item;
        }

        $excel = new Excel();
        $url   = $excel->export($data, $header, '广州商学院简历库864');
        self::log('导出成功，url：' . $url);
    }

    // 发布日期>=2024.7.1并且包含推荐+焦点的在线公告是么
    // php ./timer_yii script/hw-announcement
    public function actionHwAnnouncement()
    {
        $list = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'a.article_id=b.id')
            ->innerJoin(['c' => BaseArticleAttribute::tableName()], 'b.id=c.article_id')
            ->innerJoin(['d' => BaseArticleColumn::tableName()], 'b.id=d.article_id')
            ->select([
                'a.id',
                'a.title',
                'a.add_time',
            ])
            ->where([
                'a.status' => BaseAnnouncement::STATUS_ONLINE,
            ])
            ->andWhere([
                'column_id' => 542,
            ])
            // ->andWhere([
            //     '>=',
            //     'b.release_time',
            //     '2023-07-01',
            // ])
            // ->andWhere([
            //     'c.type' => [
            //         BaseArticleAttribute::ATTRIBUTE_RECOMMEND,
            //         BaseArticleAttribute::ATTRIBUTE_FOCUS,
            //     ],
            // ])
            ->asArray()
            ->all();

        // 首先删除这个栏目的全部删除掉
        BaseArticleColumn::deleteAll(['column_id' => 542]);
        BaseJobColumn::deleteAll(['column_id' => 542]);

        $count = count($list);
        self::log('共找到' . $count . '条数据');
        foreach ($list as $k => $item) {
            $model = new AnnouncementAutoClassify($item['id']);
            $model->run();

            // 第几条 百分比多少，处理成功
            self::log(($k + 1) . '/' . $count . ' ' . (round(($k + 1) / $count,
                        2) * 100) . '% ' . '公告ID：' . $item['id'] . '，处理成功');

            sleep(1);
        }
    }

    public function actionHwAnnouncement2()
    {
        // 找到引才活动的全部属性的公告
        $list = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'a.article_id=b.id')
            ->innerJoin(['c' => BaseArticleAttribute::tableName()], 'b.id=c.article_id')
            ->innerJoin(['d' => BaseArticleColumn::tableName()], 'b.id=d.article_id')
            ->select([
                'a.id',
                'a.title',
                'a.add_time',
            ])
            ->where([
                'a.status' => BaseAnnouncement::STATUS_ONLINE,
            ])
            // ->andWhere([
            //     'column_id' => 542,
            // ])
            ->andWhere([
                '>=',
                'b.refresh_time',
                '2024-07-01',
            ])
            ->andWhere([
                'c.type' => [
                    BaseArticleAttribute::ATTRIBUTE_RECOMMEND,
                    BaseArticleAttribute::ATTRIBUTE_FOCUS,
                ],
            ])
            ->asArray()
            ->all();

        // 首先删除这个栏目的全部删除掉
        // BaseArticleColumn::deleteAll(['column_id' => 542]);
        // BaseJobColumn::deleteAll(['column_id' => 542]);

        $count = count($list);
        self::log('共找到' . $count . '条数据');
        foreach ($list as $k => $item) {
            $model = new AnnouncementAutoClassify($item['id']);
            $model->run();

            // 第几条 百分比多少，处理成功
            self::log(($k + 1) . '/' . $count . ' ' . (round(($k + 1) / $count,
                        2) * 100) . '% ' . '公告ID：' . $item['id'] . '，处理成功');

            sleep(1);
        }
    }

    // php ./timer_yii script/hw-announcement3
    public function actionHwAnnouncement3()
    {
        // 找到引才活动的全部属性的公告
        $list = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'a.article_id=b.id')
            ->innerJoin(['c' => BaseArticleAttribute::tableName()], 'b.id=c.article_id')
            ->innerJoin(['d' => BaseArticleColumn::tableName()], 'b.id=d.article_id')
            ->select([
                'a.id',
                'a.title',
                'a.add_time',
            ])
            ->where([
                'a.status' => BaseAnnouncement::STATUS_ONLINE,
            ])
            ->andWhere([
                'column_id' => HomeColumn::ABROAD_QIUXIAN_ID,
            ])
            ->asArray()
            ->all();

        // 首先删除这个栏目的全部删除掉
        // BaseArticleColumn::deleteAll(['column_id' => 542]);
        // BaseJobColumn::deleteAll(['column_id' => 542]);

        $count = count($list);
        self::log('共找到' . $count . '条数据');
        foreach ($list as $k => $item) {
            $model = new AnnouncementAutoClassify($item['id']);
            $model->run();

            // 第几条 百分比多少，处理成功
            self::log(($k + 1) . '/' . $count . ' ' . (round(($k + 1) / $count,
                        2) * 100) . '% ' . '公告ID：' . $item['id'] . '，处理成功');

            sleep(1);
        }
    }

    /**
     * 更新单位包配置表的job_refresh_amount和announcement_refresh_amount字段
     * php timer_yii script/update-company-package-config-number
     * @return void
     */
    public function actionUpdateCompanyPackageConfigNumber()
    {
        //总共有127条
        $constant = 127;
        //获取所有过期单位的资源没有清零的数据
        //        SELECT
        //	c.id,
        //	c.full_name,
        //	cc.job_amount,
        //	cc.announcement_amount,
        //	cc.resume_download_amount,
        //	cc.chat_amount,
        //	c.package_type,
        //	cc.announcement_refresh_amount,
        //	cc.job_refresh_amount,
        //	cc.effect_time
        // FROM
        //	company c
        //	LEFT JOIN company_package_config cc ON c.id = cc.company_id
        //
        // WHERE
        //	c.package_type != 2
        //    AND cc.job_refresh_amount != 0
        $data = BaseCompany::find()
            ->alias('c')
            ->select([
                'cpc.id',
                'cpc.company_id',
                'cpc.job_amount',
                'cpc.announcement_amount',
                'cpc.job_refresh_amount',
                'cpc.announcement_refresh_amount',
            ])
            ->leftJoin(['cpc' => BaseCompanyPackageConfig::tableName()], 'c.id = cpc.company_id')
            ->where([
                'c.package_type' => 3,
            ])
            ->andWhere([
                '>',
                'cpc.job_refresh_amount',
                0,
            ])
            ->orderBy('c.id asc')
            ->asArray()
            ->all();
        if ($constant != count($data)) {
            self::log('总数对不上,请核对错误数据总数');
            exit();
        } else {
            self::log('总数：' . count($data));
        }
        $i = 0;
        foreach ($data as $item) {
            self::log('更新数据,单位ID：' . $item['company_id'] . ',job_amount：' . $item['job_amount'] . ',announcement_amount：' . $item['announcement_amount'] . ',job_refresh_amount：' . $item['job_refresh_amount'] . ',announcement_refresh_amount：' . $item['announcement_refresh_amount']);
            $model                              = BaseCompanyPackageConfig::findOne($item['id']);
            $model->job_amount                  = 0;
            $model->announcement_amount         = 0;
            $model->job_refresh_amount          = 0;
            $model->announcement_refresh_amount = 0;
            $model->save();
            self::log('单位ID为' . $item['company_id'] . '更新成功');
            $i++;
            self::log('已处理' . $i . '条数据');
        }

        self::log('更新完毕');
    }

    /**
     * php timer_yii script/insert-package-log
     * @return void
     */
    public function actionInsertPackageLog()
    {
        $strLog  = "company_id：45,job_amount：60,announcement_amount：30,job_refresh_amount：150,announcement_refresh_amount：0;company_id：72,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：86,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：108,job_amount：60,announcement_amount：40,job_refresh_amount：160,announcement_refresh_amount：0;company_id：110,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：116,job_amount：20,announcement_amount：10,job_refresh_amount：50,announcement_refresh_amount：0;company_id：135,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：150,job_amount：20,announcement_amount：10,job_refresh_amount：50,announcement_refresh_amount：0;company_id：162,job_amount：20,announcement_amount：10,job_refresh_amount：50,announcement_refresh_amount：0;company_id：164,job_amount：60,announcement_amount：40,job_refresh_amount：160,announcement_refresh_amount：0;company_id：173,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：182,job_amount：40,announcement_amount：20,job_refresh_amount：85,announcement_refresh_amount：0;company_id：188,job_amount：60,announcement_amount：40,job_refresh_amount：160,announcement_refresh_amount：0;company_id：190,job_amount：30,announcement_amount：20,job_refresh_amount：80,announcement_refresh_amount：0;company_id：211,job_amount：60,announcement_amount：30,job_refresh_amount：150,announcement_refresh_amount：0;company_id：216,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：225,job_amount：20,announcement_amount：10,job_refresh_amount：50,announcement_refresh_amount：0;company_id：227,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：228,job_amount：40,announcement_amount：20,job_refresh_amount：97,announcement_refresh_amount：0;company_id：246,job_amount：60,announcement_amount：30,job_refresh_amount：150,announcement_refresh_amount：0;company_id：251,job_amount：60,announcement_amount：30,job_refresh_amount：150,announcement_refresh_amount：0;company_id：252,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：263,job_amount：80,announcement_amount：40,job_refresh_amount：200,announcement_refresh_amount：0;company_id：299,job_amount：20,announcement_amount：10,job_refresh_amount：50,announcement_refresh_amount：0;company_id：306,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：320,job_amount：40,announcement_amount：20,job_refresh_amount：96,announcement_refresh_amount：0;company_id：337,job_amount：60,announcement_amount：30,job_refresh_amount：150,announcement_refresh_amount：0;company_id：339,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：344,job_amount：90,announcement_amount：60,job_refresh_amount：240,announcement_refresh_amount：0;company_id：355,job_amount：20,announcement_amount：10,job_refresh_amount：50,announcement_refresh_amount：0;company_id：359,job_amount：20,announcement_amount：10,job_refresh_amount：50,announcement_refresh_amount：0;company_id：372,job_amount：60,announcement_amount：30,job_refresh_amount：150,announcement_refresh_amount：0;company_id：378,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：381,job_amount：20,announcement_amount：10,job_refresh_amount：50,announcement_refresh_amount：0;company_id：398,job_amount：80,announcement_amount：40,job_refresh_amount：200,announcement_refresh_amount：0;company_id：400,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：408,job_amount：60,announcement_amount：30,job_refresh_amount：150,announcement_refresh_amount：0;company_id：411,job_amount：20,announcement_amount：10,job_refresh_amount：50,announcement_refresh_amount：0;company_id：412,job_amount：80,announcement_amount：40,job_refresh_amount：200,announcement_refresh_amount：0;company_id：416,job_amount：120,announcement_amount：80,job_refresh_amount：320,announcement_refresh_amount：0;company_id：417,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：418,job_amount：80,announcement_amount：40,job_refresh_amount：200,announcement_refresh_amount：0;company_id：426,job_amount：90,announcement_amount：60,job_refresh_amount：240,announcement_refresh_amount：0;company_id：468,job_amount：40,announcement_amount：20,job_refresh_amount：99,announcement_refresh_amount：0;company_id：473,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：479,job_amount：60,announcement_amount：30,job_refresh_amount：150,announcement_refresh_amount：0;company_id：480,job_amount：80,announcement_amount：40,job_refresh_amount：149,announcement_refresh_amount：0;company_id：497,job_amount：80,announcement_amount：40,job_refresh_amount：200,announcement_refresh_amount：0;company_id：503,job_amount：60,announcement_amount：30,job_refresh_amount：150,announcement_refresh_amount：0;company_id：518,job_amount：60,announcement_amount：30,job_refresh_amount：149,announcement_refresh_amount：0;company_id：521,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：527,job_amount：60,announcement_amount：40,job_refresh_amount：160,announcement_refresh_amount：0;company_id：537,job_amount：20,announcement_amount：10,job_refresh_amount：50,announcement_refresh_amount：0;company_id：540,job_amount：40,announcement_amount：20,job_refresh_amount：90,announcement_refresh_amount：0;company_id：543,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：544,job_amount：80,announcement_amount：40,job_refresh_amount：200,announcement_refresh_amount：0;company_id：552,job_amount：60,announcement_amount：30,job_refresh_amount：150,announcement_refresh_amount：0;company_id：568,job_amount：60,announcement_amount：30,job_refresh_amount：150,announcement_refresh_amount：0;company_id：577,job_amount：60,announcement_amount：30,job_refresh_amount：150,announcement_refresh_amount：0;company_id：593,job_amount：20,announcement_amount：10,job_refresh_amount：50,announcement_refresh_amount：0;company_id：609,job_amount：20,announcement_amount：10,job_refresh_amount：50,announcement_refresh_amount：0;company_id：613,job_amount：20,announcement_amount：10,job_refresh_amount：50,announcement_refresh_amount：0;company_id：618,job_amount：90,announcement_amount：60,job_refresh_amount：240,announcement_refresh_amount：0;company_id：620,job_amount：60,announcement_amount：40,job_refresh_amount：160,announcement_refresh_amount：0;company_id：621,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：784,job_amount：60,announcement_amount：40,job_refresh_amount：160,announcement_refresh_amount：0;company_id：843,job_amount：60,announcement_amount：30,job_refresh_amount：150,announcement_refresh_amount：0;company_id：894,job_amount：90,announcement_amount：60,job_refresh_amount：240,announcement_refresh_amount：0;company_id：903,job_amount：20,announcement_amount：10,job_refresh_amount：50,announcement_refresh_amount：0;company_id：912,job_amount：60,announcement_amount：30,job_refresh_amount：150,announcement_refresh_amount：0;company_id：923,job_amount：60,announcement_amount：30,job_refresh_amount：150,announcement_refresh_amount：0;company_id：950,job_amount：80,announcement_amount：40,job_refresh_amount：200,announcement_refresh_amount：0;company_id：958,job_amount：60,announcement_amount：30,job_refresh_amount：150,announcement_refresh_amount：0;company_id：1149,job_amount：20,announcement_amount：10,job_refresh_amount：50,announcement_refresh_amount：0;company_id：1214,job_amount：60,announcement_amount：30,job_refresh_amount：150,announcement_refresh_amount：0;company_id：1234,job_amount：120,announcement_amount：80,job_refresh_amount：320,announcement_refresh_amount：0;company_id：1255,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：1256,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：1312,job_amount：60,announcement_amount：30,job_refresh_amount：150,announcement_refresh_amount：0;company_id：1317,job_amount：20,announcement_amount：10,job_refresh_amount：50,announcement_refresh_amount：0;company_id：1584,job_amount：90,announcement_amount：60,job_refresh_amount：240,announcement_refresh_amount：0;company_id：2555,job_amount：20,announcement_amount：10,job_refresh_amount：37,announcement_refresh_amount：0;company_id：2926,job_amount：39,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：3361,job_amount：37,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：3683,job_amount：20,announcement_amount：10,job_refresh_amount：50,announcement_refresh_amount：0;company_id：3690,job_amount：120,announcement_amount：80,job_refresh_amount：320,announcement_refresh_amount：0;company_id：3870,job_amount：20,announcement_amount：10,job_refresh_amount：50,announcement_refresh_amount：0;company_id：4385,job_amount：40,announcement_amount：20,job_refresh_amount：94,announcement_refresh_amount：0;company_id：4390,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：4849,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：5121,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：5736,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：6060,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：7456,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：7607,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：7916,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：8219,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：8261,job_amount：40,announcement_amount：20,job_refresh_amount：97,announcement_refresh_amount：0;company_id：8565,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：9664,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：10195,job_amount：35,announcement_amount：19,job_refresh_amount：100,announcement_refresh_amount：0;company_id：10556,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：11616,job_amount：40,announcement_amount：19,job_refresh_amount：100,announcement_refresh_amount：0;company_id：13169,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：13599,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：14219,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：14299,job_amount：40,announcement_amount：20,job_refresh_amount：94,announcement_refresh_amount：0;company_id：14501,job_amount：40,announcement_amount：20,job_refresh_amount：91,announcement_refresh_amount：0;company_id：16003,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：16585,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：17071,job_amount：37,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：17281,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：17739,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：17744,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：18260,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：18360,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：18461,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：18694,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：19175,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：19470,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：19638,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：19925,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：19983,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：20377,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：20489,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：21256,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0;company_id：21399,job_amount：40,announcement_amount：20,job_refresh_amount：100,announcement_refresh_amount：0";
        $timeArr = [
            '2022-07-27 00:00:00',
            '2022-06-26 00:00:00',
            '2022-06-26 00:00:00',
            '2022-06-26 00:00:00',
            '2022-06-26 00:00:00',
            '2022-05-26 00:00:00',
            '2022-06-26 00:00:00',
            '2022-05-26 00:00:00',
            '2022-05-26 00:00:00',
            '2022-06-26 00:00:00',
            '2022-06-26 00:00:00',
            '2022-06-26 00:00:00',
            '2022-06-26 00:00:00',
            '2022-05-26 00:00:00',
            '2022-07-27 00:00:00',
            '2022-06-26 00:00:00',
            '2022-05-26 00:00:00',
            '2022-06-26 00:00:00',
            '2022-06-26 00:00:00',
            '2022-07-27 00:00:00',
            '2022-07-27 00:00:00',
            '2022-06-26 00:00:00',
            '2022-08-27 00:00:00',
            '2022-05-26 00:00:00',
            '2022-06-26 00:00:00',
            '2022-06-26 00:00:00',
            '2022-07-27 00:00:00',
            '2022-06-26 00:00:00',
            '2022-07-27 00:00:00',
            '2022-05-26 00:00:00',
            '2022-05-26 00:00:00',
            '2022-07-27 00:00:00',
            '2022-06-26 00:00:00',
            '2022-05-26 00:00:00',
            '2022-08-27 00:00:00',
            '2022-06-26 00:00:00',
            '2022-07-27 00:00:00',
            '2022-05-26 00:00:00',
            '2022-08-27 00:00:00',
            '2022-08-27 00:00:00',
            '2022-06-26 00:00:00',
            '2022-08-27 00:00:00',
            '2022-07-27 00:00:00',
            '2022-06-26 00:00:00',
            '2022-06-26 00:00:00',
            '2022-07-27 00:00:00',
            '2022-08-27 00:00:00',
            '2022-08-27 00:00:00',
            '2022-07-27 00:00:00',
            '2022-07-27 00:00:00',
            '2022-06-26 00:00:00',
            '2022-06-26 00:00:00',
            '2022-05-26 00:00:00',
            '2022-06-26 00:00:00',
            '2022-06-26 00:00:00',
            '2022-08-27 00:00:00',
            '2022-07-27 00:00:00',
            '2022-07-27 00:00:00',
            '2022-07-27 00:00:00',
            '2022-05-26 00:00:00',
            '2022-05-26 00:00:00',
            '2022-05-26 00:00:00',
            '2022-07-27 00:00:00',
            '2022-06-26 00:00:00',
            '2022-06-26 00:00:00',
            '2022-06-22 00:00:00',
            '2022-07-27 00:00:00',
            '2022-07-27 00:00:00',
            '2022-05-26 00:00:00',
            '2022-07-27 00:00:00',
            '2022-07-27 00:00:00',
            '2022-08-27 00:00:00',
            '2022-07-27 00:00:00',
            '2022-05-26 00:00:00',
            '2022-07-27 00:00:00',
            '2022-08-27 00:00:00',
            '2022-06-26 00:00:00',
            '2022-06-26 00:00:00',
            '2022-07-27 00:00:00',
            '2022-05-26 00:00:00',
            '2022-07-27 00:00:00',
            '2022-05-26 00:00:00',
            '2022-06-19 00:00:00',
            '2022-06-17 00:00:00',
            '2022-05-26 00:00:00',
            '2022-08-27 00:00:00',
            '2022-05-26 00:00:00',
            '2022-06-09 00:00:00',
            '2022-08-07 00:00:00',
            '2022-08-07 00:00:00',
            '2022-08-07 00:00:00',
            '2022-08-07 00:00:00',
            '2022-08-07 00:00:00',
            '2022-08-07 00:00:00',
            '2022-08-07 00:00:00',
            '2022-08-07 00:00:00',
            '2022-08-11 00:00:00',
            '2022-07-22 00:00:00',
            '2022-08-07 00:00:00',
            '2022-08-07 00:00:00',
            '2022-06-23 00:00:00',
            '2022-08-07 00:00:00',
            '2022-07-02 00:00:00',
            '2022-08-07 00:00:00',
            '2022-08-07 00:00:00',
            '2022-08-07 00:00:00',
            '2022-07-28 00:00:00',
            '2022-07-23 00:00:00',
            '2022-08-07 00:00:00',
            '2022-08-07 00:00:00',
            '2022-08-01 00:00:00',
            '2022-08-08 00:00:00',
            '2022-08-08 00:00:00',
            '2022-08-08 00:00:00',
            '2022-08-12 00:00:00',
            '2022-08-12 00:00:00',
            '2022-08-12 00:00:00',
            '2022-08-12 00:00:00',
            '2022-08-14 00:00:00',
            '2022-08-18 00:00:00',
            '2022-08-22 00:00:00',
            '2022-08-20 00:00:00',
            '2022-08-20 00:00:00',
            '2022-08-22 00:00:00',
            '2022-08-22 00:00:00',
            '2022-08-28 00:00:00',
            '2022-08-28 00:00:00',
        ];
        //炸成数组
        $arr1 = explode(';', $strLog);
        $data = [];
        foreach ($arr1 as $key => $itemStr) {
            $itemStrArr = explode(',', $itemStr);
            $dataItem   = [];
            foreach ($itemStrArr as $item) {
                $itemArr               = explode('：', $item);
                $dataItem[$itemArr[0]] = $itemArr[1];
            }
            $dataItem['expire_time'] = $timeArr[$key];
            $data[]                  = $dataItem;
        }
        self::log('总数:' . count($data));
        //开始写日志
        $l                 = BaseCompanyPackageChangeLog::RELEVANT_TYPE_LIST;
        $relevantList      = array_splice($l, 0, 4);
        $packageConfigName = BaseCompanyPackageSystemConfig::PACKAGE_CONFIG_NAME;
        $packageSurplus    = [
            $packageConfigName['job_amount']                  => 0,
            $packageConfigName['announcement_amount']         => 0,
            $packageConfigName['job_refresh_amount']          => 0,
            $packageConfigName['announcement_refresh_amount'] => 0,
        ];
        $packageSurplus    = json_encode($packageSurplus);
        foreach ($data as $item) {
            //获取单位信息
            $companyInfo = BaseCompany::findOne($item['company_id']);
            if (!$companyInfo) {
                self::log('单位信息不存在:' . $item['company_id']);
            }
            $member = BaseMember::findOne($companyInfo->member_id);
            $data   = [
                'identify'        => BaseCompanyPackageChangeLog::IDENTIFY_REDUCE,
                'surplus'         => 0,
                'package_surplus' => $packageSurplus,
                'member_id'       => $companyInfo->member_id,
                'member_name'     => $member->username ?: '',
                'company_id'      => $companyInfo->id ?: '',
                'company_name'    => $companyInfo->full_name ?: '',
                'handle_after'    => '0',
                'handler_type'    => BaseCompanyPackageChangeLog::HANDLER_TYPE_PERSON,
                'handler_id'      => $companyInfo->member_id,
                'handler'         => $companyInfo->full_name ?: '',
                'content'         => '服务过期',
                'remark'          => '服务过期',
                'handle_type'     => BaseCompanyPackageChangeLog::HANDLE_TYPE_EXPIRATION,
                'type'            => '',
                'change_amount'   => 0,
                'handle_before'   => '',
                'add_time'        => $item['expire_time'],
            ];
            foreach ($relevantList as $v) {
                $oldAmount             = $item[$v['field']];
                $data['type']          = $v['type'];
                $data['change_amount'] = $oldAmount . '';
                $data['handle_before'] = $oldAmount . '';
                //                BaseCompanyPackageChangeLog::createCompanyPackageChangeLog($data);
                $model                  = new BaseCompanyPackageChangeLog();
                $model->type            = $data['type'] ?: '';
                $model->identify        = $data['identify'] ?: '';
                $model->change_amount   = $data['change_amount'] ?: '';
                $model->surplus         = $data['surplus'] ?: '';
                $model->package_surplus = $data['package_surplus'] ?: '';
                $model->member_id       = $data['member_id'] ?: '';
                $model->member_name     = $data['member_name'] ?: '';
                $model->company_id      = $data['company_id'] ?: '';
                $model->company_name    = $data['company_name'] ?: '';
                $model->handle_before   = $data['handle_before'];
                $model->handle_after    = $data['handle_after'];
                $model->handler_type    = $data['handler_type'] ?: '';
                $model->handler         = $data['handler'] ?: '';
                $model->handler_id      = $data['handler_id'] ?: '';
                $model->content         = $data['content'] ?: '';
                $model->remark          = $data['remark'] ?: '';
                $model->handle_type     = $data['handle_type'] ?: 0;
                $model->add_time        = $data['add_time'] ?: date('Y-m-d H:i:s');
                $model->save();
            }
        }
        self::log('日志处理完毕');
    }

    /**
     * php timer_yii script/fix-meilisearch
     * @return void
     */
    public function actionFixMeilisearch()
    {
        // $list = Cache::lPop('meilisearch_job');

        $key = 'meilisearch_job';
        // lpop

        // 每次处理1000个
        for ($i = 0; $i < 5000; $i++) {
            $value = Cache::lPop($key);
            if (!$value) {
                self::log('没有数据了');
                break;
            }

            //  之前是这样进去的 Cache::lPush('meilisearch_job', $this->mainId . '_' . $this->type);
            $list = explode('_', $value);
            $id   = $list[0];
            $type = $list[1];

            Producer::meilisearch($id, $type);

            self::log('处理成功：' . $id . ' ' . $type);
        }
    }

    /**
     * php timer_yii script/update-top-education
     * @return void
     */
    public function actionUpdateTopEducation()
    {
        //先获取所有正常的简历
        $data  = BaseResume::find()
            ->select([
                'id',
                'last_education_id',
                'top_education_code',
            ])
            //            ->where(['status' => BaseResume::STATUS_ACTIVE])
            ->andWhere([
                'id' => [
                    10118,
                    13260,
                    16094,
                    22965,
                    30076,
                    32989,
                    34560,
                    42400,
                    49760,
                    49814,
                    51636,
                    55836,
                    75719,
                    76472,
                    76734,
                    77938,
                    80789,
                    82055,
                    85537,
                    86581,
                    88866,
                    90210,
                    90948,
                    91287,
                    122990,
                    128767,
                    129161,
                    133896,
                    145453,
                    148786,
                    150930,
                    153963,
                    160507,
                    161183,
                    162752,
                    176172,
                    179449,
                    182293,
                    184358,
                    205711,
                    228949,
                    230678,
                    250992,
                    289044,
                    298252,
                    303767,
                    306686,
                    329815,
                    338954,
                    341498,
                    350769,
                    351243,
                    356388,
                    357342,
                    357675,
                    371166,
                    372624,
                    380824,
                    383688,
                    388050,
                    394876,
                    398470,
                    411461,
                    411541,
                    413860,
                    429379,
                    438573,
                    441001,
                    441147,
                    450277,
                    451027,
                    452220,
                    454981,
                    466978,
                    469527,
                    473093,
                    477041,
                    488917,
                    501992,
                    533431,
                    564317,
                    574082,
                    576199,
                ],
            ])
            ->asArray()
            ->all();
        $count = count($data);
        if ($count != 83) {
            self::log('总数不对请核对，简历数量：' . $count);
            exit();
        }
        foreach ($data as $item) {
            //获取最高学历
            $topEducation = BaseResumeEducation::getTopEducationInfo($item['id']);
            if ($topEducation['id'] != $item['last_education_id'] || $topEducation['education_id'] != $item['top_education_code']) {
                self::log('旧简历：' . $item['id'] . '，当前最高学历ID：' . $item['last_education_id'] . '，当前最高学历：' . $item['top_education_code']);
                self::log('新简历：' . $item['id'] . '，当前最高学历ID：' . $topEducation['id'] . '，当前最高学历：' . $topEducation['education_id']);
                //更新
                $model                     = BaseResume::findOne($item['id']);
                $model->last_education_id  = $topEducation['id'];
                $model->top_education_code = $topEducation['education_id'];
                $model->save();
                ResumeCacheService::setInfo($item['id']);
            }
        }
        self::log('处理完毕');
    }

    /**
     * 指定公告近队列
     * @return void
     * @throws Exception
     *                  php timer_yii script/update-announcement
     */
    public function actionUpdateAnnouncement()
    {
        //获取昨天到现在的公告
        $yesterday = date('Y-m-d', strtotime('-1 day')) . ' 00:00:00';
        $data      = BaseAnnouncement::find()
            ->alias('a')
            ->select(['a.id'])
            ->leftJoin(['art' => BaseArticle::tableName()], 'a.article_id=art.id')
            ->andWhere([
                '>=',
                'art.refresh_time',
                $yesterday,
            ])
            ->asArray()
            ->all();
        foreach ($data as $item) {
            $model = new AnnouncementAutoClassify($item['id']);
            $model->run();
            self::log('公告ID：' . $item['id'] . '，推送成功！');
        }
    }

    /**
     * 初始化数据，只需将在线的数据弄一下就好
     * @return void
     */
    public function actionUpdateCompanyStatIsPay()
    {
        $where = [
            [
                '=',
                's.status',
                BaseShowcase::STATUS_ONLINE,
            ],
            [
                '<>',
                's.company_id',
                0,
            ],
        ];
        // 查询生效中的广告
        $showCaseList = BaseShowcase::find()
            ->alias('s')
            ->select([
                's.company_id',
                'h.platform_type',
                's.type',
            ])
            ->leftJoin(['h' => BaseHomePosition::tableName()], 'h.id = s.home_position_id')
            ->where(new AndCondition($where))
            ->asArray()
            ->all();

        $companyList = [];
        foreach ($showCaseList as $showCaseInfo) {
            if (!isset($companyList[$showCaseInfo['company_id']])) {
                $companyList[$showCaseInfo['company_id']] = [];
            }

            $companyList[$showCaseInfo['company_id']][] = $showCaseInfo;
        }

        foreach ($companyList as $companyId => $showCases) {
            $isPay         = 2;
            $isBoshihouPay = 2;
            foreach ($showCases as $showCaseInfo) {
                $types = explode(',', $showCaseInfo['type']);

                if (in_array(BaseShowcase::TYPE_PAY, $types)) {
                    $isPay = 1;
                    if ($showCaseInfo['platform_type'] == BaseHomePosition::PLATFORM_BO_SHI_HOU) {
                        $isBoshihouPay = 1;
                        break;
                    }
                }
            }
            if ($isPay == 2 && $isBoshihouPay == 2) {
                continue;
            }

            $statModel                  = BaseCompanyStatData::findOne(['company_id' => $companyId]);
            $statModel->is_pay          = $isPay;
            $statModel->is_boshihou_pay = $isBoshihouPay;

            $statModel->save();
            //单位下公告
            BaseAnnouncementExtra::updateAll([
                'is_pay'          => $isPay,
                'is_boshihou_pay' => $isBoshihouPay,
            ], ['company_id' => $companyId]);
            //单位下的职位
            BaseJobExtra::updateAll([
                'is_pay'          => $isPay,
                'is_boshihou_pay' => $isBoshihouPay,
            ], ['company_id' => $companyId]);
        }
    }

    /**
     * php timer_yii script/update-resume-tag
     */
    public function actionUpdateResumeTag()
    {
        $list = [
            10005613,
            10006466,
            10008056,
            10011944,
            10012691,
            10015651,
            10018546,
            10020711,
            10022009,
            10022768,
            10024284,
            10026029,
            10028156,
            10038967,
            10041402,
            10043486,
            10044632,
            10045745,
            10047476,
            10050197,
            10051463,
            10051470,
            10052610,
            10052998,
            10053534,
            10055946,
            10056092,
            10056833,
            10057773,
            10059216,
            10059261,
            10060214,
            10063307,
            10063600,
            10065926,
            10068150,
            10072930,
            10073454,
            10074369,
            10074800,
            10074920,
            10076021,
            10077273,
            10079954,
            10082609,
            10085130,
            10087346,
            10090210,
            10099750,
            10101423,
            10102613,
            10102711,
            10107657,
            10117051,
            10117838,
            10117929,
            10119081,
            10120240,
            10124727,
            10125185,
            10128240,
            10129122,
            10141520,
            10149992,
            10152289,
            10153185,
            10155773,
            10165606,
            10167151,
            10176000,
            10177007,
            10177248,
            10180862,
            10182654,
            10185561,
            10194848,
            10195871,
            10196319,
            10198136,
            10198150,
            10200355,
            10202385,
            10220647,
            10226248,
            10229467,
            10242236,
            10243206,
            10244338,
            10244653,
            10244757,
            10248514,
            10249835,
            10251546,
            10253407,
            10253725,
            10253781,
            10268341,
            10268937,
            10274727,
            10278511,
            10282707,
            10284566,
            10287867,
            10288220,
            10294589,
            10295054,
            10295914,
            10296654,
            10299293,
            10300094,
            10300666,
            10300983,
            10307646,
            10310012,
            10310182,
            10311259,
            10313199,
            10314045,
            10314169,
            10316784,
            10317945,
            10320856,
            10321724,
            10324881,
            10325512,
            10326644,
            10327653,
            10328557,
            10329804,
            10330423,
            10332623,
            10335519,
            10336765,
            10336842,
            10338288,
            10339211,
            10340716,
            10340875,
            10342077,
            10351122,
            10351818,
            10352027,
            10353669,
            10359254,
            10366131,
            10366842,
            10369210,
            10376526,
            10378388,
            10381183,
            10381218,
            10391032,
            10396266,
            10399944,
            10404496,
            10416508,
            10416677,
            10427360,
            10429933,
            10430279,
            10432443,
            10435837,
            10437270,
            10439213,
            10441781,
            10444536,
            10446365,
            10446428,
            10447759,
            10448794,
            10451213,
            10454866,
            10456411,
            10458713,
            10459381,
            10459774,
            10460138,
            10462340,
            10462442,
            10468705,
            10469011,
            10472654,
            10474179,
            10476451,
            10477590,
            10480332,
            10482263,
            10485088,
            10485774,
            10485942,
            10489545,
            10489960,
            10492230,
            10493303,
            10495597,
            10496288,
            10498352,
            10501250,
            10502613,
            10504335,
            10508010,
            10508751,
            10509501,
            10510811,
            10511652,
            10513180,
            10515729,
            10516603,
            10520727,
            10522502,
            10529439,
            10533738,
            10538384,
            10538788,
            10538962,
            10539097,
            10539504,
            10540356,
            10544361,
            10545471,
            10546488,
            10547276,
            10547871,
            10548958,
            10554235,
            10557603,
            10561239,
            10563402,
            10563735,
            10566133,
            10569844,
            10575621,
            10579609,
            10579894,
            10580956,
            10581313,
            10593828,
            10596261,
            10597462,
            10598502,
            10600944,
            10606660,
            10609488,
            10617393,
            10617430,
            10620646,
            10621197,
            10621517,
            10625636,
            10627333,
            10631439,
            10631670,
            10636521,
            10637442,
            10640706,
            10642992,
            10648501,
            10656874,
            10660521,
            10660683,
            10672451,
            10673650,
            10673947,
            10675027,
            10676583,
            10677346,
            10681017,
            10683168,
            10684784,
            10684863,
            10685227,
            10687393,
            10691805,
            10700041,
            10705016,
            10707635,
            10710815,
            10712953,
            10713054,
            10714584,
            10718165,
            10724568,
            10724757,
            10724787,
            10725299,
            10725493,
            10727082,
            10727744,
            10728146,
            10729953,
            10735387,
            10740443,
            10746256,
            10748396,
            10750647,
            10750695,
            10751274,
            10753836,
            10755337,
            10755947,
            10758823,
            10762399,
            10763296,
            10768080,
            10770679,
            10774235,
            10774280,
            10774569,
            10775991,
            10779287,
            10779634,
            10783394,
            10787023,
            10788413,
            10791453,
            10793570,
            10793738,
            10795977,
            10796621,
            10809471,
            10810782,
            10811022,
            10813034,
            10814494,
            10815365,
            10816089,
            10817073,
            10655131,
            10048305,
            10186815,
            10313156,
            10169872,
            10525330,
            10278930,
            10479202,
            10454003,
            10527805,
            10625673,
            10617976,
            10283876,
            10305184,
            10583429,
            10464147,
            10225859,
            10249667,
            10513893,
            10719699,
            10744553,
            10284445,
            10684441,
            10299302,
            10533737,
            10407067,
            10249357,
            10178335,
            10246889,
            10744558,
            10683779,
            10723207,
            10326610,
            10402263,
            10381164,
            10218193,
            10315094,
            10270908,
            10289239,
            10590963,
            10502164,
            10304498,
            10054609,
            10096161,
            10160789,
            10178114,
            10226240,
            10486162,
            10532592,
            10683670,
            10799488,
            10105626,
            10650139,
            10753504,
            10304271,
            10333464,
            10333562,
            10278373,
            10293561,
            10239340,
            10030757,
            10333983,
            10246129,
            10737407,
            10749248,
            10590391,
            10341489,
            10132412,
            10772870,
            10416357,
            10102554,
            10133492,
            10135942,
            10300793,
            10328444,
            10667801,
            10141863,
            10191407,
            10207253,
            10216334,
            10071268,
            10721221,
            10496656,
            10291154,
            10542695,
            10652447,
            10687486,
            10572392,
            10590800,
            10494523,
            10476125,
            10025560,
            10068020,
            10334199,
            10609708,
            10626494,
            10366133,
            10740503,
            10030648,
            10098980,
            10207581,
            10739343,
            10741911,
            10747590,
            10795838,
            10514742,
            10410606,
            10813388,
            10720067,
            10081855,
            10211387,
            10297728,
            10364813,
            10385218,
            10464798,
            10474461,
            10500447,
            10523152,
            10526461,
            10562924,
            10622675,
            10657365,
            10659359,
            10706886,
            10714447,
            10710283,
            10750631,
            10756598,
            10776704,
            10522900,
            10566060,
            10052077,
            10351127,
            10561190,
            10564506,
            10602394,
            10659698,
            10715348,
            10717532,
            10808990,
            10002993,
            10300062,
            10458425,
            10495654,
            10273294,
            10505687,
            10712990,
            10721977,
            10447293,
            10763001,
            10173421,
            10580248,
            10108207,
            10268649,
            10433644,
            10434874,
            10499984,
            10463198,
            10670414,
            10775219,
            10029834,
            10587541,
            10725090,
            10762014,
            10027519,
            10040721,
            10398914,
            10538410,
            10543507,
            10572607,
            10573818,
            10587121,
            10765095,
            10766849,
            10774214,
            10810673,
            10814515,
            10817791,
            10551569,
        ];

        $resumeList = BaseResume::find()
            ->select([
                'id',
            ])
            ->where(['uuid' => $list])
            ->asArray()
            ->all();

        $listCount   = count($list);
        $resumeCount = count($resumeList);

        self::log('传入数量：' . $listCount . '，简历数量：' . $resumeCount);

        foreach ($resumeList as $item) {
            Resume::editTag($item['id'], [1]);

            self::log('简历ID：' . $item['id'] . '，处理成功！');
        }
    }

    /**
     * php timer_yii script/update-pi-announcement
     */
    public function actionUpdatePiAnnouncement()
    {
        $list = [
            55,
            266,
            499,
            502,
            503,
            514,
            525,
            526,
            535,
            538,
            541,
            543,
            616,
            625,
            635,
            721,
            722,
            723,
            724,
            725,
            726,
            1103,
            2757,
            2758,
            2771,
            2870,
            3200,
            3713,
            3718,
            4365,
            4401,
            5346,
            5350,
            5475,
            5527,
            5569,
            5582,
            6482,
            7312,
            7622,
            7626,
            7627,
            7628,
            7629,
            7630,
            7631,
            7634,
            7635,
            7636,
            7639,
            8472,
            8485,
            8631,
            8632,
            8634,
            8635,
            8636,
            8637,
            8638,
            8639,
            8640,
            9108,
            9110,
            9122,
            9132,
            9172,
            9174,
            9251,
            9685,
            9686,
            9688,
            9689,
            9690,
            9691,
            9694,
            10158,
            10237,
            10238,
            10241,
            10625,
            10627,
            11005,
            11021,
            11022,
            11023,
            11031,
            11039,
            11441,
            11450,
            11452,
            11453,
            11778,
            11779,
            11780,
            11782,
            11783,
            11784,
            11829,
            11830,
            12081,
            12220,
            12221,
            12222,
            12224,
            13012,
            13055,
            13121,
            13300,
            13303,
            13344,
            13357,
            13360,
            13361,
            13363,
            13365,
            13797,
            13832,
            14181,
            14189,
            14196,
            14198,
            14200,
            14203,
            14205,
            14206,
            14207,
            14589,
            14590,
            14591,
            15439,
            15452,
            15454,
            15464,
            15466,
            15468,
            15476,
            15479,
            15482,
            15487,
            15702,
            15792,
            15799,
            16056,
            16057,
            16089,
            16090,
            16197,
            16204,
            16426,
            16447,
            16448,
            16449,
            16456,
            16458,
            16463,
            16540,
            16586,
            16603,
            16819,
            16830,
            16831,
            16832,
            16840,
            16841,
            16842,
            17218,
            17219,
            17237,
            17239,
            17246,
            17574,
            17683,
            17930,
            17933,
            17934,
            18061,
            18063,
            18265,
            18959,
            18969,
            19053,
            19167,
            19174,
            19796,
            19797,
            19811,
            19823,
            20198,
            20200,
            20498,
            20582,
            20584,
            21546,
            21547,
            21548,
            22035,
            22037,
            22038,
            22041,
            22042,
            22043,
            23478,
            23486,
            23503,
            23504,
            23917,
            23918,
            23920,
            23991,
            24160,
            24623,
            24726,
            24970,
            25240,
            25592,
            25902,
            25908,
            26024,
            26205,
            26206,
            26463,
            26573,
            26812,
            27045,
            27836,
            27840,
            27858,
            28194,
            28457,
            28458,
            28459,
            28625,
            28626,
            28627,
            28822,
            28975,
            28977,
            28979,
            29044,
            29046,
            29409,
            29411,
            29728,
            29729,
            30081,
            30082,
            30084,
            31177,
            31179,
            31344,
            31622,
            31623,
            31624,
            31938,
            31939,
            32993,
            34153,
            35538,
            35828,
            36318,
            36561,
            36563,
            36565,
            36939,
            36940,
            37399,
            37401,
            37614,
            37616,
            37785,
            37786,
            38175,
            38178,
            38341,
            38621,
            38675,
            39083,
            39104,
            39806,
            40366,
            40373,
            40719,
            40838,
            41503,
            41533,
            41968,
            42385,
            42901,
            42905,
            43410,
            43438,
            44016,
            44017,
            46682,
            48299,
            48301,
            50194,
            50591,
            50592,
            50868,
            50973,
            52150,
            52158,
            52169,
            52442,
            53082,
            54270,
            55159,
            55800,
            55804,
            55806,
            56073,
            56082,
            56089,
            56096,
            56118,
            56187,
            56199,
            56623,
            56627,
            59258,
            60661,
            60662,
            61529,
            61535,
            61537,
            61541,
            61754,
            62287,
            62288,
            62713,
            63686,
            65008,
            65726,
            66674,
            68997,
            69215,
            69560,
            69563,
            69633,
            70836,
            71260,
            71635,
            71903,
            71904,
            72224,
            72548,
            73674,
            73841,
            74415,
            74419,
            75035,
            75815,
            75883,
            77052,
            77906,
            80030,
            80050,
            80693,
            80806,
            80847,
            80913,
            80939,
            80944,
            80988,
            81411,
            81959,
            81975,
            81976,
            82306,
            82453,
            82624,
            83416,
            85683,
            85860,
            87298,
            87814,
            87980,
            89185,
            89186,
            89571,
            89574,
            89648,
            89651,
            89979,
            89981,
            89982,
            89983,
            90047,
            91177,
            91653,
            91695,
            91807,
            91818,
            92402,
            93326,
            93329,
            93331,
            93335,
            93336,
            93338,
            93341,
            93351,
            93994,
            94021,
            94022,
            94023,
            94028,
            94029,
            94030,
            94147,
            95305,
            95321,
            95596,
            95631,
            95648,
            95687,
            95754,
            95762,
            95776,
            95779,
            95856,
            96707,
            96711,
            97919,
            98055,
            100901,
            102140,
            103624,
            104716,
            106485,
            106487,
            106841,
            106842,
            107252,
            107696,
            108040,
            109481,
            111922,
            111923,
            112067,
            112373,
            112374,
            112377,
            112378,
            112863,
            112864,
            112865,
            112867,
            113050,
            113331,
            113374,
            113408,
            113441,
            113516,
            113600,
            113670,
            113744,
            113745,
            113746,
            113798,
            113799,
            114231,
            114461,
            114462,
            114466,
            114701,
            114858,
            114859,
            115114,
            115889,
            116018,
            117688,
            117727,
            119803,
            120033,
            120034,
            120073,
            120146,
            120285,
            122884,
            122895,
            122907,
            123003,
            123045,
            123540,
            124379,
            125698,
            126978,
            127936,
            131027,
            131459,
            132826,
            132828,
            132931,
            133359,
            133360,
            133361,
            133362,
            133363,
            133364,
            133902,
            133968,
            133974,
            133980,
            134001,
            134028,
            134205,
            136108,
            136927,
            136928,
            137013,
            137327,
            138191,
            138278,
            139335,
            139373,
            139376,
            139988,
            140098,
            140761,
            140762,
            141247,
            141262,
            141907,
            142147,
            142225,
            142227,
            142239,
            142434,
            142501,
            143610,
            143645,
            143887,
            144063,
            144253,
            144281,
            144491,
            144505,
            144506,
            144948,
            144949,
            145407,
            145493,
            146322,
            146788,
            146789,
            146793,
            148071,
            148079,
            148096,
            148156,
            148680,
            148682,
            149110,
            149546,
            150004,
            150038,
            150046,
            150476,
            151664,
            152035,
            152044,
            152303,
            152746,
            152896,
            152897,
            152899,
            153148,
            153150,
            153160,
            153170,
            153177,
            153183,
            153186,
            153191,
            153198,
            153205,
            153206,
            153207,
            153208,
            153209,
            153210,
            153211,
            154341,
            154342,
            155228,
            155675,
            155987,
            156461,
            157459,
            157744,
            157757,
            158393,
            158581,
            158582,
            158584,
            158585,
            158586,
            158588,
            158595,
            158598,
            158604,
            158608,
            158764,
            158767,
            158769,
            158786,
            158950,
            159179,
            159189,
            159222,
            159228,
            159232,
            159260,
            159376,
            159412,
            159441,
            159506,
            159588,
            159605,
            159639,
            159666,
            159675,
            159718,
            159723,
            159846,
            159848,
            159910,
            159927,
            160005,
            160020,
            160110,
            160150,
            160151,
            160191,
            160234,
            160238,
            160243,
            160244,
            160245,
            160346,
            160356,
            160692,
            160848,
            160849,
            160851,
            160918,
            160919,
            160922,
            160968,
            160972,
            160995,
            161046,
            161047,
            161084,
            161102,
            161135,
            161249,
            161269,
            161349,
            161379,
            161381,
            161383,
            161419,
            161424,
            161477,
            161488,
            161490,
            161492,
            161513,
            161528,
            161541,
            161543,
            161545,
            161548,
            161564,
            161566,
            161655,
            161676,
            161753,
            161761,
            161764,
            161947,
            161955,
            162002,
            162004,
            162013,
            162016,
            162018,
            162019,
            162066,
            162072,
            162074,
            162099,
            162103,
            162110,
            162113,
            162117,
            162148,
            162150,
            162152,
            162181,
            162183,
            162184,
            162207,
            162244,
            162245,
            162253,
            162258,
            162286,
            162300,
            162327,
            162328,
            162330,
            162336,
            162340,
            162349,
            162358,
            162379,
            162380,
            162381,
            162439,
            162440,
            162441,
            162470,
            162472,
            162527,
            162601,
            162701,
            162837,
            162854,
            162879,
            162886,
            163045,
            163057,
            163084,
            163093,
            163181,
            163184,
            163185,
            163190,
            163433,
            163439,
            163441,
            163461,
            163464,
            163484,
            163499,
            163504,
            163509,
            163516,
            163517,
            163551,
            163558,
            163560,
            163561,
            163572,
            163673,
            163720,
            163759,
            163773,
            163888,
            163979,
            164012,
            164035,
            164044,
            164063,
            164097,
            164102,
            164111,
            164112,
            164154,
            164164,
            164193,
            164238,
            164287,
            164348,
            164368,
            164472,
            164476,
            164512,
            164575,
            164742,
            164760,
            164844,
            164859,
            164860,
            164874,
            164875,
            164936,
            164955,
            164961,
            164965,
            164973,
            164977,
            164985,
            164986,
            165123,
            165128,
            165184,
            165267,
            165309,
            165328,
            165344,
            165482,
            165627,
            165687,
            165789,
            165792,
            165863,
            165864,
            165891,
            166094,
            166138,
            166156,
            166175,
            166177,
            166191,
            166200,
            166267,
            166284,
            166319,
            166478,
            166503,
            166507,
            166780,
            166892,
            166914,
            167004,
            167008,
            167009,
            167028,
            167039,
            167061,
            167064,
            167065,
            167074,
            167109,
            167110,
            167124,
            167161,
            167172,
            167179,
            167180,
            167194,
            167204,
            167209,
            167256,
            167394,
            167414,
            167455,
            167491,
            167528,
            167605,
            167675,
            167736,
            167762,
            167777,
            167813,
            167837,
            167918,
            167966,
            167967,
            168078,
            168097,
            168117,
            168146,
            168147,
            168188,
            168293,
            168324,
            168326,
            168327,
            168474,
            168512,
            168653,
            168825,
            168869,
            168896,
            168906,
            168914,
            168971,
            169000,
            169048,
            169081,
            169094,
            169226,
            169233,
            169247,
            169296,
            169302,
            169305,
            169440,
            169443,
            169522,
            169530,
            169566,
            169570,
            169571,
            169649,
            169759,
            169789,
            169807,
            169898,
            169925,
            169933,
            170269,
            170287,
            170357,
            170367,
            170445,
            170470,
            170482,
            170484,
            170494,
            170517,
            170552,
            170640,
            170645,
            170657,
            170659,
            170666,
            170703,
            170723,
            170724,
            170751,
            170814,
            170818,
            170823,
            170908,
            170922,
            170923,
            170930,
            170952,
            170953,
            171027,
            171144,
            171198,
            171216,
            171269,
            171325,
            171345,
            171347,
            171372,
            171374,
            171445,
            171469,
            171488,
            171506,
            171514,
            171590,
            171624,
            171685,
            171775,
            172041,
            172109,
            172142,
            172169,
            172174,
            172183,
            172199,
            172210,
            172244,
            172265,
            172272,
            172273,
            172295,
            172337,
            172443,
            172444,
            172471,
            172525,
            172562,
            172686,
            172719,
            172753,
            172771,
            172777,
            172803,
            172830,
            172909,
            172920,
            172991,
            173023,
            173026,
            173036,
            173052,
            173057,
            173196,
            173305,
            173306,
            173323,
            173357,
            173359,
            173365,
            173396,
            173501,
            173570,
            173591,
            173617,
            173741,
            173743,
            173832,
            173836,
            173861,
            173944,
            173953,
            173956,
            173965,
            174055,
            174103,
            174199,
            174319,
            174338,
            174352,
            174400,
            174403,
            174409,
            174421,
            174444,
            174445,
            174451,
            174621,
            174683,
            174695,
            174745,
            174796,
            174813,
            174879,
            174911,
            174934,
            174935,
            174936,
            174990,
            175003,
            175004,
            175039,
            175060,
            175061,
            175124,
            175135,
            175136,
            175137,
            175153,
            175163,
            175181,
            175201,
            175213,
            175264,
            175294,
            175403,
            175409,
            175540,
            175597,
            175602,
            175608,
            175632,
            175633,
            175634,
            175641,
            175645,
            175651,
            175726,
            175744,
            175760,
            175808,
            175811,
            175863,
            175895,
            175898,
            176024,
            176044,
            176045,
            176046,
            176068,
            176071,
            176072,
            176219,
            176279,
            176310,
            176339,
            176497,
            176522,
            176729,
            176733,
            176780,
            176781,
            176802,
            176834,
            176836,
            176837,
            177000,
            177012,
            177016,
            177019,
            177056,
            177060,
            177061,
            177177,
            177255,
            177256,
            177589,
            177614,
            177643,
            177648,
            177672,
            177678,
            177741,
            177782,
            177820,
            177827,
            177877,
            177899,
            177904,
            177969,
            177995,
            177998,
            178018,
            178048,
            178120,
            178121,
            178138,
            178157,
            178183,
            178193,
            178235,
            178238,
            178254,
            178258,
            178272,
            178306,
            178311,
            178313,
            178364,
            178462,
            178479,
            178516,
            178617,
            178618,
            178626,
            178648,
            178707,
            178867,
            178872,
            178873,
            178874,
            178880,
            178952,
            178960,
            178971,
            178995,
            179082,
            179134,
            179246,
            179248,
            179358,
            179373,
            179376,
            179422,
            179473,
            179475,
            179555,
            179557,
            179599,
            179658,
            179661,
            179677,
            179729,
            179815,
            179946,
            180100,
            180132,
            180134,
            180137,
            180143,
            180145,
            180164,
            180211,
            180215,
            180367,
            180524,
            180533,
            180558,
            180598,
            180628,
            180682,
            180684,
            180687,
            180693,
            180705,
            180709,
            180725,
            180744,
            180858,
            180860,
            180888,
            180950,
            181024,
            181028,
            181042,
            181051,
            181052,
            181058,
            181113,
            181146,
            181202,
            181228,
            181391,
            181418,
            181443,
            181445,
            181453,
            181493,
            181523,
            181551,
            181581,
            181587,
            181638,
            181691,
            181751,
            181752,
            181795,
            181809,
            181841,
            181849,
            181868,
            181927,
            181940,
            181981,
            181985,
            181997,
            181998,
            181999,
            182000,
            182001,
            182020,
            182238,
            182256,
            182283,
            182288,
            182289,
            182290,
            182291,
            182366,
            182396,
            182464,
            182515,
            182534,
            182537,
            182553,
            182642,
            182681,
            182702,
            182760,
            182801,
            182868,
            183129,
            183149,
            183162,
            183221,
            183413,
            183591,
            183635,
            183678,
            183953,
            183957,
            183958,
            183970,
            184107,
            184197,
            184198,
            184238,
            184405,
            184466,
            184613,
            184626,
            184627,
            184637,
            184662,
            184668,
            184702,
            184703,
            184756,
            184786,
            184831,
            184832,
            184878,
            184879,
            184881,
            184895,
            184896,
            184899,
            184930,
            184975,
            185060,
            185069,
            185070,
            185103,
            185135,
            185136,
            185149,
            185166,
            185212,
            185229,
            185299,
            185301,
            185327,
            185328,
            185415,
            185436,
            185462,
            185488,
            185498,
            185599,
            185769,
            185877,
            185915,
            186108,
            186169,
            186237,
            186257,
            186330,
            186331,
            186357,
            186526,
            186534,
            186611,
            186710,
            186712,
            186718,
            186719,
            186723,
            186805,
            186849,
            186887,
            186899,
            186900,
            186926,
            186943,
            186955,
            187010,
            187048,
            187080,
            187084,
            187087,
            187147,
            187277,
            187280,
            187282,
            187303,
            187305,
            187372,
            187468,
            187479,
            187513,
            187557,
            187577,
            187592,
            187600,
            187602,
            187648,
            187650,
            187651,
            187711,
            187746,
            188060,
            188068,
            188072,
            188158,
            188312,
            188329,
            188338,
            188343,
            188377,
            188525,
            188527,
            188528,
            188529,
            188530,
            188531,
            188532,
            188580,
            188608,
            188609,
            188641,
            188765,
            188920,
            188970,
            189009,
            189025,
            189216,
            189341,
            189430,
            189454,
            189485,
            189486,
            189490,
            189501,
            189502,
            189567,
            189700,
            189778,
            189834,
            189851,
            189883,
            189913,
            189954,
            189988,
            189989,
            190000,
            190089,
            190133,
            190146,
            190227,
            190320,
            190332,
            190362,
            190363,
            190366,
            190377,
            190399,
            190458,
            190477,
            190478,
            190480,
            190494,
            190505,
            190511,
            190606,
            190631,
            190633,
            190645,
            190673,
            190690,
            190767,
            190768,
            190778,
            190811,
            190834,
            190837,
            190844,
            190868,
            190869,
            190895,
            190903,
            190911,
            190928,
            190930,
            190955,
            191066,
            191123,
            191186,
            191221,
            191223,
            191268,
            191273,
            191324,
            191382,
            191384,
            191435,
            191436,
            191568,
            191569,
            191578,
            191624,
            191645,
            191652,
            191657,
            191662,
            191663,
            191666,
            191688,
            191731,
            191741,
            191767,
            191834,
            191848,
            191906,
            191909,
            191964,
            191967,
            191983,
            192012,
            192016,
            192017,
            192027,
            192038,
            192074,
            192093,
            192117,
            192118,
            192156,
            192218,
            192244,
            192245,
            192323,
            192332,
            192333,
            192404,
            192535,
            192572,
            192606,
            192610,
            192650,
            192749,
            192756,
            192776,
            192842,
            192903,
            193033,
            193064,
            193074,
            193091,
            193102,
            193104,
            193208,
            193210,
            193433,
            193501,
            193543,
            193544,
            193629,
            193754,
            193773,
            193835,
            193921,
            193933,
            193948,
            193950,
            193982,
            194033,
            194096,
            194127,
            194284,
            194290,
            194296,
            194420,
            194431,
            194476,
            194487,
            194488,
            194513,
            194591,
            194601,
            194618,
            194621,
            194625,
            194632,
            194643,
            194649,
            194664,
            194700,
            194778,
            194878,
            194887,
            194984,
            195129,
            195149,
            195150,
            195180,
            195205,
            195279,
            195308,
            195341,
            195342,
            195382,
            195434,
            195479,
            195493,
            195510,
            195536,
            195566,
            195579,
            195581,
            195660,
            195675,
            195677,
            195700,
            195740,
            195759,
            195853,
            195855,
            195894,
            195984,
            196026,
            196029,
            196042,
            196048,
            196050,
            196102,
            196124,
            196256,
            196325,
            196376,
            196393,
            196495,
            196551,
            196552,
            196670,
            196701,
            196763,
            196764,
            196848,
            196857,
            196860,
            196990,
            197138,
            197139,
            197140,
            197189,
            197257,
            197278,
            197347,
            197348,
            197349,
            197502,
            197535,
            197573,
            197640,
            197711,
            197720,
            197734,
            197738,
            197768,
            197769,
            197787,
            197871,
            197920,
            197938,
            197973,
            198000,
            198022,
            198031,
            198048,
            198111,
            198340,
            198553,
            198554,
            198580,
            198590,
            198594,
            198611,
            198612,
            198633,
            198653,
            198658,
            198659,
            198679,
            198723,
            198724,
            198725,
            198837,
            198838,
            199005,
            199006,
            199010,
            199103,
            199150,
            199211,
            199212,
            199224,
            199270,
            199271,
            199332,
            199440,
            199441,
            199451,
            199453,
            199478,
            199489,
            199490,
            199511,
            199512,
            199513,
            199514,
            199545,
            199587,
            199592,
            199648,
            199653,
            199670,
            199699,
            199715,
            199717,
            199724,
            199725,
            199742,
            199783,
            199830,
            199833,
            199846,
            199901,
            199976,
            199984,
            199992,
            199998,
            200000,
            200004,
            200036,
            200051,
            200113,
            200151,
            200153,
            200161,
            200163,
            200366,
            200368,
            200446,
            200546,
            200608,
            200680,
            200770,
            200803,
            200914,
            200923,
            200975,
            201008,
            201073,
            201074,
            201082,
            201083,
            201224,
            201229,
            201245,
            201261,
            201262,
            201299,
            201323,
            201331,
            201335,
            201340,
            201360,
            201375,
            201394,
            201666,
            201777,
            201808,
            201810,
            201884,
            201905,
            201962,
            202027,
            202147,
            202155,
            202206,
            202249,
            202309,
            202340,
            202347,
            202356,
            202382,
            202415,
            202471,
            202491,
            202541,
            202596,
            202597,
            202635,
            202638,
            202675,
            202693,
            202745,
            202772,
            202842,
            202909,
            202933,
            202942,
            203009,
            203012,
            203082,
            203095,
            203118,
            203179,
            203270,
            203273,
            203277,
            203278,
            203281,
            203408,
            203469,
            203500,
            203502,
            203503,
            203560,
            203561,
            203571,
            203574,
            203596,
            203632,
            203705,
            203721,
            203739,
            203757,
            203762,
            203814,
            203825,
            203826,
            203839,
            203866,
            203873,
            203916,
            203929,
            203940,
            204000,
            204014,
            204015,
            204020,
            204038,
            204070,
            204127,
            204133,
            204156,
            204197,
            204338,
            204378,
            204431,
            204448,
            204501,
            204544,
            204591,
            204618,
            204646,
            204669,
            204673,
            204686,
            204749,
            204753,
            204823,
            204837,
            204848,
            204874,
            204944,
            204996,
            205034,
            205057,
            205066,
            205114,
            205163,
            205179,
            205268,
            205280,
            205283,
            205284,
            205333,
            205334,
            205359,
            205363,
            205420,
            205426,
            205560,
            205598,
            205627,
            205657,
            205806,
            206105,
            206162,
            206206,
            206258,
            206259,
            206268,
            206285,
            206296,
            206301,
            206350,
            206378,
            206398,
            206420,
            206436,
            206508,
            206588,
            206591,
            206638,
            206640,
            206685,
            206868,
            206870,
            206871,
            206872,
            206950,
            206951,
            206976,
            206987,
            206989,
            207060,
            207115,
            207157,
            207169,
            207183,
            207271,
            207362,
            207369,
            207453,
            207454,
            207456,
            207486,
            207487,
            207525,
            207526,
            207551,
            207617,
            207675,
            207707,
            207759,
            207761,
            207777,
            207786,
            207831,
            207841,
            207849,
            207851,
            207898,
            207924,
            207949,
            207993,
            207998,
            208100,
            208102,
            208130,
            208134,
            208201,
            208225,
            208239,
            208249,
            208257,
            208258,
            208259,
            208263,
            208304,
            208309,
            208321,
            208348,
            208363,
            208367,
            208379,
            208380,
            208384,
            208397,
            208415,
            208417,
            208428,
            208430,
            208470,
            208482,
            208486,
            208538,
            208548,
            208599,
            208602,
            208664,
            208670,
            208696,
            208698,
            208701,
            208784,
            208820,
            208831,
            208838,
            208839,
            208841,
            208845,
            208846,
            208852,
            208856,
            208857,
            208858,
            208859,
            208874,
            208875,
            208887,
            208989,
            209015,
            209140,
            209143,
            209294,
            209306,
            209326,
            209328,
            209329,
            209415,
            209425,
            209474,
            209517,
            209553,
            209593,
            209595,
            209596,
            209605,
            209608,
            209676,
            209679,
            209803,
            209831,
            209955,
            209964,
            209987,
            210042,
            210068,
            210075,
            210076,
            210134,
            210135,
            210162,
            210163,
            210187,
            210201,
            210202,
            210216,
            210258,
            210284,
            210289,
            210306,
            210427,
            210479,
            210523,
            210526,
            210581,
            210599,
            210637,
            210685,
            210754,
            210859,
            210861,
            210898,
            210899,
            210905,
            210927,
            210934,
            210936,
            211064,
            211152,
            211176,
            211177,
            211184,
            211293,
            211295,
            211301,
            211302,
            211332,
            211333,
            211393,
            211400,
            211401,
            211406,
            211410,
            211416,
            211417,
            211425,
            211440,
            211499,
            211503,
            211515,
            211529,
            211563,
            211580,
            211594,
            211616,
            211699,
            211806,
            211811,
            211825,
            211827,
            211829,
            211830,
            211831,
            211838,
            211839,
            211865,
            211900,
            211907,
            211925,
            211982,
            211985,
            211996,
            212014,
            212025,
            212117,
            212144,
            212224,
            212230,
            212269,
            212274,
            212277,
            212278,
            212281,
            212286,
            212288,
            212290,
            212305,
            212354,
            212361,
            212427,
            212440,
            212470,
            212595,
            212605,
            212629,
            212690,
            212780,
            212789,
            212809,
            212860,
            212864,
            213116,
            213132,
            213161,
            213165,
            213173,
            213220,
            213255,
            213259,
            213302,
            213303,
            213304,
            213498,
            213521,
            213523,
            213529,
            213535,
            213630,
            213636,
            213759,
            213771,
            213772,
            213773,
            213797,
            213865,
            213906,
            213910,
            213912,
            213966,
            214039,
            214100,
            214113,
            214148,
            214162,
            214181,
            214182,
            214183,
            214184,
            214185,
            214319,
            214329,
            214341,
            214342,
            214354,
            214393,
            214483,
            214505,
            214514,
            214572,
            214590,
            214728,
            214748,
            214749,
            214837,
            214859,
            214958,
            214959,
            215078,
            215100,
            215125,
            215202,
            215270,
            215282,
            215286,
            215287,
            215289,
            215290,
            215396,
            215406,
            215414,
            215418,
            215515,
            215516,
            215524,
            215530,
            215532,
            215566,
            215568,
            215588,
            215604,
            215606,
            215607,
            215646,
            215649,
            215655,
            215656,
            215676,
            215700,
            215704,
            215773,
            215774,
            215820,
            215870,
            215928,
            215932,
            215985,
            216009,
            216099,
            216107,
            216116,
            216181,
            216201,
            216249,
            216262,
            216263,
            216264,
            216265,
            216266,
            216271,
            216273,
            216274,
            216293,
            216295,
            216382,
            216390,
            216411,
            216412,
            216414,
            216419,
            216466,
            216551,
            216591,
            216600,
            216619,
            216621,
            216665,
            216669,
            216684,
            216685,
            216697,
            216701,
            216702,
            216703,
            216727,
            216731,
            216745,
            216888,
            216892,
            216959,
            217018,
            217045,
            217065,
            217108,
            217132,
            217146,
            217147,
            217159,
            217165,
            217205,
            217207,
            217222,
            217298,
            217300,
            217302,
            217303,
            217323,
            217369,
            217370,
            217484,
            217544,
            217546,
            217568,
            217605,
            217633,
            217722,
            217811,
            217819,
            217945,
            217956,
            218086,
            218124,
            218146,
            218235,
            218236,
            218249,
            218275,
            218303,
            218330,
            218365,
            218367,
            218380,
            218395,
            218414,
            218471,
            218561,
            218630,
            218633,
            218639,
            218648,
            218650,
            218657,
            218718,
            218723,
            218727,
            218757,
            218766,
            218776,
            218787,
            218796,
            218801,
            218803,
            218819,
            218827,
            218829,
            218893,
            218985,
            218994,
            219019,
            219020,
            219021,
            219079,
            219089,
            219097,
            219104,
            219134,
            219138,
            219139,
            219162,
            219175,
            219176,
            219178,
            219179,
            219198,
            219218,
            219224,
            219231,
            219264,
            219274,
            219281,
            219285,
            219286,
            219299,
            219323,
            219333,
            219335,
            219351,
            219357,
            219375,
            219377,
            219403,
            219445,
            219459,
            219473,
            219474,
            219476,
            219481,
            219518,
            219604,
            219644,
            219680,
            219725,
            219733,
            219761,
            219766,
            219773,
            219788,
            219918,
            219949,
            220046,
            220054,
            220055,
            220110,
            220112,
            220113,
            220122,
            220149,
            220162,
            220245,
            220259,
            220283,
            220381,
            220443,
            220449,
            220481,
            220591,
            220724,
            220725,
            220726,
            220734,
            220736,
            220737,
            220738,
            220740,
            220751,
            220752,
            220753,
            220754,
            220774,
            220775,
            220776,
            220777,
            220778,
            220779,
            220780,
            220781,
            220828,
            220833,
            220834,
            220835,
            220836,
            220840,
            220843,
            220844,
            220847,
            220912,
            220913,
            220938,
            220967,
            220970,
            221016,
            221018,
            221024,
            221052,
            221055,
            221067,
            221112,
            221118,
            221137,
            221234,
            221369,
            221371,
            221372,
            221404,
            221420,
            221437,
            221461,
            221463,
            221464,
            221497,
            221528,
            221591,
            221593,
            221594,
            221603,
            221639,
            221641,
            221724,
            221772,
            221841,
            221870,
            221886,
            221892,
            221914,
            221917,
            221918,
            221928,
            221932,
            221935,
            221937,
            221949,
            222017,
            222024,
            222028,
            222031,
            222047,
            222048,
            222065,
            222066,
            222117,
            222118,
            222125,
            222139,
            222149,
            222150,
            222152,
            222172,
            222174,
            222221,
            222222,
            222224,
            222228,
            222295,
            222394,
            222520,
            222521,
            222539,
            222555,
            222583,
            222586,
            222652,
            222654,
            222679,
            222705,
            222739,
            222741,
            222751,
            222849,
            222850,
            222865,
            222869,
            222882,
            222886,
            222888,
            222891,
            222897,
            222903,
            222908,
            222909,
            222914,
            222915,
            222916,
            222947,
            222948,
            222954,
            222982,
            223019,
            223092,
            223093,
            223105,
            223120,
            223159,
            223160,
            223173,
            223193,
            223195,
            223205,
            223212,
            223216,
            223217,
            223224,
            223241,
            223242,
            223260,
            223283,
            223303,
            223304,
            223327,
            223355,
            223408,
            223411,
            223414,
            223478,
            223481,
            223538,
            223619,
            223651,
            223657,
            223700,
            223730,
            223779,
            223868,
            223919,
            224043,
            224083,
            224133,
            224179,
            224191,
            224192,
            224210,
            224280,
            224281,
            224294,
            224312,
            224369,
            224392,
            224408,
            224440,
            224513,
            224551,
            224563,
            224640,
            224682,
            224732,
            224741,
            224765,
            224805,
            224810,
            224822,
            224832,
            224903,
            224936,
            224991,
            224995,
            225020,
            225119,
            225121,
            225124,
            225132,
            225133,
            225177,
            225210,
            225321,
            225338,
            225344,
            225355,
            225357,
            225363,
            225365,
            225377,
            225387,
            225392,
            225424,
            225477,
            225495,
            225506,
            225517,
            225525,
            225528,
            225536,
            225573,
            225574,
            225636,
            225655,
            225658,
            225664,
            225671,
            225710,
            225711,
            225712,
            225751,
            225752,
            225781,
            225799,
            225812,
            225815,
            225859,
            225895,
            225923,
            225931,
            225948,
            226041,
            226042,
            226061,
            226151,
            226242,
            226289,
            226366,
            226386,
            226391,
            226392,
            226395,
            226400,
            226401,
            226458,
            226470,
            226517,
            226537,
            226573,
            226575,
            226582,
            226642,
            226673,
            226675,
            226676,
            226709,
            226715,
            226718,
            226737,
            226738,
            226790,
            226843,
            226847,
            226855,
            226890,
            226968,
            226972,
            227075,
            227094,
            227166,
            227177,
            227229,
            227330,
            227353,
            227373,
            227386,
            227425,
            227496,
            227527,
            227528,
            227550,
            227586,
            227645,
            227886,
            227887,
            227896,
            227953,
            227961,
            227987,
            228049,
            228056,
            228058,
            228068,
            228085,
            228118,
            228126,
            228137,
            228140,
            228175,
            228198,
            228202,
            228239,
            228257,
            228260,
            228290,
            228299,
            228325,
            228328,
            228339,
            228379,
            228423,
            228450,
            228486,
            228513,
            228526,
            228548,
            228588,
            228630,
            228636,
            228667,
            228682,
            228689,
            228820,
            228843,
            228844,
            228846,
            228888,
            228948,
            228958,
            228985,
            229008,
            229040,
            229048,
            229060,
            229062,
            229069,
            229096,
            229103,
            229105,
            229305,
            229344,
            229345,
            229366,
            229384,
            229405,
            229408,
            229411,
            229430,
            229451,
            229460,
            229461,
            229469,
            229475,
            229522,
            229523,
            229534,
            229589,
            229599,
            229631,
            229685,
            229751,
            229810,
            229876,
            229936,
            229953,
            229961,
            229962,
            230006,
            230008,
            230013,
            230015,
            230017,
            230026,
            230027,
            230038,
            230040,
            230041,
            230046,
            230050,
            230070,
            230072,
            230090,
            230134,
            230137,
            230153,
            230156,
            230160,
            230162,
            230179,
            230181,
            230190,
            230198,
            230228,
            230229,
            230273,
            230328,
            230429,
            230470,
            230547,
            230560,
            230579,
            230596,
            230652,
            230682,
            230744,
            230764,
            230802,
            230803,
            230820,
            230823,
            230837,
            230844,
            230846,
            230849,
            230857,
            230864,
            230876,
            230910,
            230937,
            230941,
            230987,
            231055,
            231065,
            231114,
            231124,
            231125,
            231132,
            231169,
            231171,
            231174,
            231175,
            231176,
            231177,
            231250,
            231251,
            231256,
            231409,
            231445,
            231446,
            231474,
            231587,
            231605,
            231614,
            231619,
            231630,
            231681,
            231692,
            231697,
            231699,
            231710,
            231721,
            231817,
            231864,
            231889,
            231963,
            231998,
            232045,
            232058,
            232063,
            232103,
            232118,
            232119,
            232120,
            232239,
            232248,
            232308,
            232309,
            232388,
            232436,
            232471,
            232472,
            232476,
            232477,
            232478,
            232484,
            232561,
            232562,
            232564,
            232614,
            232641,
            232716,
            232717,
            232720,
            232781,
            232890,
            232893,
            232922,
            232958,
            233358,
            233362,
            233365,
            233380,
            233474,
            233520,
            233560,
            233590,
            233684,
            233687,
            233722,
            233821,
            233868,
            233902,
            233945,
            233948,
            233951,
            233966,
            233970,
            233976,
            233989,
            233997,
            233998,
            234025,
            234035,
            234036,
            234046,
            234227,
            234289,
            234298,
            234332,
            234379,
            234445,
            234601,
            234631,
            234632,
            234721,
            234723,
            234758,
            234824,
            234900,
            235019,
            235038,
            235071,
            235172,
            235218,
            235411,
            235476,
            235558,
            235593,
            235635,
            235655,
            235684,
            235699,
            235701,
            235741,
            235748,
            235749,
            235767,
            235780,
            235781,
            235797,
            235837,
            235863,
            235873,
            235884,
            235887,
            235916,
            235937,
            235986,
            236021,
            236035,
            236048,
            236056,
            236073,
            236078,
            236109,
            236161,
            236233,
            236246,
            236319,
            236324,
            236337,
            236356,
            236431,
            236455,
            236458,
            236462,
            236480,
            236481,
            236506,
            236516,
            236523,
            236652,
            236699,
            236803,
            236863,
            236869,
            236905,
            236906,
            236925,
            236931,
            236946,
            237141,
            237287,
            237342,
            237354,
            237355,
            237380,
            237461,
            237473,
            237482,
            237492,
            237503,
            237597,
            237599,
            237694,
            237695,
            237734,
            237735,
            237736,
            237737,
            237742,
            237774,
            238023,
            238030,
            238033,
            238479,
            238483,
            238486,
            238636,
            238660,
            238807,
            239043,
            239201,
            239202,
            239306,
            239326,
            239337,
            239340,
            239511,
            239593,
            239809,
            239892,
            239912,
            239990,
            240011,
            240036,
            240234,
            240380,
            241183,
            241184,
            241189,
            241341,
            241351,
            241439,
            241632,
            241825,
            241855,
            241908,
            241947,
            241949,
            242059,
            242127,
            242140,
            242197,
            242214,
            242265,
            242312,
            242349,
            242373,
            242551,
            242580,
            242651,
            242652,
            242653,
            242654,
            242792,
            242852,
            242891,
            242960,
            242963,
            242973,
            243016,
            243078,
            243116,
            243123,
            243201,
            243440,
            243453,
            243455,
            243457,
            243493,
            243494,
            243572,
            243651,
            243776,
            243806,
            243894,
            244222,
            244283,
            244300,
            244407,
            244410,
            244533,
            244562,
            244582,
            244584,
            244598,
            244651,
            244652,
            244670,
            244687,
            244858,
            244860,
            244944,
            244958,
            244959,
            244975,
            244982,
            244997,
            245049,
            245059,
            245101,
            245104,
            245116,
            245147,
            245196,
            245245,
            245284,
            245287,
            245291,
            245378,
            245386,
            245453,
            245514,
            245526,
            245530,
            245704,
            245783,
            245855,
            245943,
            245971,
            246129,
            246168,
            246215,
            246219,
            246220,
            246328,
            246336,
            246371,
            246388,
            246459,
            246466,
            246484,
            246488,
            246509,
            246538,
            246559,
            246577,
            246699,
            246702,
            246817,
            246823,
            246870,
            246887,
            246936,
            246951,
            246958,
            246979,
            246981,
            246983,
            247059,
            247068,
            247221,
            247223,
            247230,
            247400,
            247401,
            247402,
            247403,
            247404,
            247405,
            247421,
            247445,
            247483,
            247543,
            247573,
            247608,
            247781,
            247867,
            247988,
            248119,
            248126,
            248157,
            248253,
            248254,
            248340,
            248375,
            248481,
            248770,
            249211,
            249244,
            249245,
            249373,
            249385,
            249472,
            249511,
            249656,
            249711,
            249714,
            249716,
            249906,
            249968,
            250011,
            250019,
            250098,
            250099,
            250194,
            250199,
            250246,
            250265,
            250275,
            250290,
            250363,
            250436,
            250555,
            250556,
            250595,
            250677,
            250947,
            250983,
            250995,
            250996,
            251033,
            251088,
            251093,
            251119,
            251128,
            251146,
            251173,
            251181,
            251188,
            251204,
            251242,
            251251,
            251299,
            251323,
            251346,
            251389,
            251435,
            251459,
            251484,
            251490,
            251496,
            251566,
            251568,
            251671,
            251685,
            251916,
            251959,
            251989,
            251995,
            252046,
            252089,
            252173,
            252203,
            252209,
            252303,
            252316,
            252554,
            252666,
            252708,
            252737,
            252746,
            252806,
            252824,
            252828,
            252832,
            252936,
            253024,
            253029,
            253058,
            253106,
            253119,
            253147,
            253155,
            253156,
            253168,
            253190,
            253213,
            253259,
            253262,
            253293,
            253345,
            253348,
            253349,
            253352,
            253353,
            253364,
            253380,
            253392,
            253409,
            253427,
            253564,
            253574,
            253593,
            253805,
            253824,
            253905,
            253922,
            253960,
            254026,
            254101,
            254124,
            254189,
            254239,
            254430,
            254475,
            254529,
            254554,
            254647,
            254823,
            254923,
            255038,
            255065,
            255067,
            255087,
            255088,
            255089,
            255090,
            255104,
            255204,
            255294,
            255298,
            255359,
            255462,
            255496,
            255500,
            255502,
            255520,
            255548,
            255623,
            255734,
            255791,
            255814,
            255990,
            256032,
            256048,
            256103,
            256122,
            256275,
            256282,
            256330,
            256351,
            256433,
            256436,
            256504,
            256561,
            256785,
            256798,
            256832,
            256833,
            256871,
            256917,
            257125,
            257312,
            257344,
            257409,
            257428,
            257506,
            257507,
            257539,
            257541,
            257545,
            257555,
            257556,
            257576,
            257597,
            257623,
            257624,
            257640,
            257641,
            257642,
            257672,
            257699,
            257700,
            257702,
            257726,
            257734,
            257748,
            257763,
            257775,
            257788,
            257870,
            257953,
            257965,
            257976,
            257978,
            257979,
            258092,
            258111,
            258216,
            258260,
            258298,
            258328,
            258425,
            258507,
            258508,
            258509,
            258547,
            258570,
            258581,
            258652,
            258678,
            258680,
            258716,
            258756,
            258757,
            258775,
            258792,
            258794,
            258857,
            258858,
            258892,
            258896,
            258904,
            258938,
            258952,
            258956,
            259047,
            259097,
            259107,
            259188,
            259190,
            259192,
            259193,
            259218,
            259224,
            259269,
            259308,
            259367,
            259410,
            259412,
            259434,
            259488,
            259494,
            259501,
            259561,
            259576,
            259595,
            259754,
            259755,
            259760,
            259764,
            259765,
            259801,
            259810,
            259819,
            259821,
            259859,
            259879,
            259880,
            259947,
            259958,
            259959,
            259960,
            260019,
            260024,
            260027,
            260075,
            260076,
            260077,
            260119,
            260135,
            260138,
            260141,
            260155,
            260159,
            260172,
            260185,
            260186,
            260187,
            260192,
            260193,
            260194,
            260218,
            260225,
            260228,
            260230,
            260276,
            260291,
            260292,
            260311,
            260312,
            260341,
            260379,
            260380,
            260426,
            260435,
            260438,
            260519,
            260646,
            260814,
            260877,
            260951,
            261052,
            261187,
            261190,
            261202,
            261233,
            261331,
            261361,
            261431,
            261554,
            261687,
            261724,
            261797,
            261800,
            261841,
            261949,
            261967,
            261968,
            262069,
            262121,
            262122,
            262309,
            262387,
            262388,
            262390,
            262446,
            262448,
            262468,
            262474,
            262496,
            262535,
            262552,
            262578,
            262633,
            262634,
            262648,
            262672,
            262758,
            262799,
            262806,
            262867,
            262955,
            262973,
            263003,
            263004,
            263060,
            263064,
            263122,
            263123,
            263343,
            263350,
            263493,
            263534,
            263539,
            263597,
            263622,
            263674,
            263675,
            263686,
            263688,
            263691,
            263699,
            263715,
            263722,
            263759,
            263761,
            263772,
            263791,
            263819,
            263890,
            263891,
            263945,
            264021,
            264025,
            264031,
            264052,
            264063,
            264078,
            264083,
            264098,
            264207,
            264218,
            264243,
            264274,
            264301,
            264411,
            264455,
            264492,
            264500,
            264573,
            264582,
            264605,
            264718,
            264743,
            264809,
            264842,
            264846,
            264888,
            264893,
            265033,
            265041,
            265127,
            265129,
            265182,
            265231,
            265251,
            265266,
            265340,
            265356,
        ];

        // 循环
        $count = count($list);

        foreach ($list as $k => $item) {
            // 找到article_id
            $articleId = BaseAnnouncement::findOneVal(['id' => $item], 'article_id');
            if (!$articleId) {
                self::log($item . '不存在');
                continue;
            }

            $model             = new BaseArticleAttribute();
            $model->article_id = $articleId;
            $model->type       = BaseArticleAttribute::ATTRIBUTE_PI;
            $model->sort_time  = date('Y-m-d H:i:s');
            if (!$model->save()) {
                self::log('保存失败');
            }

            $percent = round(($k + 1) / $count * 100, 2);

            self::log('第' . ($k + 1) . '/' . $count . '个公告，' . $percent . '%');

            // 成功后入队
            Producer::afterAnnouncementUpdateJob($item);
        }
    }

    /**
     * php timer_yii script/update-pi-company
     * @return void
     */
    public function actionUpdatePiCompany()
    {
        $list = [
            20106474,
            20106470,
            20105931,
            20105709,
            20105612,
            20105431,
            20105156,
            20105121,
            20105052,
            20105040,
            20104733,
            20104643,
            20104300,
            20104292,
            20104114,
            20103971,
            20103870,
            20103214,
            20102952,
            20102849,
            20102846,
            20102840,
            20102713,
            20102602,
            20102553,
            20102375,
            20102191,
            20102190,
            20101721,
            20101609,
            20101481,
            20101152,
            20101129,
            20101041,
            20100904,
            20100876,
            20100107,
            20099970,
            20099968,
            20099863,
            20099661,
            20099623,
            20098992,
            20098927,
            20098825,
            20098746,
            20098582,
            20098578,
            20098320,
            20098216,
            20098213,
            20097797,
            20097766,
            20097694,
            20097592,
            20097503,
            20097499,
            20097470,
            20097350,
            20097019,
            20096814,
            20096688,
            20096687,
            20096570,
            20096119,
            20095805,
            20095391,
            20094922,
            20094920,
            20094440,
            20094171,
            20093921,
            20093775,
            20093610,
            20093424,
            20092833,
            20092747,
            20092519,
            20092298,
            20092174,
            20092170,
            20091999,
            20091803,
            20091802,
            20091687,
            20091686,
            20091561,
            20091432,
            20091303,
            20091287,
            20091274,
            20091270,
            20091148,
            20090893,
            20090427,
            20090318,
            20090136,
            20090134,
            20090059,
            20088965,
            20088748,
            20088484,
            20088479,
            20088125,
            20088052,
            20087511,
            20087085,
            20086810,
            20085996,
            20085791,
            20085489,
            20084945,
            20084662,
            20084618,
            20084488,
            20084278,
            20083840,
            20083417,
            20083376,
            20083310,
            20083233,
            20082387,
            20082104,
            20080625,
            20080607,
            20080570,
            20080320,
            20080303,
            20080168,
            20080046,
            20079945,
            20079548,
            20079544,
            20079542,
            20078867,
            20078410,
            20077535,
            20077475,
            20077349,
            20077034,
            20076442,
            20076116,
            20075972,
            20075321,
            20075114,
            20075099,
            20074714,
            20074712,
            20074697,
            20074404,
            20074016,
            20073991,
            20073414,
            20073246,
            20072818,
            20072704,
            20072687,
            20071253,
            20071114,
            20070506,
            20070503,
            20070272,
            20070196,
            20069925,
            20069601,
            20069448,
            20069347,
            20068443,
            20067480,
            20065659,
            20065612,
            20065349,
            20065283,
            20065096,
            20064631,
            20064489,
            20064487,
            20062889,
            20062745,
            20061766,
            20061724,
            20061044,
            20061011,
            20059638,
            20059454,
            20059274,
            20058480,
            20058304,
            20058303,
            20056631,
            20056455,
            20056431,
            20055958,
            20055944,
            20055301,
            20055091,
            20055006,
            20054189,
            20054188,
            20053854,
            20053713,
            20053692,
            20053282,
            20053203,
            20052499,
            20051792,
            20051268,
            20050221,
            20049632,
            20049347,
            20049109,
            20049089,
            20048867,
            20048672,
            20048274,
            20048237,
            20048016,
            20047393,
            20046557,
            20045265,
            20043826,
            20041917,
            20041578,
            20040444,
            20040083,
            20039522,
            20038254,
            20038051,
            20036863,
            20036858,
            20036479,
            20034298,
            20033604,
            20033362,
            20033200,
            20032778,
            20032506,
            20032399,
            20032011,
            20031137,
            20030165,
            20029166,
            20028310,
            20027910,
            20027412,
            20026889,
            20026687,
            20025716,
            20024696,
            20024124,
            20024014,
            20023049,
            20023018,
            20021073,
            20020058,
            20019428,
            20019415,
            20019216,
            20019176,
            20018222,
            20017953,
            20017807,
            20017462,
            20017035,
            20016585,
            20016547,
            20016546,
            20016542,
            20016351,
            20015041,
            20015040,
            20014571,
            20014522,
            20013719,
            20013599,
            20012972,
            20012900,
            20011908,
            20011867,
            20011161,
            20011131,
            20010834,
            20010259,
            20009380,
            20008525,
            20008261,
            20007917,
            20007456,
            20007415,
            20006900,
            20006770,
            20006598,
            20006358,
            20005731,
            20005369,
            20004850,
            20004849,
            20004257,
            20004255,
            20003673,
            20003223,
            20003221,
            20003148,
            20001317,
            20001312,
            20001256,
            20001255,
            20000958,
            20000952,
            20000923,
            20000912,
            20000640,
            20000601,
            20000599,
            20000561,
            20000545,
            20000485,
            20000479,
            20000460,
            20000410,
            20000378,
            20000340,
            20000339,
            20000328,
            20000327,
            20000320,
            20000319,
            20000315,
            20000314,
            20000304,
            20000294,
            20000251,
            20000217,
            20000214,
            20000213,
            20000211,
            20000210,
            20000194,
            20000096,
            20000086,
            20000021,
        ];

        $count = count($list);
        self::log('一共' . count($list) . '个公司');
        foreach ($list as $k => $item) {
            // 运行到第几/x个，还有百分比
            $companyId = BaseCompany::findOneVal(['uuid' => $item], 'id');
            if (!$companyId) {
                self::log('公司不存在：' . $item);
                continue;
            }
            Company::editCompanyFeaturedTag($companyId, [1]);

            $percent = round(($k + 1) / $count * 100, 2);

            self::log('第' . ($k + 1) . '/' . $count . '个公司，' . $percent . '%');
        }
    }

    /**
     * 临时处理一写公告没有进after的逻辑
     * php timer_yii script/temp-announcement-after
     */
    public function actionTempAnnouncementAfter()
    {
        $annIds = BaseAnnouncement::find()
            ->alias('a')
            ->select('a.id')
            ->innerJoin(['art' => BaseArticle::tableName()], 'a.article_id=art.id')
            ->leftJoin(['aar' => BaseAnnouncementAreaRelation::tableName()], 'a.id=aar.announcement_id')
            ->where([
                'art.status'    => [
                    1,
                    2,
                ],
                'art.is_delete' => BaseArticle::IS_DELETE_NO,
                'aar.area_id'   => null,
            ])
            ->asArray()
            ->column();
        foreach ($annIds as $id) {
            Producer::afterAnnouncementUpdateJob($id);
            self::log('处理公告：' . $id);
        }
    }

    /**
     * 全量公告更新脚本
     * php timer_yii script/all-announcement-update-data
     */
    public function actionAllAnnouncementUpdateData()
    {
        $id = Cache::get('announcement_update_id');
        if (!$id) {
            $id = 0;
        }
        $annIds = BaseAnnouncement::find()
            ->alias('a')
            ->select('a.id')
            ->innerJoin(['art' => BaseArticle::tableName()], 'a.article_id=art.id')
            ->where([
                'art.status' => [
                    1,
                    2,
                ],
            ])
            ->andWhere([
                '>',
                'a.id',
                $id,
            ])
            ->limit(10000)
            ->orderBy('a.id asc')
            ->asArray()
            ->column();
        if (count($annIds) == 0) {
            Cache::set('announcement_update_id', 0);
        }
        foreach ($annIds as $id) {
            Producer::afterAnnouncementUpdateJob($id);
            Cache::set('announcement_update_id', $id);
            self::log('处理公告：' . $id);
        }
        self::log('处理完毕');
    }

    // 更新职位搜索名称
    // php timer_yii script/update-job-search-name
    public function actionUpdateJobSearchName()
    {
        $id = Cache::get('job_search_name_id');

        if (!$id) {
            $id = 0;
        }

        // 每次1w
        $jobIds = BaseJob::find()
            ->select('id')
            ->where([
                '>',
                'id',
                $id,
            ])
            ->andWhere(['search_name' => ''])
            ->limit(100000)
            ->orderBy('id asc')
            ->asArray()
            ->column();

        if (count($jobIds) == 0) {
            Cache::set('job_search_name_id', 0);
        }

        $count = count($jobIds);

        foreach ($jobIds as $k => $jobId) {
            $sql = "UPDATE job j
JOIN company c ON j.company_id = c.id
LEFT JOIN announcement a ON a.id = j.announcement_id
SET j.search_name = CONCAT(j.name, ' ', c.full_name, ' ', j.department, ' ', IFNULL(a.title, '')) WHERE j.id = $jobId";
            Yii::$app->db->createCommand($sql)
                ->execute();
            Cache::set('job_search_name_id', $jobId);
            // 百分比 保留两位小数
            $percent = round(($k + 1) / $count * 100, 2);
            self::log("$k/$count 处理职位id：$jobId / $percent");
        }

        self::log('处理完毕');
    }

    /**
     * @return void
     * php timer_yii script/delete-article-column
     */
    public function actionDeleteArticleColumn()
    {
        $articleIds = [
            235,
            773,
            77867,
            120742,
            153653,
            167089,
            168989,
            169781,
            174068,
            177800,
            180518,
            182053,
            182085,
            182426,
            183874,
            184999,
            187222,
            189428,
            189961,
            190512,
            191256,
            191516,
            196495,
            196599,
            196629,
            198234,
            198502,
            199572,
            199903,
            200502,
            200527,
            200562,
            202927,
            204252,
            204624,
            204627,
            204837,
            204883,
            204941,
            205228,
            205256,
            205721,
            205776,
            206049,
            206548,
            206805,
            208545,
            208593,
            209121,
            209747,
            210890,
            211812,
            211845,
            213197,
            214351,
            215458,
            215508,
            216105,
            216513,
            223836,
            224667,
            225393,
            225730,
            226597,
            227142,
            229395,
            235239,
            238050,
            239576,
            241078,
            241328,
            243839,
            244295,
            246406,
            247439,
            253023,
            255115,
            255413,
            255654,
            255772,
            255791,
            256661,
            256893,
            257115,
            257238,
            258883,
            259773,
            260316,
            260395,
            260847,
            260877,
            264253,
            264692,
            265621,
            266036,
            266210,
            266847,
            267814,
            268367,
            268934,
            269148,
            271058,
            271227,
            271427,
            271428,
            271703,
            272065,
            272510,
            272577,
            272875,
            272880,
            273313,
            273523,
            273534,
            273618,
            273620,
            274263,
            274870,
            275206,
            275438,
            275586,
            275715,
            275779,
            275780,
            275783,
            275784,
            275788,
            275792,
            275793,
            275802,
            275809,
            275811,
            275812,
            275824,
            275836,
            275840,
            275842,
            275843,
        ];
        $aids       = BaseAnnouncement::find()
            ->select([
                'id',
            ])
            ->andWhere(['article_id' => $articleIds])
            ->asArray()
            ->column();
        //BaseArticleColumn::deleteAll(['article_id' => $articleIds]);

        foreach ($aids as $aid) {
            Producer::afterAnnouncementUpdateJob($aid);
        }
    }

    /**
     * 统计公告日点击量
     * php timer_yii script/update-announcement-click-total-daily
     */
    public function actionUpdateAnnouncementClickTotalDaily()
    {
        // 找到第一条数据
        $addTime = BaseArticleClickLog::find()
            ->select('add_time')
            ->orderBy('add_time asc')
            ->limit(1)
            ->scalar();

        $date = substr($addTime, 0, 10);

        // 昨天
        $yesterday = TimeHelper::getYesterday();

        // 循环到昨天
        while ($date <= $yesterday) {
            self::log("开始处理日期:{$date}");
            BaseAnnouncementClickTotalDaily::updateDate($date);
            $date = date('Y-m-d', strtotime($date . ' +1 day'));
            self::log("处理日期:{$date}完成");
        }
    }

    /**
     * 修复单位附属表
     * php timer_yii script/company-stata-data-total
     */
    public function actionCompanyStataDataTotal()
    {
        $companyIds = BaseCompany::find()
            ->select('id')
            //            ->where(['status' => 1])
            //            ->where(['id' => 72555])
            ->orderBy('id asc')
            ->column();
        foreach ($companyIds as $companyId) {
            $info = BaseCompanyStatData::findOne(['company_id' => $companyId]);
            if ($info) {
                $info->online_announcement_count = BaseAnnouncement::find()
                    ->alias('a')
                    ->leftJoin(['art' => BaseArticle::tableName()], 'a.article_id=art.id')
                    ->where([
                        'art.status'   => 1,
                        'art.is_show'  => 1,
                        'a.company_id' => $companyId,
                    ])
                    ->count();
                $info->all_announcement_count    = BaseAnnouncement::find()
                    ->alias('a')
                    ->leftJoin(['art' => BaseArticle::tableName()], 'a.article_id=art.id')
                    ->where([
                        'art.status'   => [
                            1,
                            2,
                        ],
                        'art.is_show'  => 1,
                        'a.company_id' => $companyId,
                    ])
                    ->count();
                $info->online_job_count          = BaseJob::find()
                    ->where([
                        'status'     => 1,
                        'is_show'    => 1,
                        'company_id' => $companyId,
                    ])
                    ->count();
                $info->all_job_count             = BaseJob::find()
                    ->where([
                        'status'     => [
                            0,
                            1,
                        ],
                        'is_show'    => 1,
                        'company_id' => $companyId,
                    ])
                    ->count();
                $info->save();
                self::log('单位ID:' . $companyId);
            }
        }
        self::log('已处理完毕');
    }

    /**
     * 修复单位附属表
     * php timer_yii script/announcement-job-total
     */
    public function actionAnnouncementJobTotal()
    {
        $Ids = BaseAnnouncement::find()
            ->select('id')
            ->orderBy('id asc')
            ->column();
        foreach ($Ids as $id) {
            $info = BaseAnnouncement::findOne($id);

            $info->online_job_amount = BaseJob::find()
                ->where([
                    'status'          => 1,
                    'is_show'         => 1,
                    'announcement_id' => $id,
                ])
                ->count();
            $info->all_job_amount    = BaseJob::find()
                ->where([
                    'status'          => [
                        0,
                        1,
                    ],
                    'is_show'         => 1,
                    'announcement_id' => $id,
                ])
                ->count();
            $info->save();
            self::log('公告ID:' . $id);
        }
        self::log('已处理完毕');
    }

    /**
     * 修复单位附属表
     * php timer_yii script/company-stata-data-total-one-time
     */
    public function actionCompanyStataDataTotalOneTime()
    {
        $company = BaseCompanyStatData::find()
            ->alias('csd')
            ->select([
                'csd.company_id',
                'csd.online_job_count',
                'csd.all_job_count',
                't'  => BaseJob::find()
                    ->alias('tj')
                    ->select('count(tj.id)')
                    ->andWhere('tj.status=1 and tj.is_show=1 and csd.company_id=tj.company_id'),
                'tt' => BaseJob::find()
                    ->alias('ttj')
                    ->select('count(ttj.id)')
                    ->andWhere('ttj.status in (0,1) and ttj.is_show=1 and csd.company_id=ttj.company_id'),
            ])
            ->having('t!=csd.online_job_count or tt!=csd.all_job_count')
            ->asArray()
            ->all();
        foreach ($company as $item) {
            $info = BaseCompanyStatData::findOne(['company_id' => $item['company_id']]);
            if ($info) {
                $info->online_announcement_count = BaseAnnouncement::find()
                    ->alias('a')
                    ->leftJoin(['art' => BaseArticle::tableName()], 'a.article_id=art.id')
                    ->where([
                        'art.status'   => 1,
                        'art.is_show'  => 1,
                        'a.company_id' => $item['company_id'],
                    ])
                    ->count();
                $info->all_announcement_count    = BaseAnnouncement::find()
                    ->alias('a')
                    ->leftJoin(['art' => BaseArticle::tableName()], 'a.article_id=art.id')
                    ->where([
                        'art.status'   => [
                            1,
                            2,
                        ],
                        'art.is_show'  => 1,
                        'a.company_id' => $item['company_id'],
                    ])
                    ->count();
                $info->online_job_count          = BaseJob::find()
                    ->where([
                        'status'     => 1,
                        'is_show'    => 1,
                        'company_id' => $item['company_id'],
                    ])
                    ->count();
                $info->all_job_count             = BaseJob::find()
                    ->where([
                        'status'     => [
                            0,
                            1,
                        ],
                        'is_show'    => 1,
                        'company_id' => $item['company_id'],
                    ])
                    ->count();
                $info->save();
                self::log('单位ID:' . $item['company_id']);
            }
        }
        self::log('已处理完毕');
    }

    /**
     * 活动子状态的重构
     * php timer_yii script/update-activity-child-status
     */
    public function actionUpdateActivityChildStatus()
    {
        $activityList = BaseHwActivity::find()
            ->select([
                'id',
                'activity_child_status',
                'series_type',
            ])
            ->where([
                'activity_child_status' => [
                    1,
                    2,
                ],
                'series_type'           => BaseHwActivity::ZHAOPINHUI_UN_TYPE,
            ])
            ->asArray()
            ->all();
        //activity_child_status旧的1改为-1  旧的2改为1；
        foreach ($activityList as $item) {
            $model     = BaseHwActivity::findOne($item['id']);
            $oldStatus = $model->activity_child_status;
            $newStatus = 0;
            if ($oldStatus == 1) {
                $newStatus = BaseHwActivity::ACTIVITY_CHILD_STATUS_SIGN_UP;
            }
            if ($oldStatus == 2) {
                $newStatus = BaseHwActivity::ACTIVITY_CHILD_STATUS_NOT_START;
            }
            if ($newStatus > 0) {
                $model->activity_child_status = $newStatus;

                if ($model->save()) {
                    self::log('活动ID：' . $item['id'] . ',子状态修改成功！！！由原来的' . $oldStatus . '改为' . $newStatus);
                }
            }
        }
    }

    /**
     * 举办方式修复历史数据
     * php timer_yii script/update-to-hold-type
     */
    public function actionUpdateToHoldType()
    {
        //结构定义
        // activityId=> toHoldType
        $updateData = [
            1   => 2,
            2   => 3,
            3   => 3,
            4   => 2,
            5   => 1,
            6   => 3,
            7   => 2,
            8   => 2,
            9   => 2,
            10  => 2,
            11  => 2,
            12  => 2,
            13  => 2,
            14  => 2,
            15  => 2,
            16  => 2,
            17  => 2,
            18  => 2,
            19  => 2,
            20  => 2,
            21  => 2,
            22  => 2,
            23  => 2,
            24  => 2,
            25  => 2,
            26  => 2,
            27  => 2,
            28  => 1,
            29  => 3,
            30  => 3,
            31  => 2,
            32  => 2,
            33  => 3,
            34  => 2,
            35  => 2,
            36  => 2,
            37  => 2,
            38  => 2,
            39  => 2,
            40  => 2,
            41  => 2,
            42  => 2,
            43  => 3,
            44  => 1,
            45  => 2,
            46  => 2,
            47  => 2,
            48  => 2,
            49  => 2,
            50  => 2,
            51  => 2,
            52  => 2,
            53  => 2,
            54  => 2,
            55  => 2,
            56  => 2,
            57  => 2,
            58  => 2,
            59  => 2,
            60  => 2,
            61  => 3,
            62  => 2,
            63  => 1,
            64  => 2,
            65  => 2,
            66  => 2,
            67  => 2,
            68  => 2,
            69  => 2,
            70  => 2,
            71  => 2,
            72  => 2,
            73  => 1,
            74  => 3,
            75  => 1,
            76  => 1,
            77  => 2,
            78  => 2,
            79  => 2,
            80  => 2,
            81  => 3,
            82  => 2,
            83  => 2,
            84  => 3,
            85  => 1,
            86  => 1,
            87  => 1,
            88  => 2,
            89  => 2,
            90  => 1,
            91  => 2,
            92  => 3,
            93  => 3,
            94  => 2,
            95  => 2,
            96  => 2,
            97  => 2,
            98  => 2,
            99  => 2,
            100 => 1,
            101 => 2,
            102 => 2,
            103 => 2,
            104 => 2,
            105 => 1,
            106 => 2,
            107 => 2,
            108 => 2,
            109 => 2,
            110 => 2,
            111 => 2,
            112 => 2,
            113 => 2,
            114 => 2,
            115 => 2,
            116 => 2,
            117 => 3,
            118 => 2,
            119 => 3,
            120 => 1,
            121 => 2,
            122 => 1,
            123 => 2,
            124 => 2,
            125 => 2,
            126 => 2,
            127 => 2,
            128 => 2,
            129 => 2,
            130 => 2,
            131 => 2,
            132 => 2,
            133 => 2,
            134 => 2,
            135 => 2,
            136 => 2,
            137 => 3,
            138 => 2,
            139 => 2,
            140 => 2,
            141 => 1,
            142 => 2,
            143 => 2,
            144 => 3,
            145 => 2,
            146 => 2,
            147 => 2,
            148 => 2,
            149 => 2,
            150 => 2,
            151 => 2,
            152 => 2,
            153 => 2,
            154 => 1,
            155 => 3,
            156 => 2,
            157 => 2,
            158 => 2,
            159 => 2,
            160 => 2,
            161 => 2,
            162 => 2,
            163 => 2,
            164 => 2,
            165 => 2,
            166 => 2,
            167 => 2,
            168 => 2,
            169 => 2,
            170 => 2,
            171 => 2,
            172 => 1,
            173 => 2,
            174 => 2,
            175 => 2,
            176 => 2,
            177 => 3,
            178 => 2,
            179 => 2,
            180 => 1,
            181 => 3,
            182 => 2,
            183 => 1,
            184 => 1,
            185 => 1,
            186 => 2,
            187 => 2,
            188 => 2,
            189 => 1,
            190 => 1,
            191 => 2,
            192 => 2,
            193 => 1,
            194 => 1,
            195 => 2,
            196 => 3,
            197 => 2,
            198 => 2,
            199 => 2,
            200 => 2,
            201 => 1,
            202 => 2,
            203 => 2,
            204 => 2,
            205 => 1,
            206 => 2,
            207 => 1,
            208 => 3,
            209 => 1,
            210 => 2,
            211 => 2,
            212 => 2,
            213 => 1,
            214 => 3,
            215 => 1,
            216 => 2,
            217 => 2,
            218 => 2,
            219 => 2,
            220 => 2,
            221 => 2,
            222 => 2,
            223 => 2,
            224 => 3,
            225 => 1,
            226 => 2,
            227 => 2,
            228 => 2,
            229 => 2,
            230 => 2,
            231 => 2,
            232 => 1,
            233 => 2,
            234 => 2,
            235 => 2,
            236 => 2,
            237 => 2,
            238 => 2,
            239 => 2,
            240 => 2,
            241 => 2,
            242 => 2,
            243 => 2,
        ];
        foreach ($updateData as $activityId => $type) {
            //先获取一下活动信息
            $activity = BaseHwActivity::findOne($activityId);
            if ($activity && $activity->to_hold_type == 0) {
                $activity->to_hold_type = $type;
                if ($activity->save()) {
                    self::log('活动ID：' . $activityId . ';处理成功!');
                }
            } else {
                self::log('活动ID：' . $activityId . ';==================处理失败!');
            }
        }
        self::log('处理完毕!');
    }

    /**
     * 修复活动字段
     * php timer_yii script/update-hw-activity-field
     */
    public function actionUpdateHwActivityField()
    {
        $data = BaseHwActivity::find()
            ->select('id')
            ->where(['admin_id' => 0])
            ->asArray()
            ->all();
        foreach ($data as $item) {
            $model                       = BaseHwActivity::findOne($item['id']);
            $model->admin_id             = 1;
            $model->first_grounding_time = $model->add_time;
            $model->save();
            self::log('修复活动ID:' . $item['id']);
        }
    }

    // 更新企业简介
    // php timer_yii script/update-company-intro
    public function actionUpdateCompanyIntro()
    {
        $filePath = Yii::getAlias('@runtime') . '/非合作单位介绍填充（第二批）.xlsx';

        if (!file_exists($filePath)) {
            self::log('文件不存在');

            return;
        }

        $excel    = new Excel();
        $dataList = $excel->import($filePath);

        foreach ($dataList as $item) {
            $companyId = $item[0];
            $newIntro  = $item[1];

            // 找到单位
            $company = BaseCompany::findOne($companyId);
            // 判断是否存在
            if (!$company) {
                self::log('单位不存在：' . $companyId);
                continue;
            }
            // 判断单位类型
            if ($company->is_cooperation == BaseCompany::COOPERATIVE_UNIT_YES) {
                self::log('合作单位跳过：' . $companyId);
                continue;
            }

            $company->introduce = $newIntro;

            if (!$company->save()) {
                self::log('更新失败：' . $companyId);
                continue;
            }

            self::log('更新成功 :' . $companyId);
        }
    }

    /**
     * 推送今天的公告去队列
     * @return void
     * php timer_yii script/push-today-announcement-queue
     */
    public function actionPushTodayAnnouncementQueue()
    {
        $ids = BaseAnnouncement::find()
            ->alias('a')
            ->select(['a.id'])
            ->leftJoin(['art' => BaseArticle::tableName()], 'a.article_id=art.id')
            ->where([
                'or',
                ['art.refresh_date' => date('Y-m-d')],
                [
                    '>',
                    'a.add_time',
                    date('Y-m-d 00:00:00'),
                ],
            ])
            ->orderBy('a.id asc')
            ->asArray()
            ->column();
        foreach ($ids as $id) {
            Producer::afterAnnouncementUpdateJob($id);
            self::log('公告ID：' . $id . '入队成功');
        }
    }

    /**
     * 推送公告去队列
     * @return void
     * php timer_yii script/push-announcement-id
     */
    public function actionPushAnnouncementId()
    {
        $ids = [
            126981,
            213521,
            218443,
        ];
        foreach ($ids as $id) {
            Producer::afterAnnouncementUpdateJob($id);
            self::log('公告ID：' . $id . '入队成功');
        }
    }

    /**
     * 修复关联单位is_top字段值
     * @return void
     * php timer_yii script/update-is-top-default-value
     */
    public function actionUpdateIsTopDefaultValue()
    {
        $data = BaseHwActivityCompany::find()
            ->select(['id'])
            ->where(['is_top' => 0])
            ->orderBy('id asc')
            ->asArray()
            ->all();
        foreach ($data as $item) {
            $model         = BaseHwActivityCompany::findOne($item['id']);
            $model->is_top = BaseHwActivityCompany::IS_TOP_NO;
            $model->save();
            self::log('处理完毕id:' . $item['id']);
        }
    }

    /**
     * 公告状态修复
     * php timer_yii script/update-announcement-status
     */
    public function actionUpdateAnnouncementStatus()
    {
        $ids = BaseAnnouncement::find()
            ->alias('a')
            ->select('art.id')
            ->leftJoin(['art' => BaseArticle::tableName()], 'a.article_id=art.id')
            ->where([
                'art.status'     => 0,
                'a.audit_status' => 1,
                'art.is_show'    => 1,
            ])
            ->orderBy('art.id asc')
            ->asArray()
            ->column();
        foreach ($ids as $id) {
            $model          = BaseArticle::findOne($id);
            $model->status  = BaseArticle::STATUS_OFFLINE;
            $model->is_show = BaseArticle::IS_SHOW_NO;
            $model->save();
            self::log('文章ID为：' . $id . ',状态由0变成2，是否显示由1变成2');
        }
        //第二类
        $ids2 = BaseAnnouncement::find()
            ->alias('a')
            ->select('a.id')
            ->leftJoin(['art' => BaseArticle::tableName()], 'a.article_id=art.id')
            ->where([
                'art.status'     => 2,
                'a.audit_status' => 3,
            ])
            ->orderBy('a.id asc')
            ->asArray()
            ->column();
        foreach ($ids2 as $id2) {
            $model2               = BaseAnnouncement::findOne($id2);
            $model2->audit_status = BaseAnnouncement::STATUS_AUDIT_PASS;
            $model2->save();
            self::log('公告ID为：' . $id2 . ',审核状态由3变成1');
        }
    }

    /**
     * 职位状态修复
     * php timer_yii script/update-job-status
     */
    public function actionUpdateJobStatus()
    {
        $ids = BaseJob::find()
            ->select('id')
            ->where([
                'audit_status' => 0,
            ])
            ->orderBy('id asc')
            ->asArray()
            ->column();
        foreach ($ids as $id) {
            $model               = BaseJob::findOne($id);
            $model->audit_status = BaseJob::AUDIT_STATUS_PASS_AUDIT;
            $model->save();
            self::log('职位ID为：' . $id . ',审核状态由0变成1');
        }
        //第二类
        $ids2 = BaseJob::find()
            ->select('id')
            ->where([
                'status' => -1,
            ])
            ->orderBy('id asc')
            ->asArray()
            ->column();
        foreach ($ids2 as $id2) {
            $model2         = BaseJob::findOne($id2);
            $model2->status = BaseJob::STATUS_WAIT;
            $model2->save();
            self::log('职位ID为：' . $id2 . ',状态由-1变成3');
        }
        //第三类
        $ids3 = BaseJob::find()
            ->select('id')
            ->where([
                'status'       => 7,
                'audit_status' => 7,
            ])
            ->orderBy('id asc')
            ->asArray()
            ->column();
        foreach ($ids3 as $id3) {
            $model3               = BaseJob::findOne($id3);
            $model3->status       = BaseJob::STATUS_WAIT;
            $model3->audit_status = BaseJob::AUDIT_STATUS_WAIT;
            $model3->save();
            self::log('职位ID为：' . $id3 . ',状态由7变成3,审核状态由7变成3');
        }
        //第四类
        $list4 = BaseJob::find()
            ->select([
                'id',
                'status',
                'audit_status',
            ])
            ->where([
                'status'       => [
                    0,
                    7,
                ],
                'audit_status' => 3,
            ])
            ->orderBy('id asc')
            ->asArray()
            ->all();
        foreach ($list4 as $item4) {
            $model3 = BaseJob::findOne($item4['id']);
            if ($item4['status'] == 7) {
                $model3->status = BaseJob::STATUS_WAIT;
            }
            $model3->audit_status = BaseJob::AUDIT_STATUS_PASS_AUDIT;
            $model3->save();
            self::log('职位ID为：' . $item4['id'] . ',' . ($item4['status'] == 7 ? '状态由7变成3,' : '') . '审核状态由3变成1');
        }
    }

    /**
     * 找到求职者专业和专业字典对不上的求职者，修复学历相关字段
     *
     * php timer_yii script/fix-resume-major-level
     */
    public function actionFixResumeMajorLevel()
    {
        $sql = 'select resume.uuid,
       resume_education.id,
       resume.name,
       major_level3.name,
       major_level3.parent_id,
       resume_education.major_id_level_2,
       major_level3.status,
       major_id_level_3,
       major_id_level_1,
       resume.add_time
from resume
         inner join resume_education on resume.last_education_id = resume_education.id
         inner join major as major_level3 on resume_education.major_id_level_3 = major_level3.id
where major_level3.parent_id != resume_education.major_id_level_2';

        $list = Yii::$app->db->createCommand($sql)
            ->queryAll();

        foreach ($list as $item) {
            $resumeEducation = BaseResumeEducation::findOne($item['id']);
            $majorLevel3     = $resumeEducation->major_id_level_3;
            $major           = BaseMajor::findOne($majorLevel3);
            if ($major) {
                // 更新正确的level2
                $resumeEducation->major_id_level_2 = $major->parent_id;
                // 尝试找到level4
                $realMajorLevel2 = BaseMajor::find()
                    ->where([
                        'id'     => $major->parent_id,
                        'status' => 1,
                    ])
                    ->one();
                if ($realMajorLevel2) {
                    $resumeEducation->major_id_level_1 = $realMajorLevel2->parent_id;
                }

                if ($resumeEducation->save()) {
                    // 日志
                    self::log("修复: resumeId={$item['uuid']}, educationId={$item['id']}, 原level2={$item['major_id_level_2']}, level1={$item['major_id_level_1']}, 新level2={$resumeEducation->major_id_level_2}, 新level1={$resumeEducation->major_id_level_1}");
                }

                // if ($resumeEducation->save()) {
                //     self::log("修复成功: resumeId={$item['uuid']}, educationId={$item['id']}, 将level2更新为{$major->parent_id}");
                // } else {
                //     self::log("修复失败: resumeId={$item['uuid']}, educationId={$item['id']}, 错误: " . json_encode($resumeEducation->getFirstErrors()));
                // }
            }
        }
    }

    /**
     * 为指定UUID的求职者添加外籍人才标签（标签ID=1）
     * php timer_yii script/add-foreign-talent-tag
     */
    public function actionAddForeignTalentTag()
    {
        // 指定的UUID列表
        $uuidList = [
            '11104824',
            '11096735',
            '11107386',
            '10845779',
            '10962232',
            '11000808',
            '11083151',
            '11119677',
            '11087105',
            '11121841',
            '11111973',
            '11112942',
            '11086957',
            '11081809',
            '11108643',
            '11108601',
            '11036606',
            '10387624',
            '11102061',
            '11113616',
            '10664430',
            '11017521',
            '11074027',
            '11102532',
            '11080582',
            '11121797',
            '10998173',
            '11024445',
            '11069719',
            '11094258',
            '11089096',
            '11086189',
            '11082339',
            '10944209',
            '11121857',
            '11116484',
            '11094924',
            '11122105',
            '11096640',
            '10990844',
            '11123293',
            '11117678',
            '11122904',
            '11084770',
            '11100507',
            '11118294',
            '10923298',
            '11101600',
            '11099343',
            '11122908',
            '11088021',
            '11086223',
            '11087722',
            '11115286',
            '10416330',
            '10246166',
            '11094067',
            '11096873',
            '11088347',
            '11078494',
            '10911932',
            '11108964',
            '11111740',
            '11088690',
            '11096642',
            '11116160',
            '11089833',
            '11092508',
            '10979154',
            '11111777',
            '11064739',
            '11086201',
            '11118727',
            '11100141',
            '11084183',
            '10232819',
            '11118941',
            '10069103',
            '11118930',
            '11086688',
            '11087167',
            '11112800',
            '11111412',
            '11101605',
            '11104600',
            '11075991',
            '11108533',
            '10832947',
            '11111991',
            '11094483',
            '11119106',
            '11101350',
            '11099282',
            '11121189',
            '10916488',
            '10964202',
            '11123587',
            '11119662',
            '11118727',
            '11112800',
            '11100507',
            '11097864',
            '11088347',
            '11087722',
            '11086189',
            '11084183',
            '11081809',
            '11078494',
            '10983540',
            '10960693',
            '10824755',
            '11123213',
            '11118930',
            '11116373',
            '11115286',
            '11113616',
            '11101350',
            '11086815',
        ];

        $uuidList = [
            11119677,
            11121841,
            11111973,
            11112942,
            11086957,
            11108643,
            11108601,
            11121797,
            11121857,
            11116484,
            11122105,
            11123293,
            11122904,
        ];

        $foreignTalentTagId = 1; // 外籍人才标签ID

        try {
            // 验证标签是否存在
            $tag = \admin\models\ResumeTag::findOne($foreignTalentTagId);
            if (!$tag) {
                self::log('错误：标签ID=' . $foreignTalentTagId . '不存在');

                return;
            }
            self::log('标签验证成功：' . $tag->tag);

            // 通过UUID查找对应的简历ID
            $resumeList = BaseResume::find()
                ->select([
                    'id',
                    'uuid',
                    'name',
                ])
                ->where(['uuid' => $uuidList])
                ->asArray()
                ->all();

            $inputCount = count($uuidList);
            $foundCount = count($resumeList);

            self::log('输入UUID数量：' . $inputCount . '，找到简历数量：' . $foundCount);

            if ($foundCount == 0) {
                self::log('未找到任何匹配的简历');

                return;
            }

            // 统计变量
            $successCount = 0;
            $skipCount    = 0;
            $errorCount   = 0;

            foreach ($resumeList as $resume) {
                try {
                    $resumeId = $resume['id'];
                    $uuid     = $resume['uuid'];
                    $name     = $resume['name'];

                    // 检查是否已经有该标签
                    $existingTag = BaseResumeTagRelation::findOne([
                        'resume_id'     => $resumeId,
                        'resume_tag_id' => $foreignTalentTagId,
                    ]);

                    if ($existingTag) {
                        self::log('跳过：简历ID=' . $resumeId . '，UUID=' . $uuid . '，姓名=' . $name . '（已有外籍人才标签）');
                        $skipCount++;
                        continue;
                    }

                    // 使用现有的editTag方法添加标签
                    \admin\models\Resume::editTag($resumeId, [$foreignTalentTagId]);

                    self::log('成功：简历ID=' . $resumeId . '，UUID=' . $uuid . '，姓名=' . $name . '（已添加外籍人才标签）');
                    $successCount++;
                } catch (\Exception $e) {
                    self::log('错误：简历ID=' . $resumeId . '，UUID=' . $uuid . '，错误信息：' . $e->getMessage());
                    $errorCount++;
                }
            }

            // 输出统计结果
            self::log('=== 处理完成 ===');
            self::log('输入UUID总数：' . $inputCount);
            self::log('找到简历数：' . $foundCount);
            self::log('成功添加标签：' . $successCount);
            self::log('跳过（已有标签）：' . $skipCount);
            self::log('处理失败：' . $errorCount);

            // 找出未找到的UUID
            $foundUuids    = array_column($resumeList, 'uuid');
            $notFoundUuids = array_diff($uuidList, $foundUuids);
            if (!empty($notFoundUuids)) {
                self::log('未找到的UUID：' . implode(', ', $notFoundUuids));
            }
        } catch (\Exception $e) {
            self::log('脚本执行错误：' . $e->getMessage());
        }
    }

    /**
     * 更新公告创建人
     * php timer_yii script/reload-announcement-creator-name
     * @return void
     */
    public function actionReloadAnnouncementCreatorName()
    {
        $list = BaseAnnouncement::find()
            ->asArray()
            ->all();

        self::log('一共' . count($list) . '条公告需要更新创建人名称');

        $count = count($list);
        foreach ($list as $k => $item) {
            $percent = round(($k + 1) / count($list) * 100, 2) . '%';
            $txt     = $percent . '  ' . ($k + 1) . '/' . $count;

            $model = BaseAnnouncement::findOne($item['id']);

            $createType = $model->create_type;
            $creatorId  = $model->creator_id;

            if (!$createType || !$creatorId) {
                self::log($txt . '公告ID：' . $model->id . '没有创建人信息，跳过');
                continue;
            }

            $name = '';
            if ($createType == BaseAnnouncement::CREATE_TYPE_COMPANY) {
                $member = BaseMember::findOne($creatorId);
                $name   = $member->username;

                $txt .= '公告ID：' . $model->id . '创建人类型单位端，名字：' . $name;
            }

            if ($createType == BaseAnnouncement::CREATE_TYPE_ADMIN) {
                $member = BaseAdmin::findOne($creatorId);
                $name   = $member->name;

                $txt .= '_公告ID：' . $model->id . '创建人类型管理员端，名字：' . $name;
            }

            if (!$name) {
                self::log($txt . '_公告ID：' . $model->id . '创建人名称为空，跳过');
                continue;
            }

            $oldName             = $model->creator_name;
            $model->creator_name = $name;
            if ($model->save()) {
                self::log($txt . '_创建人名称更新成功，从 ' . $oldName . ' 更新为 ' . $name);
            } else {
                self::log($txt . '_创建人名称更新失败，错误信息：' . json_encode($model->getFirstErrors()));
            }
            // self::log('更新公告id' . $model->id . '成功名称：' . $name);
        }
    }

    /**
     * 将下线的公告和职位，时间为空的补全
     * php timer_yii script/update-offline-time
     * @return void
     */
    public function actionUpdateOfflineTime()
    {
        $offlineAnnouncementList = BaseAnnouncement::find()
            ->andWhere([
                'and',
                [
                    '=',
                    'status',
                    BaseAnnouncement::STATUS_OFFLINE,
                ],
                [
                    '=',
                    'offline_time',
                    TimeHelper::ZERO_TIME,
                ],
            ])
            ->asArray()
            ->all();

        self::log('一共' . count($offlineAnnouncementList) . '条公告需要更新下线时间');

        foreach ($offlineAnnouncementList as $k => $item) {
            $pencent = round(($k + 1) / count($offlineAnnouncementList) * 100, 2) . '%';
            $model   = BaseAnnouncement::findOne($item['id']);
            $log     = BaseAnnouncementHandleLog::find()
                ->orderBy('id desc')
                ->andWhere([
                    'and',
                    [
                        '=',
                        'handle_type',
                        BaseAnnouncementHandleLog::HANDLE_TYPE_OFFLINE,
                    ],
                    [
                        '=',
                        'announcement_id',
                        $item['id'],
                    ],
                ])
                ->asArray()
                ->one();

            $txt = '拿最后一次下线时间';

            if (!$log) {
                // 拿隐藏
                $log = BaseAnnouncementHandleLog::find()
                    ->orderBy('id desc')
                    ->andWhere([
                        'and',
                        [
                            '=',
                            'handle_type',
                            BaseAnnouncementHandleLog::HANDLE_TYPE_HIDDEN,
                        ],
                        [
                            '=',
                            'announcement_id',
                            $item['id'],
                        ],
                    ])
                    ->asArray()
                    ->one();

                $txt = '拿最后一次隐藏时间';
            }

            $model->offline_time = $log['add_time'];

            if (!$log['add_time']) {
                self::log($pencent . '公告id' . $model->id . '没有找到下线日志，跳过');
                continue;
            }

            $model->save();
            

            self::log($pencent . '更新公告id' . $model->id . '下线成功，' . $txt . '：' . $model->offline_time);
            // self::log('更新公告id' . $model->id . '成功');
        }

        $offlineJobList = BaseJob::find()
            ->andWhere([
                'and',
                [
                    '=',
                    'status',
                    BaseJob::STATUS_OFFLINE,
                ],
                [
                    '=',
                    'offline_time',
                    TimeHelper::ZERO_TIME,
                ],
            ])
            ->asArray()
            ->all();

        self::log('一共' . count($offlineJobList) . '条职位需要更新下线时间');

        foreach ($offlineJobList as $k => $item) {
            $pencent = round(($k + 1) / count($offlineJobList) * 100, 2) . '%';

            self::log($pencent . '正在处理职位ID：' . $item['id']);

            $model = BaseJob::findOne($item['id']);
            $log   = BaseJobHandleLog::find()
                ->orderBy('id desc')
                ->andWhere([
                    'and',
                    [
                        '=',
                        'handle_type',
                        BaseJobHandleLog::HANDLE_TYPE_OFFLINE,
                    ],
                    [
                        '=',
                        'job_id',
                        $item['id'],
                    ],
                ])
                ->asArray()
                ->one();

            $txt = '拿最后一次下线时间';


            if (!$log) {
                // 拿隐藏
                $log = BaseJobHandleLog::find()
                    ->orderBy('id desc')
                    ->andWhere([
                        'and',
                        [
                            '=',
                            'handle_type',
                            BaseJobHandleLog::HANDLE_TYPE_HIDDEN,
                        ],
                        [
                            '=',
                            'job_id',
                            $item['id'],
                        ],
                    ])
                    ->asArray()
                    ->one();

                $txt = '拿最后一次隐藏时间';

            }

            $model->offline_time = $log['add_time'];

            $model->save();

            if (!$log['add_time']) {
                self::log($pencent . '职位id' . $model->id . '没有找到下线日志，跳过');
                continue;
            }

            self::log($pencent . '更新职位id' . $model->id . '下线成功，' . $txt . '：' . $model->offline_time);
            // self::log('更新职位id' . $model->id . '成功');
        }
    }
}

