<?php

namespace companyH5\controllers;

use common\service\job\JobApplyHandleService;
use common\service\job\BaseService;
use Yii;
use yii\console\Response;

class JobApplyController extends BaseCompanyH5Controller
{
    /**
     * 简历管理操作
     * @return Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionHandle()
    {
        $request      = Yii::$app->request->post();
        $memberId     = Yii::$app->user->id;
        $auditService = new JobApplyHandleService();
        $params       = [
            'id'                  => $request['id'],
            'handle_type'         => $request['handleType'],
            'status'              => $request['status'],
            'company_mark_status' => $request['companyMarkStatus'],
        ];

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $auditService->setOperator($memberId, BaseService::OPERATOR_HANDLE_TYPE_COMPANY)
                ->setData($params)
                ->run();
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }
}