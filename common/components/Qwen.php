<?php

namespace common\components;

use common\helpers\DebugHelper;
use yii\base\Component;

class Qwen extends Component
{
    public $apiKey;
    public $model       = 'qwen-turbo';
    public $visionModel = 'qwen-vl-max';

    private $apiUrl       = 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation';
    private $visionApiUrl = 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions';

    /**
     * 发起对话请求
     * @param string $prompt      提示词
     * @param array  $history     上下文历史
     * @param float  $temperature 发散程度
     * @return mixed
     */
    public function chat($prompt, $history = [], $temperature = 0.7)
    {
        $payload = [
            'model'      => $this->model,
            'input'      => [
                'messages' => array_merge($history, [
                    [
                        'role'    => 'user',
                        'content' => $prompt,
                    ],
                ]),
            ],
            'parameters' => [
                'temperature' => $temperature,
            ],
        ];

        return $this->request($payload);
    }

    /**
     * 图片理解和分析
     * @param string $prompt      提示词
     * @param string $imageData   图片数据(base64编码)
     * @param string $imageType   图片类型(jpg,png等)
     * @param array  $history     上下文历史
     * @param float  $temperature 发散程度
     * @return mixed
     */
    public function chatWithImage($prompt, $imageData, $imageType = 'jpg', $history = [], $temperature = 0.7)
    {
        // 构建图片URL (data URI格式)
        $imageUrl = "data:image/{$imageType};base64,{$imageData}";

        // 构建消息内容
        $userContent = [
            [
                'type'      => 'image_url',
                'image_url' => [
                    'url' => $imageUrl,
                ],
            ],
            [
                'type' => 'text',
                'text' => $prompt,
            ],
        ];

        // 构建请求消息
        $messages = [
            [
                'role'    => 'system',
                'content' => [
                    [
                        'type' => 'text',
                        'text' => 'You are a helpful assistant that can analyze images.',
                    ],
                ],
            ],
        ];

        // 添加历史对话
        if (!empty($history)) {
            $messages = array_merge($messages, $history);
        }

        // 添加用户消息
        $messages[] = [
            'role'    => 'user',
            'content' => $userContent,
        ];

        $payload = [
            'model'       => $this->visionModel,
            'messages'    => $messages,
            'temperature' => $temperature,
        ];

        return $this->visionRequest($payload);
    }

    /**
     * 图片理解请求(兼容模式)
     * @param string $prompt      提示词
     * @param string $imageUrl    图片URL
     * @param array  $history     上下文历史
     * @param float  $temperature 发散程度
     * @return mixed
     */
    public function chatWithImageUrl($prompt, $imageUrl, $history = [], $temperature = 0.7)
    {
        // 构建消息内容
        $userContent = [
            [
                'type'      => 'image_url',
                'image_url' => [
                    'url' => $imageUrl,
                ],
            ],
            [
                'type' => 'text',
                'text' => $prompt,
            ],
        ];

        // 构建请求消息
        $messages = [
            [
                'role'    => 'system',
                'content' => [
                    [
                        'type' => 'text',
                        'text' => 'You are a helpful assistant that can analyze images.',
                    ],
                ],
            ],
        ];

        // 添加历史对话
        if (!empty($history)) {
            $messages = array_merge($messages, $history);
        }

        // 添加用户消息
        $messages[] = [
            'role'    => 'user',
            'content' => $userContent,
        ];

        $payload = [
            'model'       => $this->visionModel,
            'messages'    => $messages,
            'temperature' => $temperature,
        ];



        return $this->visionRequest($payload);
    }

    /**
     * curl请求接口
     * @param array $data 请求数据
     * @return mixed
     */
    private function request($data)
    {
        $headers = [
            "Authorization: Bearer {$this->apiKey}",
            "Content-Type: application/json",
        ];

        $ch = curl_init($this->apiUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

        $response = curl_exec($ch);

        if (curl_errno($ch)) {
            return ['error' => curl_error($ch)];
        }

        curl_close($ch);

        return json_decode($response, true);
    }

    /**
     * 视觉模型请求接口
     * @param array $data 请求数据
     * @return mixed
     */
    private function visionRequest($data)
    {
        $headers = [
            "Authorization: Bearer {$this->apiKey}",
            "Content-Type: application/json",
        ];

        $ch = curl_init($this->visionApiUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

        $response = curl_exec($ch);

        DebugHelper::log($response);

        if (curl_errno($ch)) {
            return ['error' => curl_error($ch)];
        }

        curl_close($ch);

        return json_decode($response, true);
    }

}