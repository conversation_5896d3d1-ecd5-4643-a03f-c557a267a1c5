<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "article".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property string $update_time 修改时间
 * @property string $refresh_time 发布时间
 * @property int $status 状态(2已下线，1在线，3草稿)
 * @property int $type 1:公告.2:资讯.3.每日汇总
 * @property int $is_show 是否显示，1显示，2隐藏
 * @property int $is_delete 软删除，2否，1是
 * @property string $title 标题
 * @property string $content 内容
 * @property int $home_column_id 所属栏目id
 * @property string $home_sub_column_ids 副栏目ids
 * @property string $original_url 来源
 * @property string $author 作者
 * @property string $tag_ids 标签(,)
 * @property string $link_url 网址
 * @property string $cover_thumb 正文头图
 * @property string $list_thumb 列表图片
 * @property string $seo_description 公告摘要
 * @property string $seo_keywords 关键字
 * @property int $click 点击次数
 * @property int $sort 排序
 * @property string $recommend_ids 推荐位ids
 * @property string $release_time 改字段已停用
 * @property string $original 来源
 * @property string $apply_audit_time 申请审核时间
 * @property string $first_release_time 首次发布时间
 * @property string $delete_time 删除时间
 * @property string $real_refresh_time 真实刷新时间
 * @property string $refresh_date 发布日期(格式Y-m-d)
 */
class Article extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'article';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time', 'refresh_time', 'release_time', 'apply_audit_time', 'first_release_time', 'delete_time', 'real_refresh_time', 'refresh_date'], 'safe'],
            [['status', 'type', 'is_show', 'is_delete', 'home_column_id', 'click', 'sort'], 'integer'],
            [['content'], 'string'],
            [['title', 'link_url', 'cover_thumb', 'seo_keywords'], 'string', 'max' => 256],
            [['home_sub_column_ids', 'original_url'], 'string', 'max' => 150],
            [['author', 'list_thumb'], 'string', 'max' => 64],
            [['tag_ids', 'original'], 'string', 'max' => 255],
            [['seo_description'], 'string', 'max' => 512],
            [['recommend_ids'], 'string', 'max' => 32],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'refresh_time' => 'Refresh Time',
            'status' => 'Status',
            'type' => 'Type',
            'is_show' => 'Is Show',
            'is_delete' => 'Is Delete',
            'title' => 'Title',
            'content' => 'Content',
            'home_column_id' => 'Home Column ID',
            'home_sub_column_ids' => 'Home Sub Column Ids',
            'original_url' => 'Original Url',
            'author' => 'Author',
            'tag_ids' => 'Tag Ids',
            'link_url' => 'Link Url',
            'cover_thumb' => 'Cover Thumb',
            'list_thumb' => 'List Thumb',
            'seo_description' => 'Seo Description',
            'seo_keywords' => 'Seo Keywords',
            'click' => 'Click',
            'sort' => 'Sort',
            'recommend_ids' => 'Recommend Ids',
            'release_time' => 'Release Time',
            'original' => 'Original',
            'apply_audit_time' => 'Apply Audit Time',
            'first_release_time' => 'First Release Time',
            'delete_time' => 'Delete Time',
            'real_refresh_time' => 'Real Refresh Time',
            'refresh_date' => 'Refresh Date',
        ];
    }
}
