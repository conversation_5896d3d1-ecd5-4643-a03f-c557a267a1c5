<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "job_welfare_relation".
 *
 * @property int $id
 * @property int $announcement_id 公告ID
 * @property int $job_id 职位ID
 * @property int $welfare_id 福利ID
 */
class JobWelfareRelation extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'job_welfare_relation';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['announcement_id', 'job_id', 'welfare_id'], 'integer'],
            [['welfare_id'], 'required'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'announcement_id' => 'Announcement ID',
            'job_id' => 'Job ID',
            'welfare_id' => 'Welfare ID',
        ];
    }
}
