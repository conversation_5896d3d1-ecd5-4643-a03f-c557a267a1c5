<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "announcement_click_political_status_total_daily".
 *
 * @property int $id ID
 * @property string $add_date 时间格式
 * @property int $announcement_id 公告ID
 * @property int $masses 群众
 * @property int $league_member 共青团员
 * @property int $party_member 中共党员
 * @property int $probationary_party_member 中共预备党员
 * @property int $democratic_party 民主党派
 * @property int $no_democratic_party 无民主党派
 * @property int $guest 游客
 */
class AnnouncementClickPoliticalStatusTotalDaily extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'announcement_click_political_status_total_daily';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_date'], 'required'],
            [['add_date'], 'safe'],
            [['announcement_id', 'masses', 'league_member', 'party_member', 'probationary_party_member', 'democratic_party', 'no_democratic_party', 'guest'], 'integer'],
            [['announcement_id', 'add_date'], 'unique', 'targetAttribute' => ['announcement_id', 'add_date']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_date' => 'Add Date',
            'announcement_id' => 'Announcement ID',
            'masses' => 'Masses',
            'league_member' => 'League Member',
            'party_member' => 'Party Member',
            'probationary_party_member' => 'Probationary Party Member',
            'democratic_party' => 'Democratic Party',
            'no_democratic_party' => 'No Democratic Party',
            'guest' => 'Guest',
        ];
    }
}
