<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "job_apply_title_total_daily".
 *
 * @property int $id ID
 * @property string $add_date 时间格式
 * @property int $job_id 职位ID
 * @property int $title_primary 初级职称
 * @property int $title_middle 中级职称
 * @property int $title_high 正高级职称
 * @property int $title_vice_high 副高级职称
 */
class JobApplyTitleTotalDaily extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'job_apply_title_total_daily';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_date'], 'required'],
            [['add_date'], 'safe'],
            [['job_id', 'title_primary', 'title_middle', 'title_high', 'title_vice_high'], 'integer'],
            [['job_id', 'add_date'], 'unique', 'targetAttribute' => ['job_id', 'add_date']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_date' => 'Add Date',
            'job_id' => 'Job ID',
            'title_primary' => 'Title Primary',
            'title_middle' => 'Title Middle',
            'title_high' => 'Title High',
            'title_vice_high' => 'Title Vice High',
        ];
    }
}
