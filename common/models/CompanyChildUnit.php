<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "company_child_unit".
 *
 * @property int $id
 * @property string $add_time 创建时间
 * @property string $update_time 修改时间
 * @property int $status 状态
 * @property int $member_id 会员id
 * @property int $company_id 企业id
 * @property string $name 名称
 * @property string $contact 联系人
 * @property string $telephone 固定电话
 * @property string $fax 传真
 */
class CompanyChildUnit extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'company_child_unit';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['status', 'member_id', 'company_id'], 'integer'],
            [['name', 'contact', 'telephone', 'fax'], 'string', 'max' => 32],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'member_id' => 'Member ID',
            'company_id' => 'Company ID',
            'name' => 'Name',
            'contact' => 'Contact',
            'telephone' => 'Telephone',
            'fax' => 'Fax',
        ];
    }
}
