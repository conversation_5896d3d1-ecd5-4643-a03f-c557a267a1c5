<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "hw_activity_company_hot".
 *
 * @property int $id 专场ID
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $activity_id 活动ID
 * @property int $company_id 单位ID
 * @property int $sort 排序
 * @property int $link_type 落地链接类型:1=公告详情；2=单位主页
 */
class HwActivityCompanyHot extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'hw_activity_company_hot';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['activity_id', 'company_id', 'sort', 'link_type'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'activity_id' => 'Activity ID',
            'company_id' => 'Company ID',
            'sort' => 'Sort',
            'link_type' => 'Link Type',
        ];
    }
}
