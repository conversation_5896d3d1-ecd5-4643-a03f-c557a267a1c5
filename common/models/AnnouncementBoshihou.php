<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "announcement_boshihou".
 *
 * @property int $id
 * @property int $announcement_id 公告ID
 */
class AnnouncementBoshihou extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'announcement_boshihou';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['announcement_id'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'announcement_id' => 'Announcement ID',
        ];
    }
}
