<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "company_member_config".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $company_id 单位ID
 * @property int $vip_total 子账号vip总数量
 * @property int $vip_available 子账号vip可用数量
 * @property int $vip_used 子账号vip已用数量
 * @property int $total 子账号的总数量
 * @property int $available 子账号的可用数量
 * @property int $used 子账号的已用数量
 */
class CompanyMemberConfig extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'company_member_config';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['company_id', 'vip_total', 'vip_available', 'vip_used', 'total', 'available', 'used'], 'integer'],
            [['company_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'company_id' => 'Company ID',
            'vip_total' => 'Vip Total',
            'vip_available' => 'Vip Available',
            'vip_used' => 'Vip Used',
            'total' => 'Total',
            'available' => 'Available',
            'used' => 'Used',
        ];
    }
}
