<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "job_apply_handle_log".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property int $job_apply_id 职位申请id
 * @property int $resume_id 简历id
 * @property int $company_id 单位id
 * @property int $handler_type 处理人类型(1求职者，2企业)
 * @property string $handler_name 处理人的名称（快照）
 * @property int $handle_type 处理的类型(1投递，2查看。。。99备注)
 * @property string $title 标题
 * @property string $content 内容
 * @property int $handle_id 处理人账号id(对应member_id)
 */
class JobApplyHandleLog extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'job_apply_handle_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time'], 'safe'],
            [['job_apply_id', 'resume_id', 'company_id', 'handler_type', 'handle_type', 'handle_id'], 'integer'],
            [['handler_name'], 'string', 'max' => 256],
            [['title', 'content'], 'string', 'max' => 512],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'job_apply_id' => 'Job Apply ID',
            'resume_id' => 'Resume ID',
            'company_id' => 'Company ID',
            'handler_type' => 'Handler Type',
            'handler_name' => 'Handler Name',
            'handle_type' => 'Handle Type',
            'title' => 'Title',
            'content' => 'Content',
            'handle_id' => 'Handle ID',
        ];
    }
}
