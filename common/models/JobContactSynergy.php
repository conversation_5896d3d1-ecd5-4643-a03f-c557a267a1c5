<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "job_contact_synergy".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $job_id 职位ID
 * @property int $announcement_id 公告ID
 * @property int $company_member_info_id 关联联系人账号ID
 * @property int $company_id 联系人隶属单位ID
 */
class JobContactSynergy extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'job_contact_synergy';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['job_id', 'announcement_id', 'company_member_info_id', 'company_id'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'job_id' => 'Job ID',
            'announcement_id' => 'Announcement ID',
            'company_member_info_id' => 'Company Member Info ID',
            'company_id' => 'Company ID',
        ];
    }
}
