<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "member_action_log".
 *
 * @property int $id id;主键id
 * @property string $add_time 创建时间
 * @property int $member_id 会员id
 * @property int $member_type 会员类型(1个人/2企业)
 * @property int $ip 最近一次登录的ip
 * @property string $platform pc,h5等等
 * @property string $content 内容
 * @property int $is_login 是否登录
 */
class MemberActionLog extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'member_action_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time'], 'safe'],
            [['member_id', 'member_type', 'ip', 'is_login'], 'integer'],
            [['platform'], 'string', 'max' => 32],
            [['content'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'member_id' => 'Member ID',
            'member_type' => 'Member Type',
            'ip' => 'Ip',
            'platform' => 'Platform',
            'content' => 'Content',
            'is_login' => 'Is Login',
        ];
    }
}
