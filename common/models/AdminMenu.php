<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "admin_menu".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property string $update_time 修改时间
 * @property int $status 状态
 * @property string $name 名称
 * @property int $parent_id 父id
 * @property int $level 等级(为未来做三级菜单做准备，现阶段只有两级)
 * @property string $key 前端的name(在这里用key表示，相对好理解)
 * @property int $is_route 是否路由(1代表这个是路由，而不是父级)
 */
class AdminMenu extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'admin_menu';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['status', 'parent_id', 'level', 'is_route'], 'integer'],
            [['name'], 'string', 'max' => 32],
            [['key'], 'string', 'max' => 64],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'name' => 'Name',
            'parent_id' => 'Parent ID',
            'level' => 'Level',
            'key' => 'Key',
            'is_route' => 'Is Route',
        ];
    }
}
