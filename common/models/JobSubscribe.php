<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "job_subscribe".
 *
 * @property int $id
 * @property string $add_time 创建时间
 * @property string $update_time 修改时间
 * @property int $status 状态
 * @property string $job_category_ids 意向职位id，逗号分隔
 * @property string $area_ids 意向城市id，逗号分隔
 * @property string $education_ids 学历id，逗号分隔
 * @property string $send_email 推送邮箱
 * @property int $is_send_wechat 是否推送微信（1是  2否）
 * @property int $is_send_email 是否推送邮箱（1是  2否）
 * @property int $resume_id 简历id
 */
class JobSubscribe extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'job_subscribe';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['status', 'is_send_wechat', 'is_send_email', 'resume_id'], 'integer'],
            [['job_category_ids', 'area_ids', 'education_ids', 'send_email'], 'string', 'max' => 255],
            [['resume_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'job_category_ids' => 'Job Category Ids',
            'area_ids' => 'Area Ids',
            'education_ids' => 'Education Ids',
            'send_email' => 'Send Email',
            'is_send_wechat' => 'Is Send Wechat',
            'is_send_email' => 'Is Send Email',
            'resume_id' => 'Resume ID',
        ];
    }
}
