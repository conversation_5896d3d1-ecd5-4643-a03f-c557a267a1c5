<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "activity".
 *
 * @property int $id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $status 状态
 * @property string $name 活动的名称
 * @property string $begin_time 开始时间
 * @property string $end_time 结束时间
 * @property string $token 活动token
 * @property string $view 模板view名称
 * @property string $form_rule 表单规则(json保存,里面有key,有name,有是否必填和正则)
 * @property string $remark 备注，可以临时放一些东西进去，方便以后扩增
 */
class Activity extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'activity';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time', 'begin_time', 'end_time'], 'safe'],
            [['status'], 'integer'],
            [['form_rule', 'remark'], 'required'],
            [['form_rule', 'remark'], 'string'],
            [['name', 'view'], 'string', 'max' => 256],
            [['token'], 'string', 'max' => 64],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'name' => 'Name',
            'begin_time' => 'Begin Time',
            'end_time' => 'End Time',
            'token' => 'Token',
            'view' => 'View',
            'form_rule' => 'Form Rule',
            'remark' => 'Remark',
        ];
    }
}
