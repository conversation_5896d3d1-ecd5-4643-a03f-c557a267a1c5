<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_library_collect".
 *
 * @property int $id
 * @property string $add_time 创建时间
 * @property int $company_id 单位id
 * @property int $resume_id 简历id
 * @property int $member_id 单位用户id
 */
class ResumeLibraryCollect extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_library_collect';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time'], 'safe'],
            [['company_id', 'resume_id', 'member_id'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'company_id' => 'Company ID',
            'resume_id' => 'Resume ID',
            'member_id' => 'Member ID',
        ];
    }
}
