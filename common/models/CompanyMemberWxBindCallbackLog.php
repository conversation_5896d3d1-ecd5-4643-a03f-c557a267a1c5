<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "company_member_wx_bind_callback_log".
 *
 * @property int $id 主键ID
 * @property string $add_time 创建时间
 * @property string $openid openid
 * @property string $data 实际的内容，使用json保存
 */
class CompanyMemberWxBindCallbackLog extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'company_member_wx_bind_callback_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time'], 'safe'],
            [['openid'], 'string', 'max' => 64],
            [['data'], 'string', 'max' => 2048],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'openid' => 'Openid',
            'data' => 'Data',
        ];
    }
}
