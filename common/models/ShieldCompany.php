<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "shield_company".
 *
 * @property int $id id;主键id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $status 状态
 * @property int $resume_id 求职者简历id
 * @property int $company_id 公司id
 */
class ShieldCompany extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'shield_company';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['status', 'resume_id', 'company_id'], 'integer'],
            [['company_id'], 'required'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'resume_id' => 'Resume ID',
            'company_id' => 'Company ID',
        ];
    }
}
