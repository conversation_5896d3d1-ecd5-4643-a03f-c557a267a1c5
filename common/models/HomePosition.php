<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "home_position".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $status 状态，1:显示；2:隐藏；9:删除
 * @property int $platform_type 所属平台
 * @property string $number 位置编号
 * @property string $name 位置名称
 * @property string $chinese_name 中文名称
 * @property string $width 版位宽
 * @property string $height 版位高
 * @property int $creator_type 创建人类型，1:平台，2:其他
 * @property string $creator 创建人
 * @property int $creator_id 创建人id
 * @property int $sort 广告位排序
 * @property string $describe 广告位描述
 */
class HomePosition extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'home_position';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['status', 'platform_type', 'creator_type', 'creator_id', 'sort'], 'integer'],
            [['creator'], 'required'],
            [['number', 'name', 'chinese_name'], 'string', 'max' => 256],
            [['width', 'height'], 'string', 'max' => 32],
            [['creator'], 'string', 'max' => 255],
            [['describe'], 'string', 'max' => 500],
            [['number'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'platform_type' => 'Platform Type',
            'number' => 'Number',
            'name' => 'Name',
            'chinese_name' => 'Chinese Name',
            'width' => 'Width',
            'height' => 'Height',
            'creator_type' => 'Creator Type',
            'creator' => 'Creator',
            'creator_id' => 'Creator ID',
            'sort' => 'Sort',
            'describe' => 'Describe',
        ];
    }
}
