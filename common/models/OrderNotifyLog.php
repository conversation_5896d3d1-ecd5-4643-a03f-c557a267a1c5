<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "order_notify_log".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property string $update_time 修改时间
 * @property string $order_no 平台订单号
 * @property int $member_type 1求职者,2单位端
 * @property string $notify 回调参数
 */
class OrderNotifyLog extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'order_notify_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['member_type'], 'integer'],
            [['notify'], 'required'],
            [['notify'], 'string'],
            [['order_no'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'order_no' => 'Order No',
            'member_type' => 'Member Type',
            'notify' => 'Notify',
        ];
    }
}
