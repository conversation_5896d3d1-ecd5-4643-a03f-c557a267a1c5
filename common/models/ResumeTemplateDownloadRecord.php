<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_template_download_record".
 *
 * @property int $id
 * @property int $resume_id 简历ID
 * @property int $template_id 模板ID
 * @property int $type 下载类型 1PDF 2DOC
 * @property string $add_time 下载时间
 * @property int $platform 下载平台 1PC 2 h5  3mini
 */
class ResumeTemplateDownloadRecord extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_template_download_record';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['resume_id', 'template_id', 'type', 'platform'], 'integer'],
            [['add_time'], 'required'],
            [['add_time'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'resume_id' => 'Resume ID',
            'template_id' => 'Template ID',
            'type' => 'Type',
            'add_time' => 'Add Time',
            'platform' => 'Platform',
        ];
    }
}
