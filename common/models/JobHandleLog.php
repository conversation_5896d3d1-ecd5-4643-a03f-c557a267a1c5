<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "job_handle_log".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property int $ip 操作ip
 * @property int $job_id 职位id
 * @property string $handle_type 操作类型，1:编辑；2：刷新；3:发布；4:再发布；5:下线；6:审核；7:隐藏；8:显示；9:删除；10:复制
 * @property int $handler_type 用户类型，1:运营平台；2:普通用户
 * @property int $handler_id 操作用户id
 * @property string $handler_name 操作人名称
 * @property string $handle_before 操作前
 * @property string $handle_after 操作后
 * @property int $announcement_id 公告id
 */
class JobHandleLog extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'job_handle_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time'], 'safe'],
            [['ip', 'job_id', 'handler_type', 'handler_id', 'announcement_id'], 'integer'],
            [['handle_before', 'handle_after'], 'required'],
            [['handle_before', 'handle_after'], 'string'],
            [['handle_type'], 'string', 'max' => 32],
            [['handler_name'], 'string', 'max' => 256],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'ip' => 'Ip',
            'job_id' => 'Job ID',
            'handle_type' => 'Handle Type',
            'handler_type' => 'Handler Type',
            'handler_id' => 'Handler ID',
            'handler_name' => 'Handler Name',
            'handle_before' => 'Handle Before',
            'handle_after' => 'Handle After',
            'announcement_id' => 'Announcement ID',
        ];
    }
}
