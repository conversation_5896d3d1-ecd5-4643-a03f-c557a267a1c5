<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_other_skill".
 *
 * @property int $id
 * @property string $add_time 创建时间
 * @property string $update_time 修改时间
 * @property int $status 状态
 * @property int $resume_id 简历id
 * @property int $member_id 会员id
 * @property string $name 技能名称
 * @property int $degree_type 掌握程度
 * @property string $description 技能描述
 */
class ResumeOtherSkill extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_other_skill';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['status', 'resume_id', 'member_id', 'degree_type'], 'integer'],
            [['description'], 'required'],
            [['description'], 'string'],
            [['name'], 'string', 'max' => 50],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'resume_id' => 'Resume ID',
            'member_id' => 'Member ID',
            'name' => 'Name',
            'degree_type' => 'Degree Type',
            'description' => 'Description',
        ];
    }
}
