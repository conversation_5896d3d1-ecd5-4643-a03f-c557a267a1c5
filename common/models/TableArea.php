<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "table_area".
 *
 * @property int $id ID
 * @property int $pid 父id
 * @property string $shortname 简称
 * @property string $name 名称
 * @property string $mergename 全称
 * @property int $level 层级 0 1 2 省市区县
 * @property string $pinyin 拼音
 * @property string $code 长途区号
 * @property string $zip 邮编
 * @property string $first 首字母
 * @property string $lng 经度
 * @property string $lat 纬度
 */
class TableArea extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'table_area';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['pid', 'level'], 'integer'],
            [['shortname', 'name', 'pinyin', 'code', 'zip', 'lng', 'lat'], 'string', 'max' => 100],
            [['mergename'], 'string', 'max' => 255],
            [['first'], 'string', 'max' => 50],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'pid' => 'Pid',
            'shortname' => 'Shortname',
            'name' => 'Name',
            'mergename' => 'Mergename',
            'level' => 'Level',
            'pinyin' => 'Pinyin',
            'code' => 'Code',
            'zip' => 'Zip',
            'first' => 'First',
            'lng' => 'Lng',
            'lat' => 'Lat',
        ];
    }
}
