<?php
/**
 * create user：shannon
 * create time：2025/2/26 上午10:26
 */
namespace common\helpers;

use common\base\models\BaseShowcase;

/**
 * 辅助小程序的助手类
 */
class MiniHelper
{
    /**
     * 获取链接跳转类型与链接页面类型
     * 小程序跳转类型--1：小程序站内指定页面；2:小程序网页；3：小程序视频号；4:小程序小程序
     * 1:页面类型--职位详情
     * 2:页面类型--公告详情
     * 3:页面类型--单位详情
     * 4:页面类型--职位搜索结果页
     * 5:页面类型--公告搜索结果页
     * 6:页面类型--单位搜索结果页
     * 7:页面类型--视频号主页
     * 8:页面类型--视频id
     * 小程序端跳转内部域名网址的说明：
     * （1）网址为：gaoxiaojob.com/announcement/detail/xxx，则跳转小程序端对应的公告详情页；
     * （2）网址为：gaoxiaojob.com/job/detail/xxx，则跳转小程序端对应的职位详情页；
     * （3）网址为：gaoxiaojob.com/company/detail/xxx，则跳转小程序端对应的单位详情页；
     * （4）网址为：bm.gaoxiaojob.com/xxx，则跳转小程序端对应的问卷星详情页；
     * （5）非以上情况的内部域名网址，则跳转该链接对应的H5页面；
     * @param $urlParam
     * @return array
     */
    public static function getUrlType($urlParam)
    {
        $targetType = 0; //跳转类型
        $pageType   = 0; //页面类型
        $url        = $urlParam; //页面参数ID或者链接
        $common     = true;
        $keyword    = "gaoxiaojob";
        if (\Yii::$app->params['environment'] != 'prod') {
            $keyword = 'gcjob.jugaocai';
        }
        //公告详情链接：gaoxiaojob.com/announcement/detail/xxxx.html匹配到gaoxiaojob.com/announcement/detail/字符则去到公告详情页并获取公告ID xxxx
        if (preg_match("/{$keyword}.com\/announcement\/detail\/(\d+).html/i", $urlParam, $matches)) {
            $targetType = BaseShowcase::TARGET_LINK_TYPE_STATION; //跳转类型
            $pageType   = BaseShowcase::PAGE_LINK_TYPE_ANNOUNCEMENT_DETAIL; //页面类型
            $url        = $matches[1]; //页面参数ID
            $common     = false;
        }
        //职位详情链接：gaoxiaojob.com/job/detail/xxxx.html匹配到gaoxiaojob.com/job/detail/字符则去到职位详情页并获取职位ID xxxx
        if (preg_match("/{$keyword}.com\/job\/detail\/(\d+).html/i", $urlParam, $matches)) {
            $targetType = BaseShowcase::TARGET_LINK_TYPE_STATION; //跳转类型
            $pageType   = BaseShowcase::PAGE_LINK_TYPE_JOB_DETAIL; //页面类型
            $url        = $matches[1]; //页面参数ID
            $common     = false;
        }
        //单位详情链接：gaoxiaojob.com/company/detail/xxxx.html、gaoxiaojob.com/company/detail/xxxx_a.html、gaoxiaojob.com/company/detail/xxxx_j.html、gaoxiaojob.com/company/detail/xxxx_h.html
        //匹配到gaoxiaojob.com/company/detail/字符则去到单位详情页并获取单位ID xxxx
        if (preg_match("/{$keyword}.com\/company\/detail\/(\d+)(_[ajh])?.html/i", $urlParam, $matches)) {
            $targetType = BaseShowcase::TARGET_LINK_TYPE_STATION; //跳转类型
            $pageType   = BaseShowcase::PAGE_LINK_TYPE_COMPANY_DETAIL; //页面类型
            $url        = $matches[1]; //页面参数ID
            $common     = false;
        }
        //问卷星详情链接：bm.gaoxiaojob.com/xxxx.html匹配到bm.gaoxiaojob.com/字符则去到问小程序网页卷星详情页
        if (preg_match("/bm.{$keyword}.com/i", $urlParam, $matches)) {
            $targetType = BaseShowcase::TARGET_LINK_TYPE_WEBPAGE; //跳转类型
            $url        = $urlParam;
            $common     = false;
        }

        if ($common && strpos($urlParam, "{$keyword}.com") !== false) {
            $targetType = BaseShowcase::TARGET_LINK_TYPE_WEBPAGE; //跳转类型
        }

        return [
            'targetType' => $targetType,
            'pageType'   => $pageType,
            'url'        => $url,
        ];
    }
}