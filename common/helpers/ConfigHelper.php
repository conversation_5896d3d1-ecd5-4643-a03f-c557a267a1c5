<?php

namespace common\helpers;

/** 公共配置类,主要是拿一些不存在数据库和params里面的共用配置 */
class ConfigHelper
{

    /**
     * 把一个有下标的数组转为k->v的数组
     */
    public static function getCountryMobileCode()
    {
        $list = [
            [
                "type" => "热门",
                "list" => [
                    [
                        "country" => "中国大陆",
                        "code"    => "+86",
                    ],
                    [
                        "country" => "中国香港",
                        "code"    => "+852",
                    ],
                    [
                        "country" => "中国澳门",
                        "code"    => "+853",
                    ],
                    [
                        "country" => "中国台湾",
                        "code"    => "+886",
                    ],
                    [
                        "country" => "英国",
                        "code"    => "+44",
                    ],
                    [
                        "country" => "美国",
                        "code"    => "+1",
                    ],
                    [
                        "country" => "澳大利亚",
                        "code"    => "+61",
                    ],
                    [
                        "country" => "日本",
                        "code"    => "+81",
                    ],
                    [
                        "country" => "韩国",
                        "code"    => "+82",
                    ],
                    [
                        "country" => "新加坡",
                        "code"    => "+65",
                    ],
                    [
                        "country" => "加拿大",
                        "code"    => "+1",
                    ],
                    [
                        "country" => "德国",
                        "code"    => "+49",
                    ],
                ],
            ],
            [
                "type" => "A",
                "list" => [
                    [
                        'country' => '阿尔巴尼亚',
                        'code'    => '+355',
                    ],
                    [
                        'country' => '阿曼',
                        'code'    => '+968',
                    ],
                    [
                        'country' => '阿富汗',
                        'code'    => '+93',
                    ],
                    [
                        'country' => '奥地利',
                        'code'    => '+43',
                    ],
                    [
                        'country' => '安提瓜和巴布达',
                        'code'    => '+1268',
                    ],
                    [
                        'country' => '阿根廷',
                        'code'    => '+54',
                    ],
                    [
                        'country' => '安圭拉',
                        'code'    => '+1264',
                    ],
                    [
                        'country' => '阿森松岛',
                        'code'    => '+247',
                    ],
                    [
                        'country' => '爱尔兰',
                        'code'    => '+353',
                    ],
                    [
                        'country' => '安道尔',
                        'code'    => '+376',
                    ],
                    [
                        'country' => '爱沙尼亚',
                        'code'    => '+372',
                    ],
                    [
                        'country' => '阿鲁巴',
                        'code'    => '+297',
                    ],
                    [
                        'country' => '安哥拉',
                        'code'    => '+244',
                    ],
                    //                    [
                    //                        'country' => '澳大利亚',
                    //                        'code'    => '+61',
                    //                    ],
                ],
            ],
            [
                "type" => "B",
                "list" => [
                    [
                        'country' => '比利时',
                        'code'    => '+32',
                    ],
                    [
                        'country' => '玻利维亚',
                        'code'    => '+591',
                    ],
                    [
                        'country' => '巴拿马',
                        'code'    => '+507',
                    ],
                    [
                        'country' => '布隆迪',
                        'code'    => '+257',
                    ],
                    [
                        'country' => '秘鲁',
                        'code'    => '+51',
                    ],
                    [
                        'country' => '贝宁',
                        'code'    => '+229',
                    ],
                    [
                        'country' => '伯利兹',
                        'code'    => '+501',
                    ],
                    [
                        'country' => '布基纳法索',
                        'code'    => '+226',
                    ],
                    [
                        'country' => '博茨瓦纳',
                        'code'    => '+267',
                    ],
                    [
                        'country' => '冰岛',
                        'code'    => '+354',
                    ],
                    [
                        'country' => '波斯尼亚和黑塞哥维那',
                        'code'    => '+387',
                    ],
                    [
                        'country' => '波多黎各',
                        'code'    => '+1787',
                    ],
                    [
                        'country' => '巴巴多斯',
                        'code'    => '+1246',
                    ],
                    [
                        'country' => '北马利安纳群岛',
                        'code'    => '+1670',
                    ],
                    [
                        'country' => '巴哈马',
                        'code'    => '+1242',
                    ],
                    [
                        'country' => '巴拉圭',
                        'code'    => '+595',
                    ],
                    [
                        'country' => '巴林',
                        'code'    => '+973',
                    ],
                    [
                        'country' => '波兰',
                        'code'    => '+48',
                    ],
                    [
                        'country' => '巴西',
                        'code'    => '+55',
                    ],
                    [
                        "country" => "巴基斯坦",
                        "code"    => "+92",
                    ],
                ],
            ],
            [
                "type" => "C",
                "list" => [
                    [
                        'country' => '赤道几内亚',
                        'code'    => '+240',
                    ],
                ],
            ],
            [
                "type" => "D",
                "list" => [
                    //                    [
                    //                        'country' => '德国',
                    //                        'code'    => '+49',
                    //                    ],
                    [
                        'country' => '东帝汶',
                        'code'    => '+670',
                    ],
                    [
                        'country' => '多米尼加共和国',
                        'code'    => '+1809',
                    ],
                    [
                        'country' => '多米尼加',
                        'code'    => '+1767',
                    ],
                    [
                        'country' => '丹麦',
                        'code'    => '+45',
                    ],
                    [
                        'country' => '多哥',
                        'code'    => '+228',
                    ],
                ],
            ],
            [
                "type" => "E",
                "list" => [
                    [
                        'country' => '厄瓜多尔',
                        'code'    => '+593',
                    ],
                    [
                        'country' => '厄立特里亚',
                        'code'    => '+291',
                    ],
                ],
            ],
            [
                "type" => "F",
                "list" => [
                    [
                        'country' => '菲律宾',
                        'code'    => '+63',
                    ],
                    [
                        'country' => '佛得角',
                        'code'    => '+238',
                    ],
                    [
                        'country' => '芬兰',
                        'code'    => '+358',
                    ],
                    [
                        'country' => '法国',
                        'code'    => '+33',
                    ],
                    [
                        'country' => '斐济',
                        'code'    => '+679',
                    ],
                    [
                        'country' => '法罗群岛',
                        'code'    => '+298',
                    ],
                ],
            ],
            [
                "type" => "G",
                "list" => [
                    [
                        'country' => '瓜地马拉',
                        'code'    => '+502',
                    ],
                    [
                        'country' => '圭亚那',
                        'code'    => '+592',
                    ],
                    [
                        'country' => '关岛',
                        'code'    => '+1671',
                    ],
                    [
                        'country' => '哥斯达黎加',
                        'code'    => '+506',
                    ],
                    [
                        'country' => '冈比亚',
                        'code'    => '+220',
                    ],
                    [
                        'country' => '格林纳达',
                        'code'    => '+1473',
                    ],
                    [
                        'country' => '哥伦比亚',
                        'code'    => '+57',
                    ],
                    [
                        'country' => '格陵兰岛',
                        'code'    => '+299',
                    ],
                ],
            ],
            [
                "type" => "H",
                "list" => [
                    [
                        'country' => '荷兰',
                        'code'    => '+31',
                    ],
                    [
                        'country' => '洪都拉斯',
                        'code'    => '+504',
                    ],
                    [
                        'country' => '海地',
                        'code'    => '+509',
                    ],
                    [
                        'country' => '黑山',
                        'code'    => '+382',
                    ],
                    [
                        'country' => '荷属安的列斯',
                        'code'    => '+599',
                    ],
                    [
                        'country' => '荷属圣马丁',
                        'code'    => '+1721',
                    ],
                    //                    [
                    //                        'country' => '韩国',
                    //                        'code'    => '+82',
                    //                    ],
                ],
            ],
            [
                "type" => "J",
                "list" => [
                    [
                        'country' => '吉布提',
                        'code'    => '+253',
                    ],
                    [
                        'country' => '几内亚',
                        'code'    => '+224',
                    ],
                    [
                        'country' => '加纳',
                        'code'    => '+233',
                    ],
                    [
                        'country' => '柬埔寨',
                        'code'    => '+855',
                    ],
                    [
                        'country' => '捷克',
                        'code'    => '+420',
                    ],
                    [
                        'country' => '津巴布韦',
                        'code'    => '+263',
                    ],
                    [
                        'country' => '基里巴斯',
                        'code'    => '+686',
                    ],
                    //                    [
                    //                        'country' => '加拿大',
                    //                        'code'    => '+1',
                    //                    ],
                ],
            ],
            [
                "type" => "K",
                "list" => [
                    [
                        'country' => '喀麦隆',
                        'code'    => '+237',
                    ],
                    [
                        'country' => '科索沃共和国',
                        'code'    => '+383',
                    ],
                    [
                        'country' => '克罗地亚',
                        'code'    => '+385',
                    ],
                    [
                        'country' => '库拉索',
                        'code'    => '+599',
                    ],
                    [
                        'country' => '开曼群岛',
                        'code'    => '+1345',
                    ],
                    [
                        'country' => '科摩罗',
                        'code'    => '+269',
                    ],
                    [
                        'country' => '库克群岛',
                        'code'    => '+682',
                    ],
                    [
                        'country' => '卡塔尔',
                        'code'    => '+974',
                    ],
                ],
            ],
            [
                "type" => "L",
                "list" => [
                    [
                        'country' => '黎巴嫩',
                        'code'    => '+961',
                    ],
                    [
                        'country' => '卢旺达',
                        'code'    => '+250',
                    ],
                    [
                        'country' => '利比里亚',
                        'code'    => '+231',
                    ],
                    [
                        'country' => '罗马尼亚',
                        'code'    => '+40',
                    ],
                    [
                        'country' => '拉脱维亚',
                        'code'    => '+371',
                    ],
                    [
                        'country' => '老挝',
                        'code'    => '+856',
                    ],
                    [
                        'country' => '立陶宛',
                        'code'    => '+370',
                    ],
                    [
                        'country' => '列支敦士登',
                        'code'    => '+423',
                    ],
                    [
                        'country' => '卢森堡',
                        'code'    => '+352',
                    ],
                ],
            ],
            [
                "type" => "M",
                "list" => [
                    [
                        'country' => '马里',
                        'code'    => '+223',
                    ],
                    [
                        'country' => '美属萨摩亚',
                        'code'    => '+1684',
                    ],
                    [
                        'country' => '蒙古',
                        'code'    => '+976',
                    ],
                    [
                        'country' => '摩纳哥',
                        'code'    => '+377',
                    ],
                    [
                        'country' => '毛里求斯',
                        'code'    => '+230',
                    ],
                    [
                        'country' => '摩尔多瓦',
                        'code'    => '+373',
                    ],
                    [
                        'country' => '马来西亚',
                        'code'    => '+60',
                    ],
                    [
                        'country' => '摩洛哥',
                        'code'    => '+212',
                    ],
                    [
                        'country' => '蒙特塞拉特岛',
                        'code'    => '+1664',
                    ],
                    [
                        'country' => '美属维尔京群岛',
                        'code'    => '+1340',
                    ],
                    [
                        'country' => '马绍尔群岛',
                        'code'    => '+692',
                    ],
                    [
                        'country' => '马尔代夫',
                        'code'    => '+960',
                    ],
                    [
                        'country' => '马耳他',
                        'code'    => '+356',
                    ],
                    [
                        'country' => '莫桑比克',
                        'code'    => '+258',
                    ],
                    [
                        'country' => '马其顿',
                        'code'    => '+389',
                    ],
                    [
                        'country' => '墨西哥',
                        'code'    => '+52',
                    ],
                    //                    [
                    //                        'country' => '美国',
                    //                        'code'    => '+1',
                    //                    ],
                ],
            ],
            [
                "type" => "N",
                "list" => [
                    [
                        'country' => '尼日利亚',
                        'code'    => '+234',
                    ],
                    [
                        'country' => '尼日尔',
                        'code'    => '+227',
                    ],
                    [
                        'country' => '尼泊尔',
                        'code'    => '+977',
                    ],
                    [
                        'country' => '南苏丹',
                        'code'    => '+211',
                    ],
                    [
                        'country' => '尼加拉瓜',
                        'code'    => '+505',
                    ],
                    [
                        'country' => '挪威',
                        'code'    => '+47',
                    ],
                    [
                        'country' => '纳米比亚',
                        'code'    => '+264',
                    ],
                    [
                        'country' => '南非',
                        'code'    => '+27',
                    ],
                ],
            ],
            [
                "type" => "P",
                "list" => [
                    [
                        'country' => '帕劳',
                        'code'    => '+680',
                    ],
                    [
                        'country' => '葡萄牙',
                        'code'    => '+351',
                    ],
                ],
            ],
            [
                "type" => "R",
                "list" => [
                    [
                        'country' => '瑞典',
                        'code'    => '+46',
                    ],
                    [
                        'country' => '瑞士',
                        'code'    => '+41',
                    ],
                    //                    [
                    //                        'country' => '日本',
                    //                        'code'    => '+81',
                    //                    ],
                ],
            ],
            [
                "type" => "S",
                "list" => [
                    [
                        'country' => '塞内加尔',
                        'code'    => '+221',
                    ],
                    [
                        'country' => '圣多美和普林西比',
                        'code'    => '+239',
                    ],
                    [
                        'country' => '圣彼埃尔和密克隆岛',
                        'code'    => '+508',
                    ],
                    [
                        'country' => '苏丹',
                        'code'    => '+249',
                    ],
                    [
                        'country' => '圣基茨和尼维斯',
                        'code'    => '+1869',
                    ],
                    [
                        'country' => '索马里',
                        'code'    => '+252',
                    ],
                    [
                        'country' => '萨摩亚',
                        'code'    => '+685',
                    ],
                    [
                        'country' => '斯威士兰',
                        'code'    => '+268',
                    ],
                    [
                        'country' => '塞尔维亚',
                        'code'    => '+381',
                    ],
                    [
                        'country' => '塞拉利昂',
                        'code'    => '+232',
                    ],
                    [
                        'country' => '圣文森特和格林纳丁斯',
                        'code'    => '+1784',
                    ],
                    [
                        'country' => '塞舌尔',
                        'code'    => '+248',
                    ],
                    [
                        'country' => '圣露西亚',
                        'code'    => '+1758',
                    ],
                    [
                        'country' => '圣马力诺',
                        'code'    => '+378',
                    ],
                    [
                        'country' => '萨尔瓦多',
                        'code'    => '+503',
                    ],
                    [
                        'country' => '斯洛伐克',
                        'code'    => '+421',
                    ],
                    [
                        'country' => '所罗门群岛',
                        'code'    => '+677',
                    ],
                    [
                        'country' => '塞浦路斯',
                        'code'    => '+357',
                    ],
                    [
                        'country' => '斯洛文尼亚',
                        'code'    => '+386',
                    ],
                ],
            ],
            [
                "type" => "T",
                "list" => [
                    [
                        'country' => '特克斯和凯科斯群岛',
                        'code'    => '+1649',
                    ],
                    [
                        'country' => '汤加',
                        'code'    => '+676',
                    ],
                    [
                        'country' => '泰国',
                        'code'    => '+66',
                    ],
                    [
                        'country' => '土耳其',
                        'code'    => '+90',
                    ],
                    [
                        "country" => "突尼斯",
                        "code"    => "+216",
                    ],
                ],
            ],
            [
                "type" => "W",
                "list" => [
                    [
                        'country' => '乌拉圭',
                        'code'    => '+598',
                    ],
                    [
                        'country' => '委内瑞拉',
                        'code'    => '+58',
                    ],
                    [
                        'country' => '瓦努阿图',
                        'code'    => '+678',
                    ],
                    [
                        'country' => '文莱',
                        'code'    => '+673',
                    ],
                ],
            ],
            [
                "type" => "X",
                "list" => [
                    [
                        'country' => '象牙海岸',
                        'code'    => '+225',
                    ],
                    [
                        'country' => '新西兰',
                        'code'    => '+64',
                    ],
                    [
                        'country' => '西撒哈拉',
                        'code'    => '+212',
                    ],
                    [
                        'country' => '希腊',
                        'code'    => '+30',
                    ],
                    [
                        'country' => '西班牙',
                        'code'    => '+34',
                    ],
                    [
                        "country" => "匈牙利",
                        "code"    => "+36",
                    ],
                    //                    [
                    //                        'country' => '新加坡',
                    //                        'code'    => '+65',
                    //                    ],
                ],
            ],
            [
                "type" => "Y",
                "list" => [
                    [
                        'country' => '越南',
                        'code'    => '+84',
                    ],
                    [
                        'country' => '以色列',
                        'code'    => '+972',
                    ],
                    [
                        'country' => '也门',
                        'code'    => '+967',
                    ],
                    [
                        'country' => '意大利',
                        'code'    => '+39',
                    ],
                    [
                        'country' => '英属处女群岛',
                        'code'    => '+1284',
                    ],
                    //                    [
                    //                        'country' => '英国',
                    //                        'code'    => '+44',
                    //                    ],
                    [
                        'country' => '牙买加',
                        'code'    => '+1876',
                    ],
                    [
                        'country' => '印度',
                        'code'    => '+91',
                    ],
                    [
                        'country' => '印度尼西亚',
                        'code'    => '+62',
                    ],
                ],
            ],
            [
                "type" => "Z",
                "list" => [
                    [
                        'country' => '智利',
                        'code'    => '+56',
                    ],
                    [
                        'country' => '中非共和国',
                        'code'    => '+236',
                    ],
                    //                    [
                    //                        'country' => '中国大陆',
                    //                        'code'    => '+86',
                    //                    ],
                    //                    [
                    //                        'country' => '中国香港',
                    //                        'code'    => '+852',
                    //                    ],
                    //                    [
                    //                        'country' => '中国台湾',
                    //                        'code'    => '+886',
                    //                    ],
                    //                    [
                    //                        'country' => '中国澳门',
                    //                        'code'    => '+853',
                    //                    ],
                    [
                        'country' => '直布罗陀',
                        'code'    => '+350',
                    ],
                ],
            ],
        ];

        return $list;
    }

    /**
     * 把一个有下标的数组转为k->v的数组
     */
    //    public static function getCountryMobileCodeOld()
    //    {
    //        $list = [
    //            [
    //                "type" => "热门",
    //                "list" => [
    //                    [
    //                        "country" => "中国大陆",
    //                        "code"    => "+86",
    //                    ],
    //                    [
    //                        "country" => "澳大利亚",
    //                        "code"    => "+61",
    //                    ],
    //                    [
    //                        "country" => "德国",
    //                        "code"    => "+49",
    //                    ],
    //                    [
    //                        "country" => "俄罗斯",
    //                        "code"    => "+7",
    //                    ],
    //                    [
    //                        "country" => "法国",
    //                        "code"    => "+33",
    //                    ],
    //                    [
    //                        "country" => "韩国",
    //                        "code"    => "+82",
    //                    ],
    //                    [
    //                        "country" => "加拿大",
    //                        "code"    => "+1",
    //                    ],
    //                    [
    //                        "country" => "马来西亚",
    //                        "code"    => "+60",
    //                    ],
    //                    [
    //                        "country" => "美国",
    //                        "code"    => "+1",
    //                    ],
    //                    [
    //                        "country" => "日本",
    //                        "code"    => "+81",
    //                    ],
    //                    [
    //                        "country" => "泰国",
    //                        "code"    => "+66",
    //                    ],
    //                    [
    //                        "country" => "新加坡",
    //                        "code"    => "+65",
    //                    ],
    //                ],
    //            ],
    //            [
    //                "type" => "A",
    //                "list" => [
    //                    [
    //                        "country" => "阿尔巴尼亚",
    //                        "code"    => "+355",
    //                    ],
    //                    [
    //                        "country" => "阿尔及利亚",
    //                        "code"    => "+213",
    //                    ],
    //                    [
    //                        "country" => "阿富汗",
    //                        "code"    => "+93",
    //                    ],
    //                    [
    //                        "country" => "阿根廷",
    //                        "code"    => "+54",
    //                    ],
    //                    [
    //                        "country" => "阿拉伯联合酋长国",
    //                        "code"    => "+971",
    //                    ],
    //                    [
    //                        "country" => "阿曼",
    //                        "code"    => "+968",
    //                    ],
    //                    [
    //                        "country" => "阿塞拜疆",
    //                        "code"    => "+994",
    //                    ],
    //                    [
    //                        "country" => "阿森松",
    //                        "code"    => "+247",
    //                    ],
    //                    [
    //                        "country" => "埃及",
    //                        "code"    => "+20",
    //                    ],
    //                    [
    //                        "country" => "埃塞俄比亚",
    //                        "code"    => "+251",
    //                    ],
    //                    [
    //                        "country" => "爱尔兰",
    //                        "code"    => "+353",
    //                    ],
    //                    [
    //                        "country" => "爱沙尼亚",
    //                        "code"    => "+372",
    //                    ],
    //                    [
    //                        "country" => "安道尔共和国",
    //                        "code"    => "+376",
    //                    ],
    //                    [
    //                        "country" => "安哥拉",
    //                        "code"    => "+244",
    //                    ],
    //                    [
    //                        "country" => "安圭拉岛",
    //                        "code"    => "+1264",
    //                    ],
    //                    [
    //                        "country" => "安提瓜和巴布达",
    //                        "code"    => "+1268",
    //                    ],
    //                    [
    //                        "country" => "奥地利",
    //                        "code"    => "+43",
    //                    ],
    //                    [
    //                        "country" => "澳大利亚",
    //                        "code"    => "+61",
    //                    ],
    //                ],
    //            ],
    //            [
    //                "type" => "B",
    //                "list" => [
    //                    [
    //                        "country" => "巴巴多斯",
    //                        "code"    => "+1246",
    //                    ],
    //                    [
    //                        "country" => "巴布亚新几内亚",
    //                        "code"    => "+675",
    //                    ],
    //                    [
    //                        "country" => "巴哈马",
    //                        "code"    => "+1242",
    //                    ],
    //                    [
    //                        "country" => "巴基斯坦",
    //                        "code"    => "+92",
    //                    ],
    //                    [
    //                        "country" => "巴拉圭",
    //                        "code"    => "+595",
    //                    ],
    //                    [
    //                        "country" => "巴林",
    //                        "code"    => "+973",
    //                    ],
    //                    [
    //                        "country" => "巴拿马",
    //                        "code"    => "+507",
    //                    ],
    //                    [
    //                        "country" => "巴西",
    //                        "code"    => "+55",
    //                    ],
    //                    [
    //                        "country" => "白俄罗斯",
    //                        "code"    => "+375",
    //                    ],
    //                    [
    //                        "country" => "百慕大群岛",
    //                        "code"    => "+1441",
    //                    ],
    //                    [
    //                        "country" => "保加利亚",
    //                        "code"    => "+359",
    //                    ],
    //                    [
    //                        "country" => "贝宁",
    //                        "code"    => "+229",
    //                    ],
    //                    [
    //                        "country" => "比利时",
    //                        "code"    => "+32",
    //                    ],
    //                    [
    //                        "country" => "冰岛",
    //                        "code"    => "+354",
    //                    ],
    //                    [
    //                        "country" => "波多黎各",
    //                        "code"    => "+1787",
    //                    ],
    //                    [
    //                        "country" => "波兰",
    //                        "code"    => "+48",
    //                    ],
    //                    [
    //                        "country" => "玻利维亚",
    //                        "code"    => "+591",
    //                    ],
    //                    [
    //                        "country" => "伯利兹",
    //                        "code"    => "+501",
    //                    ],
    //                    [
    //                        "country" => "博茨瓦纳",
    //                        "code"    => "+267",
    //                    ],
    //                    [
    //                        "country" => "布基纳法索",
    //                        "code"    => "+226",
    //                    ],
    //                    [
    //                        "country" => "布隆迪",
    //                        "code"    => "+257",
    //                    ],
    //                    [
    //                        "country" => "波黑",
    //                        "code"    => "+387",
    //                    ],
    //                ],
    //            ],
    //            [
    //                "type" => "C",
    //                "list" => [
    //                    [
    //                        "country" => "朝鲜",
    //                        "code"    => "+850",
    //                    ],
    //                ],
    //            ],
    //            [
    //                "type" => "D",
    //                "list" => [
    //                    [
    //                        "country" => "丹麦",
    //                        "code"    => "+45",
    //                    ],
    //                    [
    //                        "country" => "德国",
    //                        "code"    => "+49",
    //                    ],
    //                    [
    //                        "country" => "东萨摩亚(美)",
    //                        "code"    => "+684",
    //                    ],
    //                    [
    //                        "country" => "多哥",
    //                        "code"    => "+228",
    //                    ],
    //                    [
    //                        "country" => "多米尼加共和国",
    //                        "code"    => "+1890",
    //                    ],
    //                    [
    //                        "country" => "东德",
    //                        "code"    => "+37",
    //                    ],
    //                ],
    //            ],
    //            [
    //                "type" => "E",
    //                "list" => [
    //                    [
    //                        "country" => "俄罗斯",
    //                        "code"    => "+7",
    //                    ],
    //                    [
    //                        "country" => "厄瓜多尔",
    //                        "code"    => "+593",
    //                    ],
    //                ],
    //            ],
    //            [
    //                "type" => "F",
    //                "list" => [
    //                    [
    //                        "country" => "法国",
    //                        "code"    => "+33",
    //                    ],
    //                    [
    //                        "country" => "法属玻利尼西亚",
    //                        "code"    => "+689",
    //                    ],
    //                    [
    //                        "country" => "法属圭亚那",
    //                        "code"    => "+594",
    //                    ],
    //                    [
    //                        "country" => "菲律宾",
    //                        "code"    => "+63",
    //                    ],
    //                    [
    //                        "country" => "斐济",
    //                        "code"    => "+679",
    //                    ],
    //                    [
    //                        "country" => "芬兰",
    //                        "code"    => "+358",
    //                    ],
    //                    [
    //                        "country" => "梵蒂冈",
    //                        "code"    => "+379",
    //                    ],
    //                ],
    //            ],
    //            [
    //                "type" => "G",
    //                "list" => [
    //                    [
    //                        "country" => "冈比亚",
    //                        "code"    => "+220",
    //                    ],
    //                    [
    //                        "country" => "刚果",
    //                        "code"    => "+242",
    //                    ],
    //                    [
    //                        "country" => "哥伦比亚",
    //                        "code"    => "+57",
    //                    ],
    //                    [
    //                        "country" => "哥斯达黎加",
    //                        "code"    => "+506",
    //                    ],
    //                    [
    //                        "country" => "格林纳达",
    //                        "code"    => "+1809",
    //                    ],
    //                    [
    //                        "country" => "格鲁吉亚",
    //                        "code"    => "+995",
    //                    ],
    //                    [
    //                        "country" => "古巴",
    //                        "code"    => "+53",
    //                    ],
    //                    [
    //                        "country" => "关岛",
    //                        "code"    => "+1671",
    //                    ],
    //                    [
    //                        "country" => "圭亚那",
    //                        "code"    => "+592",
    //                    ],
    //                ],
    //            ],
    //            [
    //                "type" => "H",
    //                "list" => [
    //                    [
    //                        "country" => "哈萨克斯坦",
    //                        "code"    => "+327",
    //                    ],
    //                    [
    //                        "country" => "海地",
    //                        "code"    => "+509",
    //                    ],
    //                    [
    //                        "country" => "韩国",
    //                        "code"    => "+82",
    //                    ],
    //                    [
    //                        "country" => "荷兰",
    //                        "code"    => "+31",
    //                    ],
    //                    [
    //                        "country" => "荷属安的列斯",
    //                        "code"    => "+599",
    //                    ],
    //                    [
    //                        "country" => "洪都拉斯",
    //                        "code"    => "+504",
    //                    ],
    //                    [
    //                        "country" => "黑山",
    //                        "code"    => "+382",
    //                    ],
    //                    [
    //                        "country" => "环欧",
    //                        "code"    => "+388",
    //                    ],
    //                ],
    //            ],
    //            [
    //                "type" => "J",
    //                "list" => [
    //                    [
    //                        "country" => "吉布提",
    //                        "code"    => "+253",
    //                    ],
    //                    [
    //                        "country" => "吉尔吉斯坦",
    //                        "code"    => "+331",
    //                    ],
    //                    [
    //                        "country" => "几内亚",
    //                        "code"    => "+224",
    //                    ],
    //                    [
    //                        "country" => "加拿大",
    //                        "code"    => "+1",
    //                    ],
    //                    [
    //                        "country" => "加纳",
    //                        "code"    => "+233",
    //                    ],
    //                    [
    //                        "country" => "加蓬",
    //                        "code"    => "+241",
    //                    ],
    //                    [
    //                        "country" => "柬埔寨",
    //                        "code"    => "+855",
    //                    ],
    //                    [
    //                        "country" => "捷克",
    //                        "code"    => "+420",
    //                    ],
    //                    [
    //                        "country" => "津巴布韦",
    //                        "code"    => "+263",
    //                    ],
    //                    [
    //                        "country" => "捷克斯洛伐克",
    //                        "code"    => "+42",
    //                    ],
    //                ],
    //            ],
    //            [
    //                "type" => "K",
    //                "list" => [
    //                    [
    //                        "country" => "喀麦隆",
    //                        "code"    => "+237",
    //                    ],
    //                    [
    //                        "country" => "卡塔尔",
    //                        "code"    => "+974",
    //                    ],
    //                    [
    //                        "country" => "开曼群岛",
    //                        "code"    => "+1345",
    //                    ],
    //                    [
    //                        "country" => "科特迪瓦",
    //                        "code"    => "+225",
    //                    ],
    //                    [
    //                        "country" => "科威特",
    //                        "code"    => "+965",
    //                    ],
    //                    [
    //                        "country" => "肯尼亚",
    //                        "code"    => "+254",
    //                    ],
    //                    [
    //                        "country" => "库克群岛",
    //                        "code"    => "+682",
    //                    ],
    //                    [
    //                        "country" => "克罗地亚",
    //                        "code"    => "+385",
    //                    ],
    //                ],
    //            ],
    //            [
    //                "type" => "L",
    //                "list" => [
    //                    [
    //                        "country" => "拉脱维亚",
    //                        "code"    => "+371",
    //                    ],
    //                    [
    //                        "country" => "莱索托",
    //                        "code"    => "+266",
    //                    ],
    //                    [
    //                        "country" => "老挝",
    //                        "code"    => "+856",
    //                    ],
    //                    [
    //                        "country" => "黎巴嫩",
    //                        "code"    => "+961",
    //                    ],
    //                    [
    //                        "country" => "立陶宛",
    //                        "code"    => "+370",
    //                    ],
    //                    [
    //                        "country" => "利比里亚",
    //                        "code"    => "+231",
    //                    ],
    //                    [
    //                        "country" => "利比亚",
    //                        "code"    => "+218",
    //                    ],
    //                    [
    //                        "country" => "列支敦士登",
    //                        "code"    => "+423",
    //                    ],
    //                    [
    //                        "country" => "留尼旺",
    //                        "code"    => "+262",
    //                    ],
    //                    [
    //                        "country" => "卢森堡",
    //                        "code"    => "+352",
    //                    ],
    //                    [
    //                        "country" => "罗马尼亚",
    //                        "code"    => "+40",
    //                    ],
    //                ],
    //            ],
    //            [
    //                "type" => "M",
    //                "list" => [
    //                    [
    //                        "country" => "马达加斯加",
    //                        "code"    => "+261",
    //                    ],
    //                    [
    //                        "country" => "马尔代夫",
    //                        "code"    => "+960",
    //                    ],
    //                    [
    //                        "country" => "马耳他",
    //                        "code"    => "+356",
    //                    ],
    //                    [
    //                        "country" => "马拉维",
    //                        "code"    => "+265",
    //                    ],
    //                    [
    //                        "country" => "马来西亚",
    //                        "code"    => "+60",
    //                    ],
    //                    [
    //                        "country" => "马里",
    //                        "code"    => "+223",
    //                    ],
    //                    [
    //                        "country" => "马里亚那群岛",
    //                        "code"    => "+1670",
    //                    ],
    //                    [
    //                        "country" => "马提尼克",
    //                        "code"    => "+596",
    //                    ],
    //                    [
    //                        "country" => "毛里求斯",
    //                        "code"    => "+230",
    //                    ],
    //                    [
    //                        "country" => "美国",
    //                        "code"    => "+1",
    //                    ],
    //                    [
    //                        "country" => "蒙古",
    //                        "code"    => "+976",
    //                    ],
    //                    [
    //                        "country" => "蒙特塞拉特岛",
    //                        "code"    => "+1664",
    //                    ],
    //                    [
    //                        "country" => "孟加拉国",
    //                        "code"    => "+880",
    //                    ],
    //                    [
    //                        "country" => "秘鲁",
    //                        "code"    => "+51",
    //                    ],
    //                    [
    //                        "country" => "缅甸",
    //                        "code"    => "+95",
    //                    ],
    //                    [
    //                        "country" => "摩尔多瓦",
    //                        "code"    => "+373",
    //                    ],
    //                    [
    //                        "country" => "摩洛哥",
    //                        "code"    => "+212",
    //                    ],
    //                    [
    //                        "country" => "摩纳哥",
    //                        "code"    => "+377",
    //                    ],
    //                    [
    //                        "country" => "莫桑比克",
    //                        "code"    => "+258",
    //                    ],
    //                    [
    //                        "country" => "墨西哥",
    //                        "code"    => "+52",
    //                    ],
    //                    [
    //                        "country" => "马其顿",
    //                        "code"    => "+389",
    //                    ],
    //                ],
    //            ],
    //            [
    //                "type" => "N",
    //                "list" => [
    //                    [
    //                        "country" => "纳米比亚",
    //                        "code"    => "+264",
    //                    ],
    //                    [
    //                        "country" => "南非",
    //                        "code"    => "+27",
    //                    ],
    //                    [
    //                        "country" => "瑙鲁",
    //                        "code"    => "+674",
    //                    ],
    //                    [
    //                        "country" => "尼泊尔",
    //                        "code"    => "+977",
    //                    ],
    //                    [
    //                        "country" => "尼加拉瓜",
    //                        "code"    => "+505",
    //                    ],
    //                    [
    //                        "country" => "尼日尔",
    //                        "code"    => "+227",
    //                    ],
    //                    [
    //                        "country" => "尼日利亚",
    //                        "code"    => "+234",
    //                    ],
    //                    [
    //                        "country" => "挪威",
    //                        "code"    => "+47",
    //                    ],
    //                    [
    //                        "country" => "南斯拉夫",
    //                        "code"    => "+38",
    //                    ],
    //                ],
    //            ],
    //            [
    //                "type" => "P",
    //                "list" => [
    //                    [
    //                        "country" => "葡萄牙",
    //                        "code"    => "+351",
    //                    ],
    //                ],
    //            ],
    //            [
    //                "type" => "R",
    //                "list" => [
    //                    [
    //                        "country" => "日本",
    //                        "code"    => "+81",
    //                    ],
    //                    [
    //                        "country" => "瑞典",
    //                        "code"    => "+46",
    //                    ],
    //                    [
    //                        "country" => "瑞士",
    //                        "code"    => "+41",
    //                    ],
    //                ],
    //            ],
    //            [
    //                "type" => "S",
    //                "list" => [
    //                    [
    //                        "country" => "塞尔维亚",
    //                        "code"    => "+381",
    //                    ],
    //                    [
    //                        "country" => "萨尔瓦多",
    //                        "code"    => "+503",
    //                    ],
    //                    [
    //                        "country" => "塞拉利昂",
    //                        "code"    => "+232",
    //                    ],
    //                    [
    //                        "country" => "塞内加尔",
    //                        "code"    => "+221",
    //                    ],
    //                    [
    //                        "country" => "塞浦路斯",
    //                        "code"    => "+357",
    //                    ],
    //                    [
    //                        "country" => "塞舌尔",
    //                        "code"    => "+248",
    //                    ],
    //                    [
    //                        "country" => "沙特阿拉伯",
    //                        "code"    => "+966",
    //                    ],
    //                    [
    //                        "country" => "圣多美和普林西比",
    //                        "code"    => "+239",
    //                    ],
    //                    [
    //                        "country" => "圣卢西亚",
    //                        "code"    => "+1758",
    //                    ],
    //                    [
    //                        "country" => "圣马力诺",
    //                        "code"    => "+378",
    //                    ],
    //                    [
    //                        "country" => "圣文森特",
    //                        "code"    => "+1784",
    //                    ],
    //                    [
    //                        "country" => "圣文森特岛",
    //                        "code"    => "+1784",
    //                    ],
    //                    [
    //                        "country" => "斯里兰卡",
    //                        "code"    => "+94",
    //                    ],
    //                    [
    //                        "country" => "斯洛伐克",
    //                        "code"    => "+421",
    //                    ],
    //                    [
    //                        "country" => "斯洛文尼亚",
    //                        "code"    => "+386",
    //                    ],
    //                    [
    //                        "country" => "斯威士兰",
    //                        "code"    => "+268",
    //                    ],
    //                    [
    //                        "country" => "苏丹",
    //                        "code"    => "+249",
    //                    ],
    //                    [
    //                        "country" => "苏里南",
    //                        "code"    => "+597",
    //                    ],
    //                    [
    //                        "country" => "所罗门群岛",
    //                        "code"    => "+677",
    //                    ],
    //                    [
    //                        "country" => "索马里",
    //                        "code"    => "+252",
    //                    ],
    //                ],
    //            ],
    //            [
    //                "type" => "T",
    //                "list" => [
    //                    [
    //                        "country" => "塔吉克斯坦",
    //                        "code"    => "+992",
    //                    ],
    //                    [
    //                        "country" => "泰国",
    //                        "code"    => "+66",
    //                    ],
    //                    [
    //                        "country" => "坦桑尼亚",
    //                        "code"    => "+255",
    //                    ],
    //                    [
    //                        "country" => "汤加",
    //                        "code"    => "+676",
    //                    ],
    //                    [
    //                        "country" => "特立尼达和多巴哥",
    //                        "code"    => "+1809",
    //                    ],
    //                    [
    //                        "country" => "突尼斯",
    //                        "code"    => "+216",
    //                    ],
    //                    [
    //                        "country" => "土耳其",
    //                        "code"    => "+90",
    //                    ],
    //                    [
    //                        "country" => "土库曼斯坦",
    //                        "code"    => "+993",
    //                    ],
    //                ],
    //            ],
    //            [
    //                "type" => "W",
    //                "list" => [
    //                    [
    //                        "country" => "危地马拉",
    //                        "code"    => "+502",
    //                    ],
    //                    [
    //                        "country" => "委内瑞拉",
    //                        "code"    => "+58",
    //                    ],
    //                    [
    //                        "country" => "文莱",
    //                        "code"    => "+673",
    //                    ],
    //                    [
    //                        "country" => "乌干达",
    //                        "code"    => "+256",
    //                    ],
    //                    [
    //                        "country" => "乌克兰",
    //                        "code"    => "+380",
    //                    ],
    //                    [
    //                        "country" => "乌拉圭",
    //                        "code"    => "+598",
    //                    ],
    //                    [
    //                        "country" => "乌兹别克斯坦",
    //                        "code"    => "+233",
    //                    ],
    //                ],
    //            ],
    //            [
    //                "type" => "X",
    //                "list" => [
    //                    [
    //                        "country" => "西班牙",
    //                        "code"    => "+34",
    //                    ],
    //                    [
    //                        "country" => "西萨摩亚",
    //                        "code"    => "+685",
    //                    ],
    //                    [
    //                        "country" => "希腊",
    //                        "code"    => "+30",
    //                    ],
    //                    [
    //                        "country" => "新加坡",
    //                        "code"    => "+65",
    //                    ],
    //                    [
    //                        "country" => "新西兰",
    //                        "code"    => "+64",
    //                    ],
    //                    [
    //                        "country" => "匈牙利",
    //                        "code"    => "+36",
    //                    ],
    //                    [
    //                        "country" => "叙利亚",
    //                        "code"    => "+963",
    //                    ],
    //                ],
    //            ],
    //            [
    //                "type" => "Y",
    //                "list" => [
    //                    [
    //                        "country" => "牙买加",
    //                        "code"    => "+1876",
    //                    ],
    //                    [
    //                        "country" => "亚美尼亚",
    //                        "code"    => "+374",
    //                    ],
    //                    [
    //                        "country" => "也门",
    //                        "code"    => "+967",
    //                    ],
    //                    [
    //                        "country" => "伊拉克",
    //                        "code"    => "+964",
    //                    ],
    //                    [
    //                        "country" => "伊朗",
    //                        "code"    => "+98",
    //                    ],
    //                    [
    //                        "country" => "以色列",
    //                        "code"    => "+972",
    //                    ],
    //                    [
    //                        "country" => "意大利",
    //                        "code"    => "+39",
    //                    ],
    //                    [
    //                        "country" => "印度",
    //                        "code"    => "+91",
    //                    ],
    //                    [
    //                        "country" => "印度尼西亚",
    //                        "code"    => "+62",
    //                    ],
    //                    [
    //                        "country" => "英国",
    //                        "code"    => "+44",
    //                    ],
    //                    [
    //                        "country" => "约旦",
    //                        "code"    => "+962",
    //                    ],
    //                    [
    //                        "country" => "越南",
    //                        "code"    => "+84",
    //                    ],
    //                ],
    //            ],
    //            [
    //                "type" => "Z",
    //                "list" => [
    //                    [
    //                        "country" => "赞比亚",
    //                        "code"    => "+260",
    //                    ],
    //                    [
    //                        "country" => "扎伊尔",
    //                        "code"    => "+243",
    //                    ],
    //                    [
    //                        "country" => "乍得",
    //                        "code"    => "+235",
    //                    ],
    //                    [
    //                        "country" => "直布罗陀",
    //                        "code"    => "+350",
    //                    ],
    //                    [
    //                        "country" => "智利",
    //                        "code"    => "+56",
    //                    ],
    //                    [
    //                        "country" => "中非共和国",
    //                        "code"    => "+236",
    //                    ],
    //                    [
    //                        "country" => "中国大陆",
    //                        "code"    => "+86",
    //                    ],
    //                    [
    //                        "country" => "中国香港",
    //                        "code"    => "+852",
    //                    ],
    //                    [
    //                        "country" => "中国澳门",
    //                        "code"    => "+853",
    //                    ],
    //                    [
    //                        "country" => "中国台湾",
    //                        "code"    => "+886",
    //                    ],
    //                ],
    //            ],
    //        ];
    //
    //        return $list;
    //    }

    public static function getCountryMobileCodeArray()
    {
        // 获取上面的区号，最终是一个数组
        $list = self::getCountryMobileCode();

        $result = [];
        foreach ($list as $item) {
            foreach ($item['list'] as $country) {
                $result[] = $country['code'];
            }
        }

        // 去重
        $result = array_unique($result);

        return $result;
    }

}
