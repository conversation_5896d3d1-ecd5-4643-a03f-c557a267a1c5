<?php

function aa($a)
{
    echo "<pre>";
    print_r($a);
    echo "</pre>";
}

function bb($a)
{
    echo "<pre>";
    print_r($a);
    echo "</pre>";
    die;
}

return [
    'environment' => 'local',

    'homeUrl' => 'http://gaoxiaojob.dong',

    'easySmsConfig' => [
        'timeout' => 5.0,

        'default'  => [
            'strategy' => \Overtrue\EasySms\Strategies\OrderStrategy::class,

            'gateways' => [
                'qcloud',
            ],
        ],
        'gateways' => [
            'errorlog' => [
                'file' => '/uploads/easy-sms.log',
            ],
            'qcloud'   => [
                'sdk_app_id' => '1400264018',
                'app_key'    => '28a71b25a0ba25b91893695d5668d7ed',
                'sign_name'  => '高校人才网',
            ],
        ],
    ],

    // 微信相关配置
    'wx'            => [
        // 开放平台相关配置
        'open'   => [
            'corp_id'              => '服务商的corpid',
            'secret'               => '服务商的secret，在服务商管理后台可见',
            'suite_id'             => '以ww或wx开头应用id',
            'suite_secret'         => '应用secret',
            'token'                => '应用的Token',
            'aes_key'              => '应用的EncodingAESKey',
            'reg_template_id'      => '注册定制化模板ID',
            'redirect_uri_install' => '安装应用的回调url（可选）',
            'redirect_uri_single'  => '单点登录回调url （可选）',
            'redirect_uri_oauth'   => '网页授权第三方回调url （可选）',
        ],
        'public' => [
            'app_id'  => 'wx1c3da30d5b160509',
            'secret'  => '4c613f363aab431f8c1d963eaeda71ba',
            'token'   => 'AInCqm2CwRn3U1D1hIiv9dzA',
            'aes_key' => 'gCiZQOKeyvAInCqm2CwRn3U1D1hIiv9dzAyftpe0jds',
        ],
    ],

    /**
     * 腾讯云的相关配置
     */
    'tencentCloud'  => [
        'secretId'  => 'AKIDEH3aqpa6LJ0RaT4OSFl0zh8NQCsQny22',
        'secretKey' => 'tX2LYbPxFI6dvyrWw62jEZhZBewzlGUS',
        // 图形验证码相关配置
        'captcha'   => [
            'CaptchaAppId' => 2042506576,
            'AppSecretKey' => '0M6LBsNzayLkzMytXsjDdtg**',
        ],

    ],

    // 测试环境可以发送的手机号
    'smsTestMobile' => [
        15902090572,
    ],
];
