<?php

namespace common\service\job;

use admin\models\Announcement;
use admin\models\Dictionary;
use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseJobContact;
use common\base\models\BaseJobContactSynergy;
use common\base\models\BaseJobEdit;
use common\base\models\BaseJobExtra;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseJobKeywordsPreprocess;
use common\base\models\BaseMajor;
use common\base\models\BaseMemberSearchLog;
use common\base\models\BaseWelfareLabel;
use common\components\MessageException;
use common\helpers\ArrayHelper;
use common\helpers\IpHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\libs\Cache;
use common\service\search\PcJobListService;
use queue\Producer;
use Yii;
use common\base\models\BaseAdmin;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseMember;
use common\helpers\FormatConverter;
use common\helpers\ValidateHelper;
use common\service\CommonService;
use yii\base\Exception;
use yii\db\conditions\AndCondition;

/**
 * 职位服务类
 * 介绍：当前服务类用于职位的操作，其他业务切记不要涉及。
 * 优化人：shannon
 * 调用规则：
 *      1、设置操作平台来源
 *      2、设置操作方法，即你要做什么
 */
class JobService extends CommonService
{
    const KEYWORD_SCORE = 25;   // 满足分数进入热词
    /** 定义操作 */
    const JOB_LIST      = 101;//职位列表
    const JOB_INFO      = 201;//职位信息
    const JOB_ADD       = 301;//职位添加
    const JOB_EDIT      = 401;//职位编辑
    const JOB_INIT_EDIT = 501;//职位编辑初始化
    const JOB_DETAIL    = 601;//职位详情
    const JOB_DELETE    = 701;//职位删除
    const JOB_COPY      = 801;//职位复制
    const JOB_ADD_TEMP  = 901;//职位临时添加

    /** 设置传入数据 */
    private $initData;
    /**
     * 原始职位信息
     * @var BaseJob
     */
    private $oldJobInfo;
    /** 设置传入数据是否初始化 */
    private $initDataBool = false;
    /** 允许调用的端口 */
    private $allowPlatform = [
        //self::PLATFORM_WEB_COMPANY,//暂时单位端没有接入，先关掉
        self::PLATFORM_ADMIN,
    ];
    /**
     * 登录账号信息
     * @var BaseMember
     */
    private $loginMemberInfo;
    /**
     * 登录账号信息
     * @var BaseAdmin
     */
    private $loginAdminInfo;
    /**
     * 当前登录账号信息
     * @var BaseAdmin
     */
    private $loginAccountInfo;
    /**
     * 单位信息
     * @var BaseCompany
     */
    private $companyInfo;
    /** 职位ID-  新增后的职位ID或者是编辑的职位ID*/
    private $jobId;
    /** 职位-公告-单位-联系人-协同信息 */
    private $allInfo;
    /** 职位-公告-单位信息 */
    private $jobAnnouncementCompanyInfo;
    /** 是否进过审核 */
    private $isAudit = false;

    /**
     * 设置数据源
     * @param $data array
     *
     */
    public function setData($data, $isImportData = false)
    {
        //这里希望在这个类里面用下划线方式命名，所以需要不管是否满足都去转换一下
        $data           = FormatConverter::convertHump($data);
        $this->initData = $data;
        //导入数据需要经过预处理
        if ($isImportData) {
            //先处理一下数据
            $this->importData();
        }

        //初始化数据源
        $this->initDataBool = true;

        return $this;
    }

    /**
     * 执行程序
     */
    public function run()
    {
        if (empty($this->operationPlatform)) {
            throw new Exception('请设置操作平台或来源端口');
        }
        //根据设置的操作类型执行相应操作
        //先判断是否设置了操作类型
        if (empty($this->operationType)) {
            throw new Exception('请设置操作类型');
        }
        //这里先限制一下平台,不在允许范围内的不允许操作
        if (!in_array($this->operationPlatform, $this->allowPlatform)) {
            throw new Exception('当前操作平台不允许调用该服务');
        }

        switch ($this->operationType) {
            case self::JOB_LIST:
                $data = $this->list();
                break;
            case self::JOB_INFO:
                $data = $this->info();
                break;
            case self::JOB_ADD:
                $data = $this->add();
                break;
            case self::JOB_ADD_TEMP:
                $data = $this->add_temp();
                break;
            case self::JOB_EDIT:
                $data = $this->edit();
                break;
            case self::JOB_INIT_EDIT:
                $data = $this->init_edit();
                break;
            case self::JOB_DETAIL:
                $data = $this->detail();
                break;
            case self::JOB_DELETE:
                $data = $this->delete();
                break;
            case self::JOB_COPY:
                $data = $this->copy();
                break;
            default:
                $data = [];
                break;
        }

        return $data;
    }

    private function list()
    {
        //获取
        return 1;
    }

    private function info()
    {
        return 1;
    }

    /**
     * 添加职位--纯职位添加
     * 注意：
     *      1、纯职位添加职位是直接添加到职位表中，不会去到临时职位表
     *      2、公告发布的时候会将临时职位表同步到职位表中
     * @return int
     */
    private function add()
    {
        //判断是否初始化数据
        if (!$this->initDataBool) {
            throw new Exception('请先设置数据源');
        }
        //先初始化一下登录账号信息与单位信息
        $this->initInfo(true);
        //检验数据合法性
        $this->dataVerify();
        //调用写表逻辑写入职位表
        $this->addJobTable();
        //写入职位联系人与职位协同
        $this->contact();

        //完成返回
        return true;
    }

    /**
     * 添加职位--临时职位添加
     * 注意：
     *      1、只有公告下去添加新的职位或者编辑以前职位就会产生临时职位去到临时表
     * @return int
     */
    private function add_temp()
    {
        return 1;
    }

    /**
     * 职位编辑
     * @return bool
     * @throws Exception
     */
    private function edit()
    {
        //判断是否初始化数据
        if (!$this->initDataBool) {
            throw new Exception('请先设置数据源');
        }
        if (!isset($this->initData['job_id']) || empty($this->initData['job_id'])) {
            throw new Exception('参数错误');
        }
        //设置一下职位ID
        $this->jobId = $this->initData['job_id'];
        //先初始化一下登录账号信息与单位信息
        $this->initInfo();
        //原始职位信息
        $this->oldJobInfo();
        //检验数据合法性
        $this->dataVerify();
        //特殊验证
        $this->specialVerify();
        //对部分内容进行对比进入审核流程
        $this->filterAudit();
        //调用更新表逻辑写入职位表-部分内容需要经过审核才能写入
        $this->updateJobTable();
        //写入职位联系人与职位协同
        $this->contact();

        //职位编辑更新后操作
        BaseJob::afterUpdate($this->jobId);
        //调用公告编辑更新后操作
        if ($this->oldJobInfo->announcement_id > 0) {
            Producer::afterAnnouncementUpdateJob($this->oldJobInfo->announcement_id);
        }

        return true;
    }

    /**
     * 编辑职位初始化
     */
    private function init_edit()
    {
        //判断是否初始化数据
        if (!$this->initDataBool) {
            throw new Exception('请先设置数据源');
        }
        if (!isset($this->initData['job_id']) || empty($this->initData['job_id'])) {
            throw new Exception('参数错误');
        }
        //设置一下职位ID
        $this->jobId = $this->initData['job_id'];
        //获取职位-公告-单位-联系人-协同信息
        $this->getAllInfo();
        //文本转换
        $this->infoText();

        return $this->allInfo;
    }

    /**
     * 职位详情
     * @return mixed
     * @throws Exception
     */
    private function detail()
    {
        //判断是否初始化数据
        if (!$this->initDataBool) {
            throw new Exception('请先设置数据源');
        }
        if (!isset($this->initData['job_id']) || empty($this->initData['job_id'])) {
            throw new Exception('参数错误');
        }
        //设置一下职位ID
        $this->jobId = $this->initData['job_id'];
        //获取职位-公告-单位-联系人-协同信息
        $this->getAllInfo();
        //文本转换
        $this->infoText();

        return $this->allInfo;
    }

    private function delete()
    {
        return 1;
    }

    private function copy()
    {
        return 1;
    }

    /**
     * 初始化登录的账号信息与单位信息
     */
    private function initInfo($isCreateUser = false)
    {
        //根据平台初始化登录账号信息
        switch ($this->operationPlatform) {
            case self::PLATFORM_WEB_COMPANY:
                $member_id = Yii::$app->user->id;
                //获取登录账号信息
                $this->loginMemberInfo  = BaseMember::findOne($member_id);
                $this->loginAccountInfo = [
                    'id'   => $this->loginMemberInfo->id,
                    'name' => $this->loginMemberInfo->username,
                    'type' => BaseJobEdit::EDITOR_TYPE_COMPANY,
                ];
                //获取单位信息
                $this->companyInfo = BaseCompany::findOne(['member_id' => $member_id]);
                if ($isCreateUser) {
                    //职位创建类型
                    $this->initData['create_type'] = BaseJob::CREATE_TYPE_SELF;
                    //职位创建人ID
                    $this->initData['create_id'] = $this->loginMemberInfo->id;
                    //职位创建人名称
                    $this->initData['creator'] = $this->loginMemberInfo->username;
                }
                break;
            case self::PLATFORM_ADMIN:
                $admin_id = Yii::$app->user->id;
                //获取登录账号信息
                $this->loginAdminInfo   = BaseAdmin::findOne($admin_id);
                $this->loginAccountInfo = [
                    'id'   => $this->loginAdminInfo->id,
                    'name' => $this->loginAdminInfo->name,
                    'type' => BaseJobEdit::EDITOR_TYPE_PLATFORM,
                ];
                //获取单位信息
                $this->companyInfo = BaseCompany::findOne($this->initData['company_id']);
                if ($isCreateUser) {
                    //职位创建类型
                    $this->initData['create_type'] = BaseJob::CREATE_TYPE_AGENT;
                    //职位创建人ID
                    $this->initData['create_id'] = $this->loginAdminInfo->id;
                    //职位创建人名称
                    $this->initData['creator'] = $this->loginAdminInfo->name;
                }
                break;
            default:
                break;
        }

        return true;
    }

    /**
     * 校验数据合法性
     * @return bool|void
     */
    private function dataVerify()
    {
        if (!$this->initDataBool) {
            return true;
        }
        //先校验是否选中了单位传递了单位ID
        if (empty($this->initData['company_id'])) {
            throw new Exception('请选择所属单位');
        }
        //职位名称不能为空
        if (empty($this->initData['name'])) {
            throw new Exception('职位名称不能为空');
        }
        //职位类型不能为空
        if (empty($this->initData['job_category_id'])) {
            throw new Exception('职位类型不能为空');
        }
        //学历要求
        if (empty($this->initData['education_type'])) {
            throw new Exception('请选择学历要求');
        }
        //招聘人数
        if (empty($this->initData['amount'])) {
            throw new Exception('招聘人数不能为空');
        }
        //工作地点必填
        if (empty($this->initData['province_id']) || empty($this->initData['city_id'])) {
            throw new Exception('工作地点不能为空');
        }
        //判断必填参数岗位职责、任职要求
        if (empty($this->initData['duty'])) {
            throw new Exception('岗位职责不能为空');
        }
        if (empty($this->initData['requirement'])) {
            throw new Exception('任职要求不能为空');
        }
        //获取单位信息
        if ($this->companyInfo->is_cooperation == BaseAnnouncement::IS_COOPERATION_NO) {
            if (isset($this->initData['delivery_way'])) {
                //非合作单位去掉delivery_way字段 永远走else分支
                unset($this->initData['delivery_way']);
            }
            if ($this->initData['extra_notify_address']) {
                throw new Exception('非合作单位不支持填写邮件通知地址');
            }
        }
        if (strtotime($this->initData['period_date']) > 1 && strtotime($this->initData['period_date']) < strtotime(date('Y-m-d'))) {
            throw new Exception('职位有效时间不能小于当前时间');
        }
        // 薪资范围(非年薪)
        if ($this->initData['wage_type'] != BaseJob::WAGE_TYPE_YEAR) {
            // 如果非面议(前端直接传了类型过来)
            if ($this->initData['is_negotiable'] == BaseJob::IS_NEGOTIABLE_NO) {
                if (!empty($this->initData['wage_id'])) {
                    $wageInfo                   = BaseDictionary::getMinAndMaxWage($this->initData['wage_id']);
                    $this->initData['min_wage'] = (int)$wageInfo['min'];
                    $this->initData['max_wage'] = (int)$wageInfo['max'];
                }
            }
        }
        //报名方式与通知地址不可同时填写
        if ($this->initData['apply_type'] && $this->initData['extra_notify_address']) {
            throw new Exception('报名方式与投递通知邮箱不可同时填写');
        }
        if ($this->companyInfo->is_cooperation == BaseAnnouncement::IS_COOPERATION_NO && isset($this->initData['delivery_way']) && $this->initData['delivery_way'] == BaseJob::DELIVERY_WAY_PLATFORM) {
            throw new Exception('报名方式填写错误');
        }
        if ($this->initData['delivery_way'] > 0) {
            if ($this->initData['delivery_way'] == BaseJob::DELIVERY_WAY_EMAIL_LINK) {
                if (empty($this->initData['apply_type']) || empty($this->initData['apply_address'])) {
                    throw new Exception('报名方式没有勾选或者投递地址为空');
                }
                $apply_type_arr = explode(',', $this->initData['apply_type']);
                $is_email       = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $apply_type_arr);
                //校验
                if ($is_email) {
                    BaseJob::checkEmailApplyAddress($this->initData['apply_address']);
                    $this->initData['delivery_way'] = BaseJob::DELIVERY_WAY_EMAIL;
                } else {
                    if (!ValidateHelper::isUrl($this->initData['apply_address'])) {
                        throw new Exception('单位报名网址格式错误');
                    }
                    $this->initData['delivery_way'] = BaseJob::DELIVERY_WAY_LINK;
                }
            } else {//delivery_way=1
                $this->initData['apply_type']    = '';
                $this->initData['apply_address'] = '';
                $this->initData['delivery_way']  = BaseJob::DELIVERY_WAY_PLATFORM;
            }
        } else {
            if ($this->initData['apply_type']) {
                $apply_type_arr = explode(',', $this->initData['apply_type']);
                $is_email       = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $apply_type_arr);
                if (empty($this->initData['apply_address'])) {
                    throw new Exception('投递地址不能为空');
                }
                //校验
                if ($is_email) {
                    BaseJob::checkEmailApplyAddress($this->initData['apply_address']);
                    $this->initData['delivery_way'] = BaseJob::DELIVERY_WAY_EMAIL;
                } else {
                    if (!ValidateHelper::isUrl($this->initData['apply_address'])) {
                        throw new Exception('单位报名网址格式错误');
                    }
                    $this->initData['delivery_way'] = BaseJob::DELIVERY_WAY_LINK;
                }
            } else {
                $this->initData['apply_type']    = '';
                $this->initData['apply_address'] = '';
                //判断投递方式
                //if ((!isset($this->initData['source']) || (isset($this->initData['source']) && $this->initData['source'] == 2))) {
                $this->initData['delivery_way'] = BaseJob::DELIVERY_WAY_PLATFORM;
                //} else {
                //    $this->initData['delivery_way'] = 0;
                //}
            }
        }
        $this->initData['extra_notify_address'] = $this->initData['extra_notify_address'] ?: '';
        //检查通知邮箱的格式
        if ($this->initData['extra_notify_address']) {
            $this->initData['extra_notify_address'] = BaseJob::checkEmailApplyAddress($this->initData['extra_notify_address']);
        }

        //        $delivery_limit_type_arr = [];
        //        if (isset($this->initData['delivery_limit_type_education']) && !empty($this->initData['delivery_limit_type_education'])) {
        //            array_push($delivery_limit_type_arr, $this->initData['delivery_limit_type_education']);
        //        }
        //        if (isset($this->initData['delivery_limit_type_file']) && !empty($this->initData['delivery_limit_type_file'])) {
        //            array_push($delivery_limit_type_arr, $this->initData['delivery_limit_type_file']);
        //        }
        //        if (count($delivery_limit_type_arr) > 0) {
        //            $this->initData['delivery_limit_type'] = implode(',', $delivery_limit_type_arr);
        //        }
        //        if (isset($this->initData['delivery_limit_type']) && !empty($this->initData['delivery_limit_type']) && empty($this->initData['delivery_limit_type'])) {
        //            $this->initData['delivery_limit_type'] = $this->initData['delivery_limit_type'];
        //        }

        //判断是否是合作单位
        if ($this->companyInfo->is_cooperation == BaseAnnouncement::IS_COOPERATION_YES) {
            //处理纯职位投递类型
            if ($this->initData['delivery_way'] == 0) {
                $this->initData['delivery_type'] = 0;
            } elseif ($this->initData['delivery_way'] == BaseJob::DELIVERY_WAY_LINK) {
                $this->initData['delivery_type'] = BaseJob::DELIVERY_TYPE_OUTSIDE;
            } else {
                $this->initData['delivery_type'] = BaseJob::DELIVERY_TYPE_INSIDE;
            }
            //验证传递过来的协同设置与职位联系人的合法性----jobContactSynergy与jobContact
            //职位协同设置
            if ($this->initData['job_contact_synergy_ids']) {
                //验证协同账号的合法性
                if (count($this->initData['job_contact_synergy_ids']) > 3) {
                    throw new Exception('协同账号最多设置3个');
                }
                $jobContactSynergyIds = [];
                foreach ($this->initData['job_contact_synergy_ids'] as $item) {
                    $record_result_synergy = BaseCompanyMemberInfo::validateMemberRecordId($this->companyInfo->id,
                        $item, 2);
                    if ($record_result_synergy) {
                        $jobContactSynergyIds[] = $item;
                    }
                }
                $this->initData['job_contact_synergy_ids'] = $jobContactSynergyIds;
            } else {
                $this->initData['job_contact_synergy_ids'] = [];
            }
            //职位联系人
            if (empty($this->initData['job_contact_id'])) {
                throw new Exception('职位联系人必须设置');
            }
            $record_result = BaseCompanyMemberInfo::validateMemberRecordId($this->companyInfo->id,
                $this->initData['job_contact_id'], 3);
            if (!$record_result) {
                throw new Exception('职位联系人设置错误');
            }
            //获取单位主账号信息
            $companyMemberInfo = BaseCompanyMemberInfo::findOne([
                'company_id'          => $this->companyInfo->id,
                'company_member_type' => BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN,
            ]);
            if (!in_array($this->initData['job_contact_id'],
                array_unique(array_merge($this->initData['job_contact_synergy_ids'], [$companyMemberInfo->id])))) {
                throw new Exception('职位联系人必须是协同账号或者是单位主账号');
            }
        } else {
            if ($this->initData['delivery_way'] == 0) {
                $this->initData['delivery_type'] = 0;
            } else {
                $this->initData['delivery_type'] = BaseJob::DELIVERY_TYPE_OUTSIDE;
            }
        }
    }

    /**
     * 特殊检验--一般是编辑出现的检验
     */
    private function specialVerify()
    {
        //判断职位投递性质
        if ($this->oldJobInfo->delivery_type != $this->initData['delivery_type']) {
            throw new Exception('你编辑使职位投递类型站内外发生变化，导致修改失败');
        }
    }

    /**
     * 对职位的联系人与协同人进行处理
     * @param $params
     */
    private function contact()
    {
        if ($this->companyInfo->is_cooperation == BaseAnnouncement::IS_COOPERATION_YES) {
            //写入职位联系人与职位协同
            $contact_insert                           = [
                'job_id'          => $this->jobId,
                'company_id'      => $this->companyInfo->id,
                'announcement_id' => $this->initData['announcement_id'],
            ];
            $contact_synergy_insert                   = $contact_insert;
            $contact_insert['company_member_info_id'] = $this->initData['job_contact_id'];
            BaseJobContact::add($contact_insert);
            $contact_synergy_insert['company_member_info_id'] = $this->initData['job_contact_synergy_ids'];
            BaseJobContactSynergy::addBatch($contact_synergy_insert);
        }
    }

    /**
     * 添加职位正式的表
     * @return bool
     */
    private function addJobTable()
    {
        $model                       = new BaseJob();
        $model->create_type          = $this->initData['create_type'];
        $model->create_id            = $this->initData['create_id'];
        $model->creator              = $this->initData['creator'];
        $model->member_id            = $this->companyInfo->member_id;
        $model->company_id           = $this->companyInfo->id;
        $model->name                 = $this->initData['name'] ?: "";
        $model->code                 = $this->initData['code'] ?: "";
        $model->announcement_id      = $this->initData['announcement_id'] ?: 0;
        $model->job_category_id      = $this->initData['job_category_id'] ?: "";
        $model->education_type       = $this->initData['education_type'] ?: "";
        $model->major_id             = $this->initData['major_id'] ?: "";
        $model->nature_type          = $this->initData['nature_type'] ?: "";
        $model->wage_type            = $this->initData['wage_type'] ?: "";
        $model->is_negotiable        = $this->initData['is_negotiable'] ?: "";
        $model->min_wage             = $this->initData['min_wage'] ?: "";
        $model->max_wage             = $this->initData['max_wage'] ?: "";
        $model->experience_type      = $this->initData['experience_type'] ?: "";
        $model->age_type             = $this->initData['age_type'] ?: '';
        $model->title_type           = $this->initData['title_type'] ?: "";
        $model->political_type       = $this->initData['political_type'] ?: "";
        $model->abroad_type          = $this->initData['abroad_type'] ?: "";
        $model->amount               = $this->initData['amount'] ?: "";
        $model->department           = $this->initData['department'] ?: "";
        $model->province_id          = $this->initData['province_id'] ?: "";
        $model->city_id              = $this->initData['city_id'] ?: "";
        $model->district_id          = $this->initData['district_id'] ?: "";
        $model->address              = $this->initData['address'] ?: "";
        $model->welfare_tag          = $this->initData['welfare_tag'] ?: '';
        $model->period_date          = $this->initData['period_date'] ?: "0000-00-00 00:00:00";
        $model->duty                 = $this->initData['duty'] ?: "";
        $model->remark               = $this->initData['remark'] ?: "";
        $model->requirement          = $this->initData['requirement'] ?: "";
        $model->apply_type           = $this->initData['apply_type'] ?: "";
        $model->apply_address        = $this->initData['apply_address'] ?: '';
        $model->status               = $this->initData['status'] ?: BaseJob::STATUS_WAIT;
        $model->is_show              = BaseJob::IS_SHOW_YES;
        $model->is_article           = BaseJob::IS_ARTICLE_NO;
        $model->audit_status         = $this->initData['audit_status'] ?: BaseJob::AUDIT_STATUS_WAIT;
        $model->apply_audit_time     = $this->initData['audit_status'] == BaseJob::AUDIT_STATUS_WAIT_AUDIT ? CUR_DATETIME : '0000-00-00 00:00:00';
        $model->file_ids             = $this->initData['file_ids'] ?: '';
        $model->delivery_limit_type  = $this->initData['delivery_limit_type'] ?: '';
        $model->delivery_type        = $this->initData['delivery_type'];
        $model->extra_notify_address = $this->initData['extra_notify_address'] ?: '';
        $model->delivery_way         = $this->initData['delivery_way'];
        //判断操作平台为运营平台
        if ($this->operationPlatform == self::PLATFORM_ADMIN) {
            $model->establishment_type = $this->initData['establishment_type'] ?: '';
            $model->is_establishment   = !empty($this->initData['establishment_type']) ? BaseJob::IS_ESTABLISHMENT_YES : BaseJob::IS_ESTABLISHMENT_NO;
        } else {
            $model->is_establishment = BaseJob::IS_ESTABLISHMENT_NO;
        }
        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }

        //设置一下写入后职位ID
        $this->jobId = $model->id;

        //写一条职位附属表逻辑
        BaseJobExtra::insertData([
            'job_id'          => $model->id,
            'announcement_id' => $this->initData['announcement_id'],
            'company_id'      => $this->companyInfo->id,
        ]);

        return true;
    }

    /**
     * 更新正式表的职位
     * @return bool
     */
    private function updateJobTable()
    {
        $model                       = BaseJob::findOne($this->jobId);
        $model->update_time          = CUR_DATETIME;
        $model->period_date          = $this->initData['period_date'] == '详见正文' ? '0000-00-00 00:00:00' : $this->initData['period_date'];
        $model->major_id             = $this->initData['major_id'];
        $model->nature_type          = $this->initData['nature_type'];
        $model->is_negotiable        = $this->initData['is_negotiable'];
        $model->wage_type            = $this->initData['wage_type'];
        $model->min_wage             = $this->initData['min_wage'];
        $model->max_wage             = $this->initData['max_wage'];
        $model->experience_type      = $this->initData['experience_type'];
        $model->title_type           = $this->initData['title_type'];
        $model->age_type             = $this->initData['age_type'];
        $model->political_type       = $this->initData['political_type'];
        $model->abroad_type          = $this->initData['abroad_type'];
        $model->amount               = $this->initData['amount'];
        $model->province_id          = $this->initData['province_id'];
        $model->city_id              = $this->initData['city_id'];
        $model->district_id          = $this->initData['district_id'] ?: 0;
        $model->address              = $this->initData['address'];
        $model->welfare_tag          = $this->initData['welfare_tag'];
        $model->department           = $this->initData['department'];
        $model->announcement_id      = $this->initData['announcement_id'];
        $model->job_category_id      = $this->initData['job_category_id'];
        $model->education_type       = $this->initData['education_type'];
        $model->name                 = $this->initData['name'];
        $model->file_ids             = $this->initData['file_ids'];
        $model->delivery_limit_type  = $this->initData['delivery_limit_type'];
        $model->apply_type           = $this->initData['apply_type'] ?: '';
        $model->apply_address        = $this->initData['apply_address'] ?: '';
        $model->delivery_type        = $this->initData['delivery_type'];
        $model->delivery_way         = $this->initData['delivery_way'] ?: '';
        $model->extra_notify_address = $this->initData['extra_notify_address'] ?: '';
        //判断操作平台为运营平台
        if ($this->operationPlatform == self::PLATFORM_ADMIN) {
            $model->establishment_type = $this->initData['establishment_type'] ?: '';
            $model->is_establishment   = !empty($this->initData['establishment_type']) ? BaseJob::IS_ESTABLISHMENT_YES : BaseJob::IS_ESTABLISHMENT_NO;
        } else {
            $model->is_establishment = BaseJob::IS_ESTABLISHMENT_NO;
        }
        //判断是否进入审核流程
        if ($this->isAudit) {
            //进入审核流程
            if ($this->oldJobInfo->status != BaseJob::STATUS_ONLINE) {
                $model->status = BaseJob::STATUS_WAIT_AUDIT;
            }
            $model->audit_status     = BaseJob::AUDIT_STATUS_WAIT_AUDIT;
            $model->apply_audit_time = CUR_DATETIME;
            // 公告+职位模式编辑
            if ($this->oldJobInfo->is_article == BaseJob::IS_ARTICLE_YES) {
                // 修改公告审核状态
                $announcementModel               = BaseAnnouncement::findOne($this->oldJobInfo->announcement_id);
                $announcementModel->audit_status = BaseAnnouncement::STATUS_AUDIT_AWAIT;
                if (!$announcementModel->save()) {
                    throw new Exception($announcementModel->getFirstErrorsMessage());
                }
                //修改公告下的职位审核状态
                BaseJob::setJobAuditStatus($this->oldJobInfo->announcement_id);
            }
        } else {
            //不用审核流程 那就不管是纯职位还是公告+职位模式都直接修改
            if (!in_array($this->oldJobInfo->status, BaseJob::JOB_HISTORY_STATUS)) {
                $model->status       = BaseJob::STATUS_WAIT_AUDIT;
                $model->audit_status = BaseJob::AUDIT_STATUS_WAIT_AUDIT;
            }
        }

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }

        return true;
    }

    /**
     * 对于职位编辑的一些字段进行对比进入审核流程
     */
    private function filterAudit()
    {
        //编辑前的内容【岗位职责、任职要求、其他说明】
        $old_duty        = $this->oldJobInfo->duty;
        $old_requirement = $this->oldJobInfo->requirement;
        $old_remark      = $this->oldJobInfo->remark;
        //编辑后新的内容
        $new_duty        = $this->initData['duty'];
        $new_requirement = $this->initData['requirement'];
        $new_remark      = $this->initData['remark'];
        //对比内容
        $duty        = $old_duty != $new_duty ? 1 : 0;
        $requirement = $old_requirement != $new_requirement ? 1 : 0;
        $remark      = $old_remark != $new_remark ? 1 : 0;
        //如果有一项不一样就进入审核流程
        if ($duty || $requirement || $remark) {
            $this->isAudit      = true;
            $changeModifyBefore = [];
            $changeModifyAfter  = [];
            //编辑内容
            $edit_content = [];
            if ($duty) {
                $edit_content['duty']                                                                      = $new_duty;
                $changeModifyBefore[BaseJobHandleLog::LOG_TEXT_LIST[BaseJobHandleLog::LOG_TEXT_LIST_DUTY]] = $old_duty;
                $changeModifyAfter[BaseJobHandleLog::LOG_TEXT_LIST[BaseJobHandleLog::LOG_TEXT_LIST_DUTY]]  = $new_duty;
            }
            if ($requirement) {
                $edit_content['requirement']                                                                      = $new_requirement;
                $changeModifyBefore[BaseJobHandleLog::LOG_TEXT_LIST[BaseJobHandleLog::LOG_TEXT_LIST_REQUIREMENT]] = $old_requirement;
                $changeModifyAfter[BaseJobHandleLog::LOG_TEXT_LIST[BaseJobHandleLog::LOG_TEXT_LIST_REQUIREMENT]]  = $new_requirement;
            }
            if ($remark) {
                $edit_content['remark']                                                                      = $new_remark;
                $changeModifyBefore[BaseJobHandleLog::LOG_TEXT_LIST[BaseJobHandleLog::LOG_TEXT_LIST_REMARK]] = $old_remark;
                $changeModifyAfter[BaseJobHandleLog::LOG_TEXT_LIST[BaseJobHandleLog::LOG_TEXT_LIST_REMARK]]  = $new_remark;
            }
            //写入职位编辑表
            $job_edit = [
                'job_id'          => $this->jobId,
                'add_time'        => date('Y-m-d H:i:s'),
                'status'          => BaseJobEdit::STATUS_ONLINE,
                'edit_content'    => json_encode($edit_content),
                'editor_id'       => $this->loginAccountInfo['id'],
                'editor_type'     => $this->loginAccountInfo['type'],
                'editor'          => $this->loginAccountInfo['name'],
                'announcement_id' => $this->oldJobInfo['announcement_id'] ?: 0,
            ];
            // 检测之前是否有职位编辑内容
            $jobEditInfo = BaseJobEdit::findOne(['job_id' => $this->jobId]);
            if ($jobEditInfo) {
                $condition = ['id' => $jobEditInfo->id];
                BaseJobEdit::updateAll($job_edit, $condition);
            } else {
                BaseJobEdit::createInfo($job_edit);
            }
            //写入日志-根据下面注释的
            $handleBefore = json_encode($changeModifyBefore);
            $handleAfter  = json_encode($changeModifyAfter);
            $jobHandleLog = [
                'add_time'      => date('Y-m-d H:i:s'),
                'job_id'        => $this->jobId,
                'handle_type'   => strval(BaseJobHandleLog::HANDLE_TYPE_EDIT),
                'handler_type'  => BaseJobHandleLog::HANDLER_TYPE_PLAT,
                'handler_id'    => $this->loginAccountInfo['id'],
                'handler_name'  => $this->loginAccountInfo['name'],
                'handle_before' => $handleBefore,
                'handle_after'  => $handleAfter,
                'ip'            => IpHelper::getIpInt(),
            ];
            BaseJobHandleLog::createInfo($jobHandleLog);
            if ($this->oldJobInfo->is_article == BaseJob::IS_ARTICLE_YES) {
                //创建公告操作日志
                $handleLogData = [
                    'announcement_id' => $this->oldJobInfo->announcement_id,
                    'handle_type'     => strval(BaseAnnouncementHandleLog::HANDLE_TYPE_EDIT),
                    'editor_type'     => BaseAnnouncement::TYPE_EDITOR_JOB,
                    'handler_type'    => BaseAnnouncementHandleLog::HANDLER_TYPE_PLAT,
                    'handler_id'      => $this->loginAccountInfo['id'],
                    'handler_name'    => $this->loginAccountInfo['name'],
                    'handle_before'   => $handleBefore,
                    'handle_after'    => $handleAfter,
                ];
                BaseAnnouncementHandleLog::createInfo($handleLogData);
            }
        }
    }

    private function addJobTempTable()
    {
        return true;
    }

    /**
     * 根据职位ID获取职位-公告-单位信息
     *      这里充分利用表关系进行数据获取
     * @param $jobId
     */
    private function getJobAnnouncementCompanyInfo()
    {
        $this->jobAnnouncementCompanyInfo = BaseJob::find()
            ->andWhere(['id' => $this->jobId])
            ->with([
                'company',
                'announcement',
            ])
            ->asArray()
            ->one();
    }

    /**
     * 根据职位ID获取职位-公告-单位-联系人-协同信息
     *      这里充分利用表关系进行数据获取
     * @param $jobId
     */
    private function getAllInfo()
    {
        $this->allInfo = BaseJob::find()
            ->andWhere(['id' => $this->jobId])
            ->with([
                'company',
                'announcement',
                'contact',
                'contactSynergy',
            ])
            ->asArray()
            ->one();
    }

    /**
     * 处理职位-公告-单位-联系人-协同的转换文本及数据类型处理
     */
    private function infoText()
    {
        if ($this->allInfo['is_article'] == 1) {
            $this->allInfo['is_article_text'] = "公告+职位模式";
        } else {
            $this->allInfo['is_article_text'] = "纯职位模式";
        }
        $this->allInfo['status_text'] = BaseJob::JOB_STATUS_NAME[$this->allInfo['status']];
        if ($this->allInfo['is_stick'] == 1) {
            $this->allInfo['is_stick_text'] = "长期";
        } else {
            $this->allInfo['is_stick_text'] = "非长期";
        }
        $this->allInfo['job_category_id_text'] = $this->allInfo['job_category_id'] ? BaseCategoryJob::getParentName($this->allInfo['job_category_id']) . '-' . BaseCategoryJob::getName($this->allInfo['job_category_id']) : '';
        $this->allInfo['education_type_text']  = empty($this->allInfo['education_type']) ? '-' : ($this->allInfo['education_type'] < 1 ? '学历不限' : BaseDictionary::getEducationName($this->allInfo['education_type']));
        $this->allInfo['major_id_arr']         = explode(',', $this->allInfo['major_id']);
        $this->allInfo['major_id_text']        = empty($this->allInfo['major_id']) ? '专业不限' : BaseMajor::getAllMajorName($this->allInfo['major_id_arr']);
        $this->allInfo['nature_type_text']     = empty($this->allInfo['nature_type']) ? '-' : BaseDictionary::getNatureName($this->allInfo['nature_type']);
        $this->allInfo['wage_type_text']       = empty($this->allInfo['wage_type']) ? '-' : BaseJob::JOB_WAGE_TYPE_NAME[$this->allInfo['wage_type']];
        $this->allInfo['experience_type_text'] = $this->allInfo['experience_type'] < 1 ? '经验不限' : BaseDictionary::getExperienceName($this->allInfo['experience_type']);
        $this->allInfo['title_type_text']      = $this->allInfo['title_type'] < 1 ? '职称不限' : BaseDictionary::getTitleName($this->allInfo['title_type']);
        $this->allInfo['political_type_text']  = $this->allInfo['political_type'] < 1 ? '不限' : BaseDictionary::getPoliticalStatusName($this->allInfo['political_type']);
        $this->allInfo['district_id_text']     = $this->allInfo['district_id'] ? BaseArea::getAreaName($this->allInfo['district_id']) : '';
        $this->allInfo['province_id_text']     = $this->allInfo['province_id'] ? BaseArea::getAreaName($this->allInfo['province_id']) : '';
        $this->allInfo['city_id_text']         = $this->allInfo['city_id'] ? BaseArea::getAreaName($this->allInfo['city_id']) : '';
        switch ($this->allInfo['offline_type']) {
            case BaseJob::OFFLINE_TYPE_AUTO:
                $this->allInfo['offline_type_text'] = '自动下线';
                break;
            case BaseJob::OFFLINE_TYPE_HAND:
                $this->allInfo['offline_type_text'] = '手动下线';
                break;
            case BaseJob::OFFLINE_TYPE_VIOLATION:
                $this->allInfo['offline_type_text'] = '违规';
                break;
            default:
                $this->allInfo['offline_type_text'] = '-';
                break;
        }
        switch ($this->allInfo['gender_type']) {
            case 1:
                $this->allInfo['gender_type_text'] = "男";
                break;
            case 2:
                $this->allInfo['gender_type_text'] = "女";
                break;
            default:
                $this->allInfo['gender_type_text'] = "不限";
                break;
        }
        switch ($this->allInfo['abroad_type']) {
            case 1:
                $this->allInfo['abroad_type_text'] = "要求";
                break;
            case 2:
                $this->allInfo['abroad_type_text'] = "不要求";
                break;
            default:
                $this->allInfo['abroad_type_text'] = "不限";
                break;
        }
        $this->allInfo['welfare_tag_text'] = $this->allInfo['welfare_tag'] ? BaseWelfareLabel::getWelfareLabelNames($this->allInfo['welfare_tag']) : '';
        $this->allInfo['wage']             = BaseJob::formatWage($this->allInfo['min_wage'], $this->allInfo['max_wage'],
            $this->allInfo['wage_type']);
        //投递类型文本
        if (empty($this->allInfo['delivery_type'])) {//职位没有就显示公告的投递类型
            if (!empty($this->allInfo['announcement_id'])) {
                $this->allInfo['delivery_type_text'] = BaseAnnouncement::DELIVERY_TYPE_NAME[$this->allInfo['announcement']['delivery_type']] ?? '-';
            } else {
                $this->allInfo['delivery_type_text'] = '-';
            }
        } else {//显示职位的投递类型
            $this->allInfo['delivery_type_text'] = BaseJob::DELIVERY_TYPE_NAME[$this->allInfo['delivery_type']] ?? '-';
        }
        //投递方式
        $this->allInfo['delivery_way_text'] = '-';
        if ($this->allInfo['delivery_way']) {//显示职位的投递类型
            if ($this->allInfo['delivery_way'] == BaseJob::DELIVERY_WAY_PLATFORM) {
                $this->allInfo['delivery_way_text'] = BaseJob::DELIVERY_WAY_SELECT_NAME[BaseJob::DELIVERY_WAY_PLATFORM];
            } else {
                $this->allInfo['delivery_way']      = strval(BaseJob::DELIVERY_WAY_EMAIL_LINK);
                $this->allInfo['delivery_way_text'] = BaseJob::DELIVERY_WAY_SELECT_NAME[BaseJob::DELIVERY_WAY_EMAIL_LINK];
            }
        } else {//职位没有就显示公告的投递类型
            if (!empty($this->allInfo['announcement_id'])) {
                if ($this->allInfo['announcement']['delivery_way'] == BaseAnnouncement::DELIVERY_WAY_PLATFORM) {
                    $this->allInfo['delivery_way_text'] = BaseAnnouncement::DELIVERY_WAY_SELECT_NAME[$this->allInfo['announcement']['delivery_way']];
                } elseif (in_array($this->allInfo['announcement']['delivery_way'],
                    BaseAnnouncement::DELIVERY_WAY_EMAIL_LINK_LIST)) {
                    $this->allInfo['delivery_way_text'] = BaseAnnouncement::DELIVERY_WAY_SELECT_NAME[BaseAnnouncement::DELIVERY_WAY_EMAIL_LINK];
                    $this->allInfo['delivery_way']      = strval(BaseAnnouncement::DELIVERY_WAY_EMAIL_LINK);
                }
            }
        }
        if ($this->allInfo['period_date'] == TimeHelper::ZERO_TIME) {
            $this->allInfo['period_date'] = '详见正文';
        }
        if ($this->allInfo['release_time'] == TimeHelper::ZERO_TIME) {
            $this->allInfo['release_time'] = $this->allInfo['add_time'];
        }
        if ($this->allInfo['experience_type'] == '0') {
            $this->allInfo['experience_type'] = '';
        }
        if ($this->allInfo['political_type'] == '0') {
            $this->allInfo['political_type'] = '';
        }
        if ($this->allInfo['abroad_type'] == '0') {
            $this->allInfo['abroad_type'] = '';
        }
        if ($this->allInfo['nature_type'] == '0') {
            $this->allInfo['nature_type'] = '';
        }
        if ($this->allInfo['wage_type'] == '0') {
            $this->allInfo['wage_type'] = '';
        }
        if ($this->allInfo['age_type'] == '0') {
            $this->allInfo['age_type'] = '';
        }
        if ($this->allInfo['title_type'] == '0') {
            $this->allInfo['title_type'] = '';
        }
        if ($this->allInfo['min_wage'] == '0') {
            $this->allInfo['min_wage'] = '';
        }
        if ($this->allInfo['max_wage'] == '0') {
            $this->allInfo['max_wage'] = '';
        }
        if ($this->allInfo['min_age'] == '0') {
            $this->allInfo['min_age'] = '';
        }
        if ($this->allInfo['max_age'] == '0') {
            $this->allInfo['max_age'] = '';
        }
        if ($this->allInfo['district_id'] == '0') {
            $this->allInfo['district_id'] = '';
        }
        if ($this->allInfo['period_date'] == TimeHelper::ZERO_TIME) {
            $this->allInfo['period_date'] = '';
        }
        if ($this->allInfo['amount'] == '-1') {
            $this->allInfo['amount'] = '若干';
        }
        $this->allInfo['age_type']       = $this->allInfo['age_type'] ?: '-';
        $this->allInfo['department']     = $this->allInfo['department'] ?: '-';
        $this->allInfo['remark']         = $this->allInfo['remark'] ?: '-';
        $this->allInfo['code']           = $this->allInfo['code'] ?: '-';
        $this->allInfo['applyAddress']   = $this->allInfo['apply_address'] ?: '';
        $this->allInfo['applyTypeTitle'] = BaseJob::getApplyTypeName($this->allInfo['apply_type']) ?: '';
        //薪资wage_id回显
        if ($this->allInfo['is_negotiable'] != 1) {
            $this->allInfo['wage_id'] = (string)BaseJob::getWageId($this->allInfo['min_wage'],
                $this->allInfo['max_wage']);
        }
        //查询福利标签
        $this->allInfo['welfare_tage']                  = BaseWelfareLabel::getWelfareLabelByIdNames($this->allInfo['welfare_tag']);
        $this->allInfo['extra_notify_address']          = $this->allInfo['extra_notify_address'] ?: '';
        $this->allInfo['company']['nature']             = intval($this->allInfo['company']['nature']);
        $this->allInfo['company']['nature_text']        = BaseDictionary::getCompanyNatureName($this->allInfo['company']['nature']);
        $this->allInfo['company']['type_text']          = BaseDictionary::getCompanyTypeName($this->allInfo['company']['type']);
        $this->allInfo['area_name']                     = $this->allInfo['province_id_text'] . $this->allInfo['city_id_text'] . $this->allInfo['district_id_text'];
        $this->allInfo['company']['delivery_type_text'] = BaseCompany::DELIVERY_TYPE_NAME[$this->allInfo['company']['delivery_type']];

        //投递限制
        $this->allInfo['delivery_limit_type_text'] = '';
        if ($this->allInfo['delivery_limit_type']) {
            $delivery_limit_type_arr      = explode(',', $this->allInfo['delivery_limit_type']);
            $delivery_limit_type_name_arr = [];
            foreach ($delivery_limit_type_arr as $val) {
                array_push($delivery_limit_type_name_arr, BaseJob::DELIVERY_LIMIT_LIST[$val]);
            }
            $this->allInfo['delivery_limit_type_text'] = implode(';', $delivery_limit_type_name_arr);
        }

        //职位附件
        if ($this->allInfo['file_ids']) {
            $this->allInfo['file_list'] = BaseAnnouncement::getAppendixList($this->allInfo['file_ids']);
        } else {
            if (!empty($this->allInfo['announcement_id'])) {
                $this->allInfo['file_list'] = BaseAnnouncement::getAppendixList($this->allInfo['announcement']['file_ids']);
            } else {
                $this->allInfo['file_list'] = [];
            }
        }
        // 编制相关文案拼接
        if ($this->allInfo['is_establishment'] == BaseJob::IS_ESTABLISHMENT_YES) {
            $this->allInfo['is_establishment_text'] = BaseDictionary::getEstablishmentName($this->allInfo['establishment_type']);
        }
    }

    /**
     * 原始职位信息
     */
    private function oldJobInfo()
    {
        $this->oldJobInfo = BaseJob::findOne($this->jobId);
        if (!$this->oldJobInfo) {
            throw new Exception('职位信息不存在');
        }
    }

    /**
     * 导入数据预处理
     */
    private function importData()
    {
    }

    /**
     * 重置热词关键字
     * @return void
     */
    public static function resetJobKeywords()
    {
        // 每隔7天重置一次
        if (Cache::incr(Cache::JOB_SEARCH_KEYWORDS . ':num') % 7 == 0) {
            $cacheKey = Cache::JOB_SEARCH_KEYWORDS;
            // 取符合可进入下周期的热词列表
            $hotWordList = Cache::zRangeByScore($cacheKey, self::KEYWORD_SCORE, '+inf');
            $hotWordList = ArrayHelper::organizeZRevRangeData($hotWordList);
            Cache::delete($cacheKey);

            // 删除所有数据，保证下周期id的顺序
            BaseJobKeywordsPreprocess::deleteAll();
            // 直接入库，保留上周期达标的热词
            foreach ($hotWordList as $hotWord => $score) {
                BaseJobKeywordsPreprocess::insertOne([
                    'keywords' => strval($hotWord),
                ]);
            }
        }
    }

    /**
     * 选定预热关键字
     * @throws MessageException
     */
    public static function preprocessJobKeywords()
    {
        self::resetJobKeywords();   // 重置数据，达到周期，将满足搜索次数的进入下个周期，清空表后从新入库
        self::insertJobKeywords();  // 判断当前周期是否有满足搜索次数的热词，判断是否需要入库
        self::preprocessJobIds();   // 将热词库里面的数据分组，让另一个脚本分配更新对应jobid
    }

    /**
     * 将搜索次数达到配指数的热词入库
     * @return void
     * @throws MessageException
     */
    private static function insertJobKeywords()
    {
        $searchKeys      = Cache::zRangeByScore(Cache::JOB_SEARCH_KEYWORDS, self::KEYWORD_SCORE, '+inf');
        $searchKeys      = ArrayHelper::organizeZRevRangeData($searchKeys);
        $needAddKeywords = $searchKeys ? array_keys($searchKeys) : [];
        if (!empty($needAddKeywords)) {
            $hasKeywords = BaseJobKeywordsPreprocess::find()
                ->andWhere(new AndCondition([
                    [
                        'in',
                        'keywords',
                        $needAddKeywords,
                    ],
                ]))
                ->select(['keywords'])
                ->column();

            $needAddKeywords = array_diff($needAddKeywords, $hasKeywords);

            // 批量导入
            if ($needAddKeywords) {
                foreach ($needAddKeywords as $keyword) {
                    BaseJobKeywordsPreprocess::insertOne([
                        'keywords' => strval($keyword),
                    ]);
                }
            }
        }
    }

    /**
     * 将预热到数据按段分好五段
     */
    public static function preprocessJobIds()
    {
        $keyWordList = BaseJobKeywordsPreprocess::getListByWhere([], [
            'keywords',
            'id',
        ]);
        $cacheInfo   = [];
        foreach ($keyWordList as $keyWordInfo) {
            $cacheIdKey               = $keyWordInfo['id'] % 5;
            $cacheInfo[$cacheIdKey][] = $keyWordInfo['id'];
        }
        $cacheKey = Cache::JOB_SEARCH_KEYWORDS_JOB_IDS;
        Cache::set($cacheKey, json_encode($cacheInfo), 60 * 60 * 25);
    }

    /**
     * 根据关键字-预热查询职位
     * @return void
     */
    public static function preprocessJobIdsByKeywords($num)
    {
        $where = [];
        if ($num != -1) {
            $cacheKey = Cache::JOB_SEARCH_KEYWORDS_JOB_IDS;
            $idsKey   = Cache::get($cacheKey);
            $idsKey   = json_decode($idsKey, true);
            $ids      = $idsKey[$num];
            if (empty($ids)) {
                throw new MessageException('关键字预热查询没有数据num：' . $num);
            }
            $where[] = [
                'in',
                'id',
                $ids,
            ];
        }

        $keyWordList = BaseJobKeywordsPreprocess::getListByWhere($where, [
            'keywords',
            'id',
        ]);

        foreach ($keyWordList as $keyWordInfo) {
            $searchData = (new PcJobListService())->getListByKeywords($keyWordInfo['keywords']);
            // 如果找不到关键字，就删了相关数据
            if (!$searchData) {
                Cache::zrem(Cache::JOB_SEARCH_KEYWORDS, $keyWordInfo['keywords']);
                BaseJobKeywordsPreprocess::findOne(['id' => $keyWordInfo['id']])
                    ->delete();
            } else {
                $model = BaseJobKeywordsPreprocess::findOne(['id' => $keyWordInfo['id']]);
                if (!$model) {
                    continue;
                }
                $model->job_ids = strval(implode(',', ArrayHelper::getColumn($searchData, 'jobId')));
                $model->save();
            }
        }
    }
}