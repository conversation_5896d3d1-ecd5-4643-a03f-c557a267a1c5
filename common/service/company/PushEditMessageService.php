<?php
/**
 * create user：shannon
 * create time：2025/3/31 上午9:31
 */
namespace common\service\company;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseCompany;
use common\base\models\BaseJob;
use common\components\MessageException;
use common\helpers\UUIDHelper;
use common\libs\WxWork;
use yii\db\Exception;

class PushEditMessageService
{
    /** @var BaseJob */
    private $jobInfo;
    /** @var BaseAnnouncement */
    private $announcementInfo;
    /** @var BaseCompany */
    private $companyInfo;
    private $params;

    //https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a51dd04c-a43a-4db4-abbc-b4e5d55df8dc

    /**
     * 初始化
     * @param $params array ['jobId'=>1,'announcementId'=>2]
     * @throws MessageException
     */
    private function init($params)
    {
        if (!isset($params['jobId']) && isset($params['announcementId']) && $params['announcementId'] <= 0 && $params['jobId'] <= 0) {
            throw new MessageException('参数错误');
        }
        $this->params = $params;

        if (isset($params['announcementId']) && $params['announcementId']) {
            $this->announcementInfo = BaseAnnouncement::findOne($params['announcementId']);
            $this->companyInfo      = BaseCompany::findOne($this->announcementInfo->company_id);
        }
        if (isset($params['jobId']) && $params['jobId']) {
            $this->jobInfo = BaseJob::findOne($params['jobId']);
            if (!$this->companyInfo) {
                $this->companyInfo = BaseCompany::findOne($this->jobInfo->company_id);
            }
        }
    }

    /**
     * 推送消息
     * @param $params
     */
    private function push($kwargs)
    {
        if (!$this->companyInfo) {
            return true;
        }
        WxWork::getInstance()
            ->robotPushCompanyEditMessage([
                'title'            => $kwargs . '通知',
                'content'          => "在单位后台操作了“内容{$kwargs}" . ($this->announcementInfo ? '，公告ID为:' . ($this->announcementInfo->uuid ? $this->announcementInfo->uuid : UUIDHelper::encrypt(UUIDHelper::TYPE_ANNOUNCEMENT,
                            $this->announcementInfo->id)) : '') . ($this->jobInfo ? '，职位ID为:' . ($this->jobInfo->uuid ? $this->jobInfo->uuid : UUIDHelper::encrypt(UUIDHelper::TYPE_JOB,
                            $this->jobInfo->id)) : '') . "”，请相关同事留意！",
                'beforeStatusText' => $this->params['beforeStatusText'],
                'afterStatusText'  => $this->params['afterStatusText'],
                'companyName'      => $this->companyInfo->full_name,
                'companyUuid'      => $this->companyInfo->uuid ? $this->companyInfo->uuid : UUIDHelper::encrypt(UUIDHelper::TYPE_COMPANY,
                    $this->companyInfo->id),

            ]);
    }

    /**
     * 推送下线操作消息
     */
    public function offline($params)
    {
        try {
            $this->init($params);
            $this->push('下线');
        } catch (MessageException $e) {
        }
    }

    /**
     * 推送再发布操作消息
     */
    public function republish($params)
    {
        try {
            $this->init($params);
            $this->push('再发布');
        } catch (MessageException $e) {
        }
    }

    /**
     * 推送发布操作消息
     */
    public function publish($params)
    {
        try {
            $this->init($params);
            $this->push('发布');
        } catch (MessageException $e) {
        }
    }
}