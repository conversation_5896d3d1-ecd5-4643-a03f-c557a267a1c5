<?php
/**
 * create user：伍彦川
 * create time：2025/1/21 19:32
 */
namespace common\service\zhaoPinHuiColumn;

use common\base\models\BaseActivityForm;
use common\base\models\BaseActivityFormIntentionOption;
use common\base\models\BaseActivityFormOptionSign;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyGroupScoreSystem;
use common\base\models\BaseHwActivity;
use common\base\models\BaseHwActivityAnnouncement;
use common\base\models\BaseHwActivityCompany;
use common\base\models\BaseHwSpecialActivity;
use common\base\models\BaseHwSpecialActivityRelation;
use common\base\models\BaseMajor;
use common\components\MessageException;
use common\helpers\ArrayHelper;
use common\helpers\FileHelper;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use common\libs\Cache;
use common\libs\WxMiniApp;
use common\models\HwActivityCompany;
use common\service\search\SearchParamsService;
use queue\Producer;
use yii\db\conditions\AndCondition;

/**
 * 专场相关服务类
 * Class SpecialActivityService
 */
class SpecialActivityService extends BaseService
{
    /**
     * 多个活动关联单个公告
     * @param $announcementId       int    公告id
     * @param $companyId            int    单位id
     * @param $activityAnnouncement string 关联的活动id，多个,隔开
     * @return void
     */
    public static function multipleActivityRelationSingleAnnouncement(
        int    $companyId,
        string $activityAnnouncement = '',
        int    $announcementId = 0
    ) {
        // 两种情况，1.新增，2.删减
        // 1.新增将单位关联表和公告关联表新增就行
        // 2.删减时，需要将公告关联表删除，若该单位在该场活动下仅关联了本条公告，系统需同时取消该单位与该活动的关联。需求2.5有备注
        $currentActivityAnnouncement = BaseHwActivityAnnouncement::find()
            ->where(['announcement_id' => $announcementId])
            ->select([
                'id',
                'announcement_id',
                'company_id',
                'activity_id',
            ])
            ->asArray()
            ->all();

        $newActivityIds     = explode(',', $activityAnnouncement);
        $currentActivityIds = array_column($currentActivityAnnouncement, 'activity_id');
        $delActivityIds     = array_diff($currentActivityIds, $newActivityIds); // 删除的数据
        $addActivityIds     = array_filter(array_diff($newActivityIds, $currentActivityIds)); // 新增的数据

        // 删除逻辑
        BaseHwActivityAnnouncement::deleteAll([
            'company_id'  => $companyId,
            'activity_id' => $delActivityIds,
        ]);

        // 新增的逻辑
        foreach ($addActivityIds as $activityId) {
            $hwActivityAnnouncementModel                  = new BaseHwActivityAnnouncement();
            $hwActivityAnnouncementModel->announcement_id = $announcementId;
            $hwActivityAnnouncementModel->company_id      = $companyId;
            $hwActivityAnnouncementModel->activity_id     = $activityId;
            $hwActivityAnnouncementModel->save();

            $existsCompanyActivity = BaseHwActivityCompany::find()
                ->where(new AndCondition([
                    [
                        '=',
                        'company_id',
                        $companyId,
                    ],
                    [
                        '=',
                        'activity_id',
                        $activityId,
                    ],
                ]))
                ->exists();
            if (!$existsCompanyActivity) {
                $hwActivityCompanyModel              = new HwActivityCompany();
                $hwActivityCompanyModel->activity_id = $activityId;
                $hwActivityCompanyModel->company_id  = $companyId;
                $hwActivityCompanyModel->is_top      = BaseHwActivityCompany::IS_TOP_NO;
                $hwActivityCompanyModel->save();
            }
        }

        $allActivity = array_column($currentActivityAnnouncement, 'activity_id');
        $allActivity = ArrayHelper::merge($allActivity, $newActivityIds);
        $allActivity = array_unique($allActivity);

        // 编辑时重置对应活动排序
        foreach ($allActivity as $activityId) {
            Producer::hwActivityCompanyQueue($activityId);
        }
    }

    /**
     * 同步活动专场状态
     * @param $id
     * @return void
     */
    public static function syncSpecialActivity($id)
    {
        $specialActivityInfo = BaseHwSpecialActivity::findOne($id);
        if (empty($specialActivityInfo->custom_time)) {
            $startDate = $specialActivityInfo->start_date . ' ' . ($specialActivityInfo->start_time ? $specialActivityInfo->start_time : '00:00:00');
            $endDate   = $specialActivityInfo->end_date . ' ' . ($specialActivityInfo->end_time ? $specialActivityInfo->end_time : '23:59:59');

            // 将时间字符串转换为时间戳
            $startTimestamp   = strtotime($startDate);
            $endTimestamp     = strtotime($endDate);
            $currentTimestamp = time();

            // 计算时间差
            $sevenDaysInSeconds = 7 * 24 * 60 * 60;

            // 判断状态
            switch (true) {
                case $currentTimestamp < $startTimestamp - $sevenDaysInSeconds:
                    $status = BaseHwSpecialActivity::STATUS_PENDING; // 待举办
                    break;
                case $currentTimestamp >= $startTimestamp - $sevenDaysInSeconds && $currentTimestamp < $startTimestamp:
                    $status = BaseHwSpecialActivity::STATUS_STARTING_SOON; // 即将开始
                    break;
                case $currentTimestamp >= $startTimestamp && $currentTimestamp <= $endTimestamp:
                    $status = BaseHwSpecialActivity::STATUS_ACTIVE; // 进行中
                    break;
                default:
                    $status = BaseHwSpecialActivity::STATUS_COMPLETED; // 已结束
                    break;
            }

            $specialActivityInfo->status = $status;
            $specialActivityInfo->save();
        }
    }

    /**
     * 获取专场关联的图片
     * @param $id                    int 专场id
     * @param $specialActivityDetail array 专场详情
     * @return string[]
     */
    public static function getSpecialActivityFile(int $id, array $specialActivityDetail = []): array
    {
        if (empty($specialActivityDetail)) {
            $specialActivityDetail = BaseHwSpecialActivity::findOne($id)
                ->toArray();
        }

        // 合并的图片数据
        $fileMergeInfo = [
            'image_pc_banner_url'    => '',
            'image_mini_banner_url'  => '',
            'image_service_code_url' => '',
        ];

        // 小程序和列表，二维码相关图片
        $imageKeyMap = [
            $specialActivityDetail['image_pc_banner_id']    => 'image_pc_banner_id',
            $specialActivityDetail['image_mini_banner_id']  => 'image_mini_banner_id',
            $specialActivityDetail['image_service_code_id'] => 'image_service_code_id',
        ];

        $imageIds = array_filter(array_keys($imageKeyMap));
        if ($imageIds) {
            $fileList = FileHelper::getListNameListByIds(implode(',', $imageIds));
            $fileList = array_column($fileList, 'path', 'id');
            foreach ($fileList as $fileId => $filePath) {
                $fileKey  = $imageKeyMap[$fileId] ?? '';
                $filePath = FileHelper::getFullUrl($filePath);
                switch ($fileKey) {
                    case 'image_pc_banner_id':
                        $fileMergeInfo['image_pc_banner_url'] = $filePath;
                        break;
                    case 'image_mini_banner_id':
                        $fileMergeInfo['image_mini_banner_url'] = $filePath;
                        break;
                    case 'image_service_code_id':
                        $fileMergeInfo['image_service_code_url'] = $filePath;
                        break;
                    default:
                        break;
                }
            }
        }

        return $fileMergeInfo;
    }

    public static function getSpecialActivityDetail($specialActivityId, $resumeId, $specialActivityDetail = [])
    {
        if (empty($specialActivityDetail)) {
            $specialActivityDetail = BaseHwSpecialActivity::findOne($specialActivityId);
            if (empty($specialActivityDetail)) {
                throw new MessageException('异常数据');
            }
            $specialActivityDetail = $specialActivityDetail->toArray();
        }

        // 更新关联活动单位数量
        $specialActivityDetail['realParticipationCompanyAmount'] = BaseHwSpecialActivityRelation::getRealParticipationCompanyAmount($specialActivityId);

        // 日期，如果出现定时器还没执行，手动将数据改为进行中
        // 时间日期文案
        $specialActivityDetail['activityStartCountdown'] = 0;
        if (!empty($specialActivityDetail['custom_time'])) {
            $activityDateText = $specialActivityDetail['custom_time'];
        } else {
            // 处理时间格式
            $activityDateText = self::formatActivityDateText(2, $specialActivityDetail['start_date'],
                $specialActivityDetail['end_date'], $specialActivityDetail['start_time'],
                $specialActivityDetail['end_time']);

            if ($specialActivityDetail['status'] <= BaseHwSpecialActivity::STATUS_STARTING_SOON) {
                $startTime                                       = substr_replace($specialActivityDetail['start_time'],
                        ':', 2, 0) . ':00';
                $formatStartTime                                 = strtotime($specialActivityDetail['start_date'] . ' ' . $startTime);
                $specialActivityDetail['activityStartCountdown'] = time() - $formatStartTime;
                if ($specialActivityDetail['activityStartCountdown'] < 0) {
                    $specialActivityDetail['activityStartCountdown'] = 0;
                    $specialActivityDetail['status']                 = BaseHwSpecialActivity::STATUS_ACTIVE;
                }
            }
        }
        $specialActivityDetail['activityDateText'] = $activityDateText;

        // 设置标签
        $specialActivityDetail['tagList'][] = [
            'type'  => 1,
            'value' => BaseHwSpecialActivity::TO_HOLD_TYPE_TEXT_LIST[$specialActivityDetail['to_hold_type']] ?? '',
        ];
        $tagIds                             = array_filter(explode(',', $specialActivityDetail['tag_ids']));
        foreach ($tagIds as $tagId) {
            $specialActivityDetail['tagList'][] = [
                'type'  => 2,
                'value' => BaseHwSpecialActivity::ACTIVITY_TAG_TEXT_LIST[$tagId] ?? '',
            ];
        }
        $customTag = array_filter(explode(',', $specialActivityDetail['custom_tag']));
        foreach ($customTag as $tag) {
            $specialActivityDetail['tagList'][] = [
                'type'  => 3,
                'value' => $tag,
            ];
        }

        // 配置地点
        if (!empty($specialActivityDetail['custom_address'])) {
            $addressText = $specialActivityDetail['custom_address'];
        } else {
            $addressText = BaseHwSpecialActivity::getAllAddressBySeries($specialActivityId);
            if (!empty($specialActivityDetail['detail_address'])) {
                $addressText = $addressText . '·' . $specialActivityDetail['detail_address'];
            }
        }

        $specialActivityDetail['addressText'] = $addressText;

        // 状态文案
        $specialActivityDetail['statusTxt'] = BaseHwSpecialActivity::STATUS_TEXT_LIST[$specialActivityDetail['status']] ?? '';

        // 报名类型
        $specialActivityDetail['typeText'] = BaseHwActivity::TYPE_TEXT_LIST[$specialActivityDetail['type']] ?? '';

        // 当报名非其他链接时
        $specialActivityDetail['applyStatus'] = 2;
        if ($specialActivityDetail['apply_link_person_type'] != BaseHwSpecialActivity::APPLY_LINK_PERSON_TYPE_OTHER && !empty($resumeId)) {
            $specialActivityDetail['applyStatus'] = BaseActivityFormOptionSign::getResumeActivityApplyStatus($resumeId,
                $specialActivityDetail['apply_link_person_form_id'],
                $specialActivityDetail['apply_link_person_form_option_id']);
        }
        // 获取报名链接
        $applyLinkInfo                          = self::getPersonApplyLinkUrl($specialActivityDetail['id'],
            $specialActivityDetail);
        $specialActivityDetail['applyLink']     = $applyLinkInfo['link'];
        $specialActivityDetail['applyLinkType'] = $applyLinkInfo['linkType'];

        // 富文本调整
        if ($specialActivityDetail['template_id'] == BaseHwSpecialActivity::TEMPLATE_GENERAL) {
            $formatContentMap = [
                'activity_detail',
                'participation_method',
                'retrospection',
            ];
            foreach ($formatContentMap as $formatContent) {
                if (PLATFORM == 'MINI') {
                    $specialActivityDetail[$formatContent] = BaseService::formatContentMiniV2($specialActivityDetail[$formatContent]);
                } else {
                    $specialActivityDetail[$formatContent] = BaseService::formatContentV2($specialActivityDetail[$formatContent]);
                }
            }
        }
        if ($specialActivityDetail['participation_benefit_detail']) {
            if (PLATFORM == 'MINI') {
                $specialActivityDetail['participation_benefit_detail'] = BaseService::formatContentMiniV1($specialActivityDetail['participation_benefit_detail']);
            } else {
                $specialActivityDetail['participation_benefit_detail'] = BaseService::formatContentV1($specialActivityDetail['participation_benefit_detail']);
            }
        }

        $specialActivityDetail['specialLink'] = SpecialActivityService::getSpecialLink($specialActivityDetail['activity_link']);

        // 将基础信息配置到detail
        $fileList = SpecialActivityService::getSpecialActivityFile($specialActivityDetail['id'],
            $specialActivityDetail);

        // 判断单位是否可以报名
        $applyCompanyTime                         = strtotime($specialActivityDetail['apply_company_time'] . ' 23:59:59');
        $specialActivityDetail['companyCanApply'] = 2;
        if ($specialActivityDetail['apply_link_company']) {
            $applyLinkCompanyInfo                          = UrlHelper::formatLinkInfo($specialActivityDetail['apply_link_company']);
            $specialActivityDetail['apply_link_company']   = $applyLinkCompanyInfo['newLink'];
            $specialActivityDetail['applyLinkCompanyType'] = $applyLinkCompanyInfo['linkType'];

            $specialActivityDetail['companyCanApply'] = 1;
            if ($applyCompanyTime < time() && $specialActivityDetail['apply_company_time'] != TimeHelper::ZERO_DATE) {
                $specialActivityDetail['companyCanApply'] = 2;
            }
        }

        // 判断人才是否可以报名
        $applyPersonTime                         = strtotime($specialActivityDetail['apply_person_time'] . ' 23:59:59');
        $specialActivityDetail['personCanApply'] = 1;
        if ($specialActivityDetail['apply_link_person_type'] == 0 || ($specialActivityDetail['apply_person_time'] != TimeHelper::ZERO_DATE && $applyPersonTime < time())) {
            $specialActivityDetail['personCanApply'] = 2;
        }

        return ArrayHelper::intToString(ArrayHelper::merge($fileList, $specialActivityDetail));
    }

    /**
     * @throws MessageException
     */
    public static function getCacheSpecialActivityRelationActivity($params, $resumeId, $dateType = 3)
    {
        $activityList = BaseHwSpecialActivityRelation::getRelationActivityList($params, [
            'a.id as activity_id',
            'sar.activity_short',
            'sar.is_recommend',
            'a.name',
            'a.activity_status',
            'a.activity_child_status',
            // 活动时间及地点
            'asession.custom_time',
            'asession.start_date',
            'asession.end_date',
            'asession.start_time',
            'asession.end_time',
            'asession.custom_address',
            'asession.detail_address',
            // 报名信息
            'a.apply_link_person_type',
            'a.apply_link_person_form_id',
            'a.apply_link_person_form_option_id',
            'a.apply_link_person',
            'a.apply_link_company',
            'a.apply_company_time',
            // 场次序号
            'sar.id as relation_id',
            'a.introduce',
            'a.click',
            'a.main_img_file_id',
            'a.participation_company_amount',
            'a.activity_link',
            'a.series_type',
            'a.wonderful_review',
            'a.to_hold_type',
            'a.apply_person_time',
        ]);

        if (empty($activityList)) {
            throw new MessageException('活动不存在');
        }

        return self::formatSpecialActivityRelationActivityList($activityList, $resumeId, $dateType);
    }

    /**
     * 获取场次安排页面数据
     * @param $specialActivityDetail
     * @param $resumeId
     * @return array
     */
    public static function getSpecialActivityRelationActivity($specialActivityId, $resumeId): array
    {
        $formatActivityList    = self::getCacheSpecialActivityRelationActivity([
            'specialActivityId' => $specialActivityId,
        ], $resumeId, 2);
        $recommendActivityList = self::getSpecialActivityRecommendActivityList($formatActivityList);    // 推荐数据
        $activitySchedule      = self::getSpecialActivitySchedule($formatActivityList); // 时间轴数据

        return [
            'recommendActivityList' => $recommendActivityList,
            'activitySchedule'      => $activitySchedule,
        ];
    }

    /**
     * 整理专场活动描述
     * @param $activityList array
     * @param $resumeId
     * @param $dateType     int 日期类型1:2022.02.11 11:11 2:2022年02月11日11时11分
     * @return array
     */
    private static function formatSpecialActivityRelationActivityList(
        array $activityList,
              $resumeId,
        int   $dateType = 1
    ): array {
        $mainFileList = [];
        if (!empty($activityList)) {
            $fileIds      = array_filter(array_column($activityList, 'main_img_file_id'));
            $mainFileList = FileHelper::getListNameListByIds($fileIds);
            $mainFileList = array_column($mainFileList, 'path', 'id');
        }

        foreach ($activityList as &$activity) {
            //计算活动状态，重置一下状态
            $activityChildStatus               = BaseHwActivity::getZhaoPinHuiActivityChildStatus($activity['start_date'],
                $activity['end_date'], $activity['start_time'], $activity['end_time']);
            $activity['activity_child_status'] = $activityChildStatus;

            $activity['mainImgFileUrl'] = $mainFileList[$activity['main_img_file_id']] ?? '';
            // 状态展示
            $activity['activityChildStatusText'] = BaseHwActivity::ACTIVITY_ZHAOPINHUI_CHILD_TEXT_LIST[$activity['activity_child_status']] ?? '';

            // 地址展示
            if (empty($activity['custom_address'])) {
                $activity['addressText'] = BaseHwActivity::getAllAddressBySeries($activity['activity_id']);
            } else {
                $activity['addressText'] = $activity['custom_address'];
            }

            if (!empty($activity['detail_address'])) {
                $activity['addressText'] .= '·' . $activity['detail_address'];
            }

            $activity['applyStatus'] = 2;  // 参与情况
            if ($activity['apply_link_person_type'] != BaseHwActivity::APPLY_LINK_PERSON_TYPE_OTHER && $resumeId) {
                $activity['applyStatus'] = BaseActivityFormOptionSign::getResumeActivityApplyStatus($resumeId,
                    $activity['apply_link_person_form_id'], $activity['apply_link_person_form_option_id']);
            }

            $applyLinkInfo             = BaseHwActivity::getPersonApplyLinkUrl($activity['activity_id']);
            $activity['applyLink']     = $applyLinkInfo['link'];
            $activity['applyLinkType'] = $applyLinkInfo['linkType'];

            // 获取PC按钮配置
            $activity['applyBtnData'] = self::getActivityBtn($activity['series_type'], $activity['activity_link'],
                $activity['activity_child_status'], boolval($activity['wonderful_review']),
                $activity['applyStatus'] == 1, $activity['applyLink'], $activity['apply_person_time'],
                $activity['apply_link_person_type']);

            if ($activity['apply_link_person_type'] == BaseHwActivity::APPLY_LINK_PERSON_TYPE_NULL || ($activity['apply_person_time'] != TimeHelper::ZERO_DATE || !$activity['apply_person_time']) && strtotime($activity['apply_person_time'] . ' 23:59:59') < time()) {
                if ($activity['applyStatus'] != 1) {
                    $activity['applyStatus'] = 3;  // 不显示按钮
                }
            }

            $activity['companyCanApply'] = 2;
            if ($activity['apply_link_company']) {
                $applyLinkCompanyInfo             = UrlHelper::formatLinkInfo($activity['apply_link_company']);
                $activity['apply_link_company']   = $applyLinkCompanyInfo['newLink'];
                $activity['applyLinkCompanyType'] = $applyLinkCompanyInfo['linkType'];

                $activity['companyCanApply'] = 1;
                if ($activity['apply_company_time'] != TimeHelper::ZERO_DATE || !$activity['apply_company_time']) {
                    $applyCompanyTime = strtotime($activity['apply_company_time'] . ' 23:59:59');
                    if ($applyCompanyTime < time()) {
                        $activity['companyCanApply'] = 2;
                    }
                }
            }

            // 参会单位
            if ($activity['participation_company_amount'] > 5) {
                $activity['activityCompanyList'] = self::getRelationActivityCompanyListByCache($activity['activity_id']);
            }

            // 日期样式调整
            if (!empty($activity['custom_time'])) {
                // 自定义录入，直接显示自定义文本
                $activity['dateText'] = $activity['custom_time'];
            } else {
                // 处理时间格式
                $activity['dateText'] = SpecialActivityService::formatActivityDateText($dateType,
                    $activity['start_date'], $activity['end_date'], $activity['start_time'], $activity['end_time']);

                $startTime = '';
                if ($activity['start_time']) {
                    $startTime = substr_replace($activity['start_time'], ':', 2, 0) . ':00';
                }
                $activity['activityStartTime'] = strtotime($activity['start_date'] . ' ' . $startTime);
            }

            $activity['showClick'] = round($activity['click'] * 26.1);
            if ($activity['showClick'] > 10000) {
                $activity['showClick'] = number_format($activity['showClick'] / 10000, 2) . ' w';
            }

            $activity['activityDetailLink'] = BaseHwActivity::getActivityLinkUrl($activity['series_type'],
                $activity['activity_link']);
        }

        return $activityList;
    }

    /**
     * 获取关联活动单位列表
     * @param $activityId
     * @return array
     */
    private static function getRelationActivityCompanyListByCache($activityId)
    {
        $isCache = \Yii::$app->request->get('isCache');

        $cacheKey    = sprintf(Cache::ACTIVITY_COMPANY_LIST, $activityId);
        $companyList = Cache::get($cacheKey);
        if ($isCache == 2 || !$companyList) {
            $companyList = BaseHwActivityCompany::find()
                ->alias('ac')
                ->innerJoin(['c' => BaseCompany::tableName()], 'c.id=ac.company_id')
                ->innerJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()], 'c.group_score_system_id = cgss.id')
                ->select([
                    'full_name',
                    'ac.company_id',
                ])
                ->orderBy('ac.is_top asc, ac.sort desc, cgss.score desc,c.id desc')
                ->where(['ac.activity_id' => $activityId])
                ->asArray()
                ->all();

            if (!$companyList) {
                return [];
            }

            foreach ($companyList as &$item) {
                $earliestAnnouncement = BaseHwActivityAnnouncement::getEarliestAnnouncementByActivityId($activityId,
                    $item['company_id']);
                $item['companyUrl']   = UrlHelper::createPcCompanyDetailPath($item['company_id']);
                if ($earliestAnnouncement) {
                    $item['companyUrl'] = UrlHelper::createPcAnnouncementDetailPath($earliestAnnouncement['id']);
                }
            }

            Cache::set($cacheKey, json_encode($companyList), 3600);
        } else {
            $companyList = json_decode($companyList, true);
        }

        return $companyList;
    }

    /**
     * 获取专场活动推荐活动
     * @param $activityList
     * @return array
     */
    private static function getSpecialActivityRecommendActivityList($activityList): array
    {
        $recommendActivityList = [];
        foreach ($activityList as &$activity) {
            // 如果是推荐活动
            if ($activity['is_recommend'] == BaseHwSpecialActivityRelation::IS_RECOMMENDED) {
                $recommendActivityList[] = $activity;
            }

            // 日期样式调整
            if (!empty($activity['custom_time'])) {
                // 自定义录入，直接显示自定义文本
                $activity['dateText'] = $activity['custom_time'];
            } else {
                // 处理时间格式
                $activity['dateText'] = SpecialActivityService::formatActivityDateText(2, $activity['start_date'],
                    $activity['end_date'], $activity['start_time'], $activity['end_time']);
            }
        }

        return $recommendActivityList;
    }

    /**
     * 获取专场活动列表时间表
     * @param int $sortType 1:按状态排序；2:按时间排序
     * @param     $activityList
     * @return array
     */
    public static function getSpecialActivitySchedule($activityList, $sortType = 1)
    {
        $containDateActivityList    = [];  // 包含日期
        $notContainDateActivityList = []; // 自定义日期

        foreach ($activityList as &$activity) {
            // 计算活动时间，用于展示排序
            if (empty($activity['custom_time'])) {
                $containDateActivityList[] = $activity;
            } else {
                $notContainDateActivityList[] = $activity;
            }
        }
        // 排序对应数据
        usort($containDateActivityList, function ($a, $b) {
            // 首先比较 activityStartTime，正序
            $timeComparison = $a['activityStartTime'] - $b['activityStartTime'];

            // 如果 activityStartTime 相等，则比较 id，倒序
            if ($timeComparison === 0) {
                return $b['activity_id'] - $a['activity_id'];
            }

            return $timeComparison;
        });

        usort($notContainDateActivityList, function ($a, $b) {
            return $b['activity_id'] - $a['activity_id'];
        });

        // 整合数据开始
        $effectiveActivityList = [];    // 有效活动列表
        $historyActivityList   = [];    // 历史活动列表
        $pendingActivityList   = [];    // 时间待定活动列表
        $nowMonthTime          = strtotime(date('Y-m-01'));
        $existsBeAboutStart    = 0;    // 判断是否需要加入倒计时内容
        foreach ($containDateActivityList as $activity) {
            switch ($sortType) {
                case 1:
                    // 判断一下现在属于操作哪个数组
                    if ($activity['activity_child_status'] == BaseHwActivity::ACTIVITY_CHILD_STATUS_END) {
                        $currentActivityList = $historyActivityList;
                    } else {
                        $currentActivityList = $effectiveActivityList;
                    }
                    break;
                case 2:
                default:
                    $currentActivityList = $effectiveActivityList;
                    break;
            }

            // 日期解析
            $startMonth = date('m', $activity['activityStartTime']);
            $startYear  = date('Y', $activity['activityStartTime']);
            $startDay   = date('d', $activity['activityStartTime']);

            // 判定一下未来还是当前还是现在;1:历史月；2:当前月；3:未来月;4:时间待定
            $currentMonthTime = strtotime($startYear . '-' . $startMonth . '-01');
            $monthStatus      = ($currentMonthTime < $nowMonthTime) ? 1 : (($currentMonthTime == $nowMonthTime) ? 2 : 3);
            if (!isset($currentActivityList[$currentMonthTime])) {
                $currentActivityList[$currentMonthTime] = [
                    'monthStatus'  => $monthStatus,
                    'statusMap'    => [
                        BaseHwActivity::ACTIVITY_CHILD_STATUS_SIGN_UP           => 0,
                        BaseHwActivity::ACTIVITY_CHILD_STATUS_NOT_START         => 0,
                        BaseHwActivity::ACTIVITY_CHILD_STATUS_BE_ABOUT_TO_START => 0,
                        BaseHwActivity::ACTIVITY_CHILD_STATUS_PROGRESS          => 0,
                        BaseHwActivity::ACTIVITY_CHILD_STATUS_END               => 0,
                    ],
                    'year'         => $startYear,
                    'month'        => ltrim($startMonth, '0'),
                    'activityDate' => [],
                ];
            }
            // 配置结构与对应活动数量+1
            $currentDateTime = strtotime($startYear . '-' . $startMonth . '-' . $startDay);
            if (!isset($currentActivityList[$currentMonthTime]['activityDate'][$currentDateTime])) {
                $currentActivityList[$currentMonthTime]['activityDate'][$currentDateTime] = [
                    'month'        => $startMonth,
                    'day'          => $startDay,
                    'activityList' => [],
                ];
            }
            // 未开始，闲时开始倒计时，只显示一个
            if ($activity['activity_child_status'] < BaseHwActivity::ACTIVITY_CHILD_STATUS_PROGRESS && !$existsBeAboutStart) {
                $existsBeAboutStart         = 1;
                $activity['startCountDown'] = $activity['activityStartTime'] - time();
                if ($activity['startCountDown'] < 0) {
                    $activity['startCountDown'] = 0;
                }
            }

            $currentActivityList[$currentMonthTime]['activityDate'][$currentDateTime]['activityList'][] = $activity;
            $currentActivityList[$currentMonthTime]['statusMap'][$activity['activity_child_status']]++;

            // 将数据赋值回去
            switch ($sortType) {
                case 1:
                    if ($activity['activity_child_status'] == BaseHwActivity::ACTIVITY_CHILD_STATUS_END) {
                        $historyActivityList = $currentActivityList;
                    } else {
                        $effectiveActivityList = $currentActivityList;
                    }
                    break;
                case 2:
                default:
                    $effectiveActivityList = $currentActivityList;
                    break;
            }
        }

        $effectiveActivityList = self::formatScheduleActivity($effectiveActivityList);

        $notContainDateActivityListCount = count($notContainDateActivityList);
        if ($notContainDateActivityListCount > 0) {
            $effectiveActivityList[] = [
                'monthStatus'    => 4,
                'activityAmount' => $notContainDateActivityListCount,
                'statusMap'      => [
                    'toBeHeld' => $notContainDateActivityListCount,
                ],
                'activityDate'   => [
                    [
                        'dateText'     => (PLATFORM == 'MINI' ? '待定' : '时间待定'),
                        'activityList' => $notContainDateActivityList,
                    ],
                ],
            ];
        }

        // 如果没有活动，这个将会是空数组
        $data = [
            'effectiveActivityList' => $effectiveActivityList,
        ];

        $historyActivityList = self::formatScheduleActivity($historyActivityList);
        if ($historyActivityList) {
            $data['historyActivityList'] = $historyActivityList;
        }

        return $data;
    }

    /**
     * 整理数据结构
     * @param $activityList
     * @return void
     */
    private static function formatScheduleActivity($scheduleActivityList)
    {
        if (empty($scheduleActivityList)) {
            return [];
        }
        $scheduleActivityList = array_values($scheduleActivityList);
        foreach ($scheduleActivityList as &$scheduleActivity) {
            $activityDate = [];
            foreach ($scheduleActivity['activityDate'] as $item) {
                $item['dateText'] = $item['month'] . '.' . $item['day'];
                $activityDate[]   = $item;
            }
            $scheduleActivity['activityDate'] = $activityDate;

            // 数据结构整理
            $scheduleActivity['activityAmount'] = array_sum($scheduleActivity['statusMap']);
            $scheduleActivity['statusMap']      = [
                'toBeHeld'   => ($scheduleActivity['statusMap'][BaseHwActivity::ACTIVITY_CHILD_STATUS_SIGN_UP] + $scheduleActivity['statusMap'][BaseHwActivity::ACTIVITY_CHILD_STATUS_NOT_START] + $scheduleActivity['statusMap'][BaseHwActivity::ACTIVITY_CHILD_STATUS_BE_ABOUT_TO_START]),
                'inProgress' => $scheduleActivity['statusMap'][BaseHwActivity::ACTIVITY_CHILD_STATUS_PROGRESS],
                'ended'      => $scheduleActivity['statusMap'][BaseHwActivity::ACTIVITY_CHILD_STATUS_END],
            ];
        }

        return $scheduleActivityList;
    }

    /**
     * 获取专场页面-筛选
     * @param $params
     * @param $resumeId
     * @return array
     */
    public static function getActivityCompanySearchParams($params, $resumeId)
    {
        $specialActivityId = $searchSpecialActivityId = intval($params['specialActivityId']);
        $activityId        = $searchActivityId = intval($params['activityId']);

        if (empty($specialActivityId)) {
            throw new MessageException('专场信息必传');
        }

        // 查询条件逻辑
        if ($searchActivityId) {
            $searchSpecialActivityId = 0;
        }
        $searchParams = [
            'cityParams'     => self::getActivityCompanySearchCityParams($searchSpecialActivityId, $searchActivityId),
            'typeParams'     => self::getActivityCompanySearchCompanyTypeParams($searchSpecialActivityId,
                $searchActivityId),
            'majorSelect'    => self::getActivityCompanySearchMajorParams($searchSpecialActivityId, $searchActivityId),
            'jobTypeList'    => BaseCategoryJob::getJobHome(),
            'companyTabList' => self::getActivityCompanySearchActivityTab($specialActivityId),
        ];

        if (empty($searchActivityId)) {
            $cacheKey        = sprintf(Cache::SPECIAL_ACTIVITY_RELATION_ACTIVITY_SORT, $specialActivityId);
            $orderCompanyIds = Cache::get($cacheKey);// 编辑和更新会有记录
            if (empty($orderCompanyIds)) {
                $isShowSearchParams = 2;
            } else {
                $isShowSearchParams = 1;
            }
        } else {
            $existsCompany = BaseHwActivityCompany::find()
                ->where(['activity_id' => $searchActivityId])
                ->exists();
            if ($existsCompany) {
                $isShowSearchParams = 1;
            } else {
                $isShowSearchParams = 2;
            }
        }

        $searchParams['isShowSearchParams'] = $isShowSearchParams;

        // 此处追加当前选择的活动
        if ($activityId) {
            $currentActivityTab = self::getActivityCompanyCurrentActivity($specialActivityId, $activityId, $resumeId);

            if ($currentActivityTab) {
                $searchParams['currentActivityTab']               = $currentActivityTab;
                $searchParams['currentActivityTab']['activityId'] = $currentActivityTab['activity_id'];
                $searchParams['currentActivityTab']['name']       = empty($currentActivityTab['short_name']) ? $currentActivityTab['name'] : $currentActivityTab['short_name'];

                if ($currentActivityTab['startCountDown']) {
                    $searchParams['currentActivityTab']['startCountDown'] = $currentActivityTab['startCountDown'];
                }
            }
        }

        return $searchParams;
    }

    /**
     * 参会单位获取筛选的活动tab
     * @param $specialActivityId
     * @param $activityId
     * @param $resumeId
     * @return array
     */
    public static function getActivityCompanySearchActivityTab($specialActivityId)
    {
        // 活动列表筛选
        $activityList          = self::getCacheSpecialActivityRelationActivity(['specialActivityId' => $specialActivityId],
            0, 3);
        $effectiveActivityList = [];
        $historyActivityList   = [];

        foreach ($activityList as $activity) {
            $activityItem = [
                'specialId'               => $specialActivityId,
                'activityId'              => $activity['activity_id'],
                'dateText'                => $activity['dateText'],
                'activityChildStatusText' => $activity['activityChildStatusText'],
                'activityChildStatus'     => $activity['activity_child_status'],
                'addressText'             => $activity['addressText'],
                'activityName'            => !empty($activity['activity_short']) ? $activity['activity_short'] : $activity['name'],
            ];

            if ($activity['activityStartTime']) {
                $activityItem['activityStartTime'] = $activity['activityStartTime'];
            }

            if ($activity['activity_child_status'] <= BaseHwActivity::ACTIVITY_CHILD_STATUS_PROGRESS) {
                $effectiveActivityList[] = $activityItem;
            } else {
                $historyActivityList[] = $activityItem;
            }
        }

        // 排序有效活动
        $sortedEffectiveActivities = self::sortActivities($effectiveActivityList);

        // 排序历史活动
        $sortedHistoryActivities = self::sortActivities($historyActivityList, true);;
        $searchActivityParams = ArrayHelper::merge($sortedEffectiveActivities, $sortedHistoryActivities);
        array_unshift($searchActivityParams, [
            'specialId'    => $specialActivityId,
            'activityName' => '全部场次',
        ]);

        return $searchActivityParams;
    }

    public static function getActivityCompanyCurrentActivity($specialActivityId, $activityId, $resumeId)
    {
        $activityList = self::getCacheSpecialActivityRelationActivity([
            'specialActivityId' => $specialActivityId,
            'activityId'        => $activityId,
        ], $resumeId, 2);
        if (!$activityList) {
            return [];
        }

        $activity = $activityList[0];

        $activity['name'] = !empty($activity['activity_short']) ? $activity['activity_short'] : $activity['name'];
        if ($activity['activityStartTime'] && $activity['activity_child_status'] < BaseHwActivity::ACTIVITY_CHILD_STATUS_PROGRESS) {
            $activity['startCountDown'] = $activity['activityStartTime'] - time();
            if ($activity['startCountDown'] < 0) {
                $activity['startCountDown'] = 0;
            }
        }

        return $activity;
    }

    private static function sortActivities(array $activities, bool $isHistory = false): array
    {
        // 自定义排序函数
        usort($activities, function ($a, $b) use ($isHistory) {
            if ($isHistory) {
                // 如果状态相同，比较开始时间
                $timeA = $a['activityStartTime'] ?? PHP_INT_MIN; // 如果没有时间，设为最小值
                $timeB = $b['activityStartTime'] ?? PHP_INT_MIN;

                if ($timeA !== $timeB) {
                    return $timeB <=> $timeA;
                }
            } else {
                // 如果状态相同，比较开始时间
                $timeA = $a['activityStartTime'] ?? PHP_INT_MAX; // 如果没有时间，设为最大值
                $timeB = $b['activityStartTime'] ?? PHP_INT_MAX;

                if ($timeA !== $timeB) {
                    return $timeA <=> $timeB;
                }
            }

            // 如果时间相同，根据ID倒序
            return $b['activityId'] <=> $a['activityId'];
        });

        return $activities;
    }

    /**
     * 获取人才报名链接
     */
    public static function getPersonApplyLinkUrl($specialActivityId, $specialActivityInfo = [])
    {
        if (!$specialActivityInfo) {
            $specialActivityInfo = BaseHwSpecialActivity::findOne($specialActivityId)
                ->toArray();
        }
        // 1=表单报名链接 2=其他链接 3=报名表选项ID
        if ($specialActivityInfo['apply_link_person_type'] == BaseHwSpecialActivity::APPLY_LINK_PERSON_TYPE_NULL) {
            $link = '';
        } elseif ($specialActivityInfo['apply_link_person_type'] != BaseHwSpecialActivity::APPLY_LINK_PERSON_TYPE_OTHER) {
            $link = BaseActivityForm::findOne(['id' => $specialActivityInfo['apply_link_person_form_id']])->link;
        } else {
            $link = $specialActivityInfo['apply_link_person'];
        }

        $linkInfo = UrlHelper::formatLinkInfo($link);

        return [
            'link'     => $linkInfo['newLink'],
            'linkType' => $linkInfo['linkType'],
        ];
    }

    /**
     * 获取专场分享二维码
     * @param $specialActivityId
     * @return mixed|string|null
     * @throws \Exception
     */
    public static function getMiniShareLinkUrl($specialActivityId)
    {
        if (!$specialActivityId) {
            return '';
        }

        $key  = Cache::MINI_CODE_KEY . ':' . WxMiniApp::QRCODE_PATH_TYPE_SPECIAL_ACTIVITY . ':' . $specialActivityId;
        $link = Cache::get($key);

        if (!$link) {
            $link = WxMiniApp::getInstance()
                ->createQrCodeByType(WxMiniApp::QRCODE_PATH_TYPE_SPECIAL_ACTIVITY, $specialActivityId);
            if ($link) {
                Cache::set($key, $link);
            }
        }

        return $link;
    }

    /**
     * 获取活动上的按钮展示和状态名称和跳链
     * @param $activityId
     * @param $type
     * @param $childStatus
     * @param $hasWonderfulReview
     * @param $applyStatus
     * @param $applyLink
     * @param $applyDate
     * @return void
     */
    public static function getActivityBtn(
        $seriesType,
        $activityLink,
        $childStatus,
        $hasWonderfulReview = false,
        $applyStatus = false,
        $applyLink = '',
        $applyDate = '',
        $applyLinkPersonType
    ) {
        $statusTextMap = [
            1 => '精彩回顾',
            2 => '已结束',
            3 => '已报名',
            4 => '立即报名',
            5 => '立即查看',
        ];

        $btnIsClick = 1;//1可点击 2不可点击
        $btnLink    = ''; //按钮链接，小程序的不提供
        $btnType    = 2; //2:灰色，1:亮色
        if ($childStatus == BaseHwActivity::ACTIVITY_CHILD_STATUS_END) {
            $btnType = 2;
            // 精彩回顾
            if ($hasWonderfulReview) {
                $status  = 1;
                $btnLink = BaseHwActivity::getActivityLinkUrl($seriesType, $activityLink) . '?showTab=review';
            } else {
                $status     = 2;
                $btnIsClick = 2;
            }
        } else {
            if ($applyStatus) {
                $status     = 3;
                $btnType    = 2;
                $btnIsClick = 2;
            } else {
                if ($applyLinkPersonType == 0) {
                    $status  = 4;
                    $btnType = 1;
                    // 跳活动详情页
                    $btnLink = BaseHwActivity::getActivityLinkUrl($seriesType, $activityLink);
                } else {
                    if ($applyDate == TimeHelper::ZERO_DATE || !$applyDate) {
                        $status  = 4;
                        $btnType = 1;
                        $btnLink = $applyLink;
                    } else {
                        $applyDateTime = strtotime($applyDate . ' 23:59:59');
                        if (time() <= $applyDateTime) {
                            $status  = 4;
                            $btnType = 1;
                            $btnLink = $applyLink;
                        } else {
                            $status  = 5;
                            $btnType = 1;
                            // 跳活动详情页
                            $btnLink = BaseHwActivity::getActivityLinkUrl($seriesType, $activityLink);
                        }
                    }
                }
            }
        }

        return [
            'btnText'    => $statusTextMap[$status],
            'btnLink'    => $btnLink,
            'btnIsClick' => $btnIsClick,
            'btnType'    => $btnType,
        ];
    }
}