<?php

namespace common\service\companyResume;

//保存单位对人才简历的标签
use common\base\models\BaseCompanyResumeLibrary;
use yii\base\Exception;

class SaveTagService extends BaseService
{
    private $tag;

    public function run($companyId, $resumeId, $tag)
    {
        $this->companyId = $companyId;
        $this->resumeId  = $resumeId;
        $this->tag       = $tag;

        $this->setData();
        $this->check();
        $this->save();
    }

    private function setData()
    {
        $this->setCompany($this->companyId);
        $this->setResume($this->resumeId);
    }

    private function check()
    {
        // 检查简历是否存在库中
        if (!BaseCompanyResumeLibrary::find()
            ->where(['resume_id' => $this->resumeId])
            ->exists()) {
            throw new Exception('简历不存在单位人才库');
        }
    }

    /**
     * 更新标签
     * @return void
     * @throws Exception
     */
    private function save()
    {
        $model = BaseCompanyResumeLibrary::findOne([
            'resume_id'  => $this->resumeId,
            'company_id' => $this->companyId,
        ]);

        $model->tag = $this->tag;
        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
    }
}