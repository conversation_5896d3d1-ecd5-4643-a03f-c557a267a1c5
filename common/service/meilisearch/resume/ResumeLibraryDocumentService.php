<?php
namespace common\service\meilisearch\resume;

/**
 * create user：shannon
 * create time：2024/5/10 15:02
 */

use common\base\models\BaseResumeAcademicBook;
use common\base\models\BaseResumeAcademicPage;
use common\base\models\BaseResumeAcademicPatent;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeResearchDirection;
use common\base\models\BaseResumeResearchProject;
use common\base\models\BaseResumeWork;
use common\helpers\DebugHelper;
use common\service\meilisearch\BaseService;
use common\base\models\BaseResume;
use zhuzixian520\meilisearch\Exception;

/**
 * 人才库搜索服务
 */
class ResumeLibraryDocumentService extends BaseService
{
    //limit限制
    const LIMIT = 1000;

    /**
     * 初始化搜索参数
     * SearchService constructor.
     */
    public function __construct($params = [])
    {
        self::$index = $this->keyInfo['resumeLibraryKey'];
        $params      = [
            'matchingStrategy' => 'all',
            'limit'            => $params['limit'] ?? self::LIMIT,
        ];

        $this->setSearchParams($params);

        parent::__construct();
    }

    /**
     * 搜索执行
     *
     */
    public function run(): array
    {
        try {
            $res = self::getInstance()
                ->search($this->searchParams);
            if ($res['estimatedTotalHits'] > 0) {
                $resume_ids = array_column($res['hits'], 'resumeId');
            } else {
                $resume_ids = [];
            }
            unset($res['hits']);

            //搜索成功后 输出一个日志

            return $resume_ids;
        } catch (Exception $e) {
            //输出日志

            //就算错误也要告知页面层查询时空的
            return [];
        }
    }

    public function originalSearch($params = [])
    {
        $page     = $params['page'] ?? 1;
        $pageSize = 50;
        $offset   = ($page - 1) * $pageSize;
        $limit    = $pageSize;
        $this->limit($limit);
        $this->offset($offset);
        $this->setAttributesToHighlight();

        $res = self::getInstance()
            ->search($this->searchParams);

        if ($res['hits']) {
            // 找到hits里面的_formatted返回
            $return = array_column($res['hits'], '_formatted');
        } else {
            $return = [];
        }

        return $return;
    }

    /**
     * 添加或者更新文档
     * @return void
     */
    public function addOrUpdateDocument($resumeId)
    {
        $documentItem = [
            'resumeId'                 => $resumeId,
            'educationSchool'          => '',
            'researchDirectionContent' => '',
            'researchProjectContent'   => '',
            'academicPaperContent'     => '',
            'academicPatentContent'    => '',
            'academicBookContent'      => '',
        ];
        if ($resumeId <= 0) {
            return;
        }
        //获取简历信息
        $resume = BaseResume::findOne($resumeId);
        if (empty($resume)) {
            return;
        }
        $documentItem['resumeId'] = $resume->id;
        //毕业院校
        $educationSchoolArray            = BaseResumeEducation::find()
            ->select([
                'school',
            ])
            ->andWhere([
                'resume_id' => $resumeId,
                'status'    => BaseResumeEducation::STATUS_ACTIVE,
            ])
            ->column();
        $documentItem['educationSchool'] = implode('|', $educationSchoolArray);
        //研究方向
        $resumeResearchDirectionContent           = BaseResumeResearchDirection::find()
            ->select([
                'content',
            ])
            ->andWhere([
                'resume_id' => $resumeId,
                'status'    => BaseResumeResearchDirection::STATUS_ACTIVE,
            ])
            ->scalar();
        $documentItem['researchDirectionContent'] = $resumeResearchDirectionContent ?: '';
        //工作内容
        $workContentArray = BaseResumeWork::find()
            ->select([
                'content',
            ])
            ->andWhere([
                'resume_id' => $resumeId,
                'status'    => BaseResumeWork::STATUS_ACTIVE,
            ])
            ->column();
        if ($workContentArray) {
            $documentItem['workContent'] = implode('|', $workContentArray);
        }
        //学术成果
        //学术成果-论文
        $resumeAcademicPaperArray = BaseResumeAcademicPage::find()
            ->select([
                "CONCAT(title,description) as content",
            ])
            ->andWhere([
                'resume_id' => $resumeId,
                'status'    => BaseResumeAcademicPage::STATUS_ACTIVE,
            ])
            ->column();
        if ($resumeAcademicPaperArray) {
            $documentItem['academicPaperContent'] = implode('|', $resumeAcademicPaperArray);
        }
        //学术成果-专利
        $resumeAcademicPatentArray = BaseResumeAcademicPatent::find()
            ->select([
                "CONCAT(name,description) as content",
            ])
            ->andWhere([
                'resume_id' => $resumeId,
                'status'    => BaseResumeAcademicPatent::STATUS_ACTIVE,
            ])
            ->column();
        if ($resumeAcademicPatentArray) {
            $documentItem['academicPatentContent'] = implode('|', $resumeAcademicPatentArray);
        }
        //学术成果-专著
        $resumeAcademicBookArray = BaseResumeAcademicBook::find()
            ->select([
                "name",
            ])
            ->andWhere([
                'resume_id' => $resumeId,
                'status'    => BaseResumeAcademicBook::STATUS_ACTIVE,
            ])
            ->column();
        if ($resumeAcademicBookArray) {
            $documentItem['academicBookContent'] = implode('|', $resumeAcademicBookArray);
        }
        //项目经历
        $resumeResearchProjectArray = BaseResumeResearchProject::find()
            ->select([
                "CONCAT(name,description) as content",
            ])
            ->andWhere([
                'resume_id' => $resumeId,
                'status'    => BaseResumeResearchProject::STATUS_ACTIVE,
            ])
            ->column();
        if ($resumeResearchProjectArray) {
            $documentItem['researchProjectContent'] = implode('|', $resumeResearchProjectArray);
        }

        //添加或者更新到meilisearch
        self::getInstance()
            ->updateDocuments($documentItem, 'resumeId');
    }
}