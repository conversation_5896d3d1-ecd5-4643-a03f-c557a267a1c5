<?php

namespace common\service\meilisearch\announcement;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArticle;
use common\base\models\BaseCompany;
use common\base\models\BaseJob;
use common\helpers\DebugHelper;
use common\service\meilisearch\BaseService;
use phpDocumentor\Reflection\Types\Parent_;
use SebastianBergmann\CodeCoverage\Report\PHP;
use Yii;

class AddService extends BaseService
{
    /**
     * @var BaseAnnouncement
     */
    private $announcementInfo;
    private $announcementId;
    private $addInfo;

    public function __construct()
    {
        self::$index = $this->keyInfo['announcementKey'];
        parent::__construct();
    }

    /**
     * 通过职位id添加、编辑meilisearch职位
     * @param $id
     * @return false|void
     */
    public function saveById($id)
    {
        $this->announcementId = $id;
        //获取职位信息
        $this->announcementInfo = BaseAnnouncement::findOne($this->announcementId);
        if (empty($this->announcementInfo)) {
            //退出
            return false;
        }
        $this->setInfo();
        $this->updateData();
    }

    /**
     * 设置添加数组
     * @return void
     */
    protected function setInfo()
    {
        //暂时需要announcementId、jobName、companyName、announcementName个参数
        $this->addInfo['announcementId'] = $this->announcementInfo->id;
        $this->addInfo['title']          = $this->announcementInfo->title;
        $articleId                       = $this->announcementInfo->article_id;
        $content                         = BaseArticle::findOneVal(['id' => $articleId], 'content');
        // 本来内容是富文本的，现在只要文字
        $this->addInfo['content'] = strip_tags($content);

        // bb($this->addInfo);
        // $this->addInfo['companyName']      = BaseCompany::findOneVal(['id' => $this->announcementInfo->company_id],
        //     'full_name');
        // $this->addInfo['announcementName'] = BaseAnnouncement::findOneVal(['id' => $this->announcementInfo->announcement_id],
        //     'title');
    }

    /**
     * 处理数据
     * - 删除
     * - 添加
     * - 更新
     */
    protected function updateData()
    {
        //判断职位的状态确认执行的操作
        //获取一下职位的文档信息
        $getIndexInfo = self::getInstance()
            ->getDocument($this->announcementId);
        if ($getIndexInfo && in_array($this->announcementInfo->status, [
                BaseJob::STATUS_ONLINE,
                BaseJob::STATUS_OFFLINE,
            ])) {
            $this->update();
        } elseif (!$getIndexInfo && in_array($this->announcementInfo->status, [
                BaseJob::STATUS_ONLINE,
                BaseJob::STATUS_OFFLINE,
            ])) {
            $this->add();
        } else {
            $this->delete();
        }
    }

    /**
     * 添加
     * @return void
     */
    protected function add()
    {
        $res = self::getInstance()
            ->addDocuments($this->addInfo);
    }

    /**
     * 更新
     * @return void
     */
    protected function update()
    {
        $res = self::getInstance()
            ->updateDocuments($this->addInfo, 'announcementId');
    }

    /**
     * 删除
     * @return void
     */
    protected function delete()
    {
        $res = self::getInstance()
            ->deleteDocument($this->announcementId);
    }
}