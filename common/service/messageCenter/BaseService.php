<?php

namespace common\service\messageCenter;

use common\base\models\BaseMemberMessage;
use common\base\models\BaseResumeWxBind;
use common\helpers\DebugHelper;
use common\libs\Cache;
use common\libs\EmailQueue;
use common\libs\JwtAuth;
use common\libs\WxPublic;
use common\service\resumeRemind\ActionService;
use common\service\resumeRemind\ResumeRemindApplication;
use queue\Producer;

class BaseService
{
    const TYPE_CONFIG = [
        // 单位新增关注
        self::COMPANY_NEW_FOLLOW_KEY             => [
            'channel'           => [self::TYPE_CHANNEL_ON_SITE],
            'onSiteMessageType' => BaseMemberMessage::TYPE_COMPANY_SYSTEM,
            'emailType'         => '',
            'smsType'           => '',
            'title'             => '【关注提醒】有新的求职者关注了您',
            'innerLink'         => BaseMemberMessage::LINK_TYPE_RESUME_LIBRARY,
        ],
        //单位邀约投递
        self::JOB_INVITATION_KEY                 => [
            'channel'           => [
                self::TYPE_CHANNEL_WECHAT,
                self::TYPE_CHANNEL_EMAIL,
                self::TYPE_CHANNEL_ON_SITE,
                self::TYPE_REMIND,
                self::TYPE_CHANNEL_SMS,
            ],
            'onSiteMessageType' => BaseMemberMessage::TYPE_RESUME_JOB_APPLY_INVITE,
            'emailType'         => EmailQueue::EMAIL_RESUME_INVITE_JOB_APPLY,
            'resumeRemindType'  => ActionService::JOB_INVITE_TYPE,
            'smsType'           => '',
            'title'             => '【高校人才网】职位投递邀请',
            'innerLink'         => BaseMemberMessage::LINK_TYPE_JOB_DETAIL,
            'wxRemark'          => '简历越完整，被查看的几率越高，更容易受到用人单位的青睐哦～',
        ],
        //职位处理--不合适
        self::JOB_INAPPROPRIATE_KEY              => [
            'channel'           => [
                self::TYPE_CHANNEL_WECHAT,
                self::TYPE_CHANNEL_EMAIL,
                self::TYPE_REMIND,
            ],
            'onSiteMessageType' => BaseMemberMessage::TYPE_RESUME_APPLY,
            'emailType'         => EmailQueue::EMAIL_TYPE_RESUME_BACK,
            'resumeRemindType'  => ActionService::JOB_APPLY_NO_PASS_TYPE,
            'smsType'           => '',
            'title'             => '【高校人才网】投递反馈-简历不合适',
            'innerLink'         => BaseMemberMessage::LINK_TYPE_JOB_DETAIL,
            'wxRemark'          => '高校人才网已更新了更多优质职位，可点击查看。',
        ],
        //简历被查看(这里只作为首次查看?)
        self::JOB_APPLY_CHECK_KEY                => [
            'channel'           => [
                self::TYPE_CHANNEL_ON_SITE,
                self::TYPE_CHANNEL_WECHAT,
                self::TYPE_REMIND,
            ],
            'onSiteMessageType' => BaseMemberMessage::TYPE_RESUME_APPLY,
            'resumeRemindType'  => ActionService::JOB_APPLY_CHECK_TYPE,
            'emailType'         => '',
            'smsType'           => '',
            'title'             => '【高校人才网】简历被查看',
            'innerLink'         => BaseMemberMessage::LINK_TYPE_JOB_DETAIL,
            'wxRemark'          => '感谢您的关注，请继续留意我们的信息。',
        ],
        //职位处理--通过筛选
        self::JOB_APPLY_APPROVE                  => [
            'channel'           => [
                self::TYPE_CHANNEL_ON_SITE,
                self::TYPE_CHANNEL_EMAIL,
                self::TYPE_CHANNEL_WECHAT,
                self::TYPE_REMIND,
            ],
            'onSiteMessageType' => BaseMemberMessage::TYPE_RESUME_APPLY,
            'resumeRemindType'  => ActionService::JOB_APPLY_PASS_TYPE,
            'emailType'         => EmailQueue::EMAIL_TYPE_RESUME_FIRST_PASS,
            'smsType'           => '',
            'title'             => '【高校人才网】投递反馈-简历通过初筛',
            'innerLink'         => BaseMemberMessage::LINK_TYPE_JOB_DETAIL,
            'wxRemark'          => '感谢您的关注，请继续留意我们的信息。',
        ],
        //职位投递简历面试
        self::JOB_APPLY_INVITATION               => [
            'channel'           => [
                self::TYPE_CHANNEL_ON_SITE,
                self::TYPE_CHANNEL_EMAIL,
                self::TYPE_CHANNEL_WECHAT,
                self::TYPE_REMIND,
            ],
            'onSiteMessageType' => BaseMemberMessage::TYPE_RESUME_APPLY,
            'resumeRemindType'  => ActionService::JOB_APPLY_INVITE_TYPE,
            'emailType'         => EmailQueue::EMAIL_TYPE_RESUME_INTERVIEW,
            'smsType'           => '',
            'title'             => '【高校人才网】面试通知',
            'innerLink'         => BaseMemberMessage::LINK_TYPE_JOB_DETAIL,
            'wxRemark'          => '上高校人才网，入职高校快人一步。',
        ],
        //职位投递已录用
        self::JOB_APPLY_EMPLOYED                 => [
            'channel'          => [
                self::TYPE_REMIND,
            ],
            'resumeRemindType' => ActionService::JOB_APPLY_EMPLOYED_TYPE,
        ],
        self::JOB_APPLY_INAPPROPRIATE_REVOCATION => [
            'channel'          => [
                self::TYPE_REMIND,
            ],
            'resumeRemindType' => ActionService::JOB_APPLY_INAPPROPRIATE_REVOCATION_TYPE,
        ],
        self::JOB_APPLY_EMPLOYED_REVOCATION      => [
            'channel'          => [
                self::TYPE_REMIND,
            ],
            'resumeRemindType' => ActionService::JOB_APPLY_EMPLOYED_REVOCATION_TYPE,
        ],
        //登录通知
        self::WX_SIGN_IN                         => [
            'channel'           => [//                self::TYPE_CHANNEL_WECHAT
            ],
            'onSiteMessageType' => BaseMemberMessage::TYPE_COMPANY_SYSTEM,
            'emailType'         => '',
            'smsType'           => '',
            'title'             => '【高校人才网】微信扫码登录',
            'innerLink'         => '',
            'wxRemark'          => '上高校人才网，入职高校快人一步。',
        ],
        //单位登录通知
        self::WX_SIGN_IN_COMPANY                 => [
            'channel'           => [self::TYPE_CHANNEL_WECHAT],
            'onSiteMessageType' => BaseMemberMessage::TYPE_COMPANY_SYSTEM,
            'emailType'         => '',
            'smsType'           => '',
            'title'             => '【高校人才网】登录成功提醒',
            'innerLink'         => '',
            'wxRemark'          => '上高校人才网，入职高校快人一步。',
        ],
        // 单位查看简历
        self::COMPANY_VIEW_RESUME                => [
            'channel'           => [
                self::TYPE_REMIND,
                self::TYPE_CHANNEL_ON_SITE,
            ],
            'onSiteMessageType' => BaseMemberMessage::TYPE_RESUME_CHECK,
            'resumeRemindType'  => ActionService::COMPANY_VIEW_RESUME_TYPE,
            'emailType'         => '',
            'smsType'           => '',
            'title'             => '【高校人才网】单位查看简历',
            'innerLink'         => '',
        ],
        // 单位查看简历
        self::RESUME_VIEW_COMPANY                => [
            'channel'          => [
                self::TYPE_REMIND,
            ],
            'resumeRemindType' => ActionService::RESUME_VIEW_COMPANY_TYPE,
        ],
        // 单位查看简历
        self::RESUME_VIEW_COMPANY_INVITE         => [
            'channel'          => [
                self::TYPE_REMIND,
            ],
            'resumeRemindType' => ActionService::RESUME_VIEW_COMPANY_INVITE_TYPE,
        ],
        self::RESUME_VIEW_ADMIN_INVITE           => [
            'channel'          => [
                self::TYPE_REMIND,
            ],
            'resumeRemindType' => ActionService::RESUME_VIEW_ADMIN_INVITE_TYPE,
        ],
        // 管理员邀约求职者
        self::ADMIN_JOB_INVITATION_KEY           => [
            'channel'          => [
                self::TYPE_REMIND,
            ],
            'resumeRemindType' => ActionService::ADMIN_JOB_INVITE_TYPE,
        ],
        self::JOB_SUBSCRIBE_SEND_KEY             => [
            'channel'   => [
                self::TYPE_CHANNEL_EMAIL,
                self::TYPE_CHANNEL_WECHAT,
            ],
            'emailType' => EmailQueue::EMAIL_JOB_SUBSCRIBE,

        ],
        //单位接收投递消息通知
        self::SIDE_DELIVERY_NOTICE_COMPANY       => [
            'channel'           => [self::TYPE_CHANNEL_WECHAT],
            'onSiteMessageType' => BaseMemberMessage::TYPE_COMPANY_SYSTEM,
            'emailType'         => '',
            'smsType'           => '',
            'title'             => '【高校人才网】收到投递简历通知',
            'innerLink'         => '',
            'wxRemark'          => '接收到新的投递简历请尽快处理。',
        ],
        //新招呼通知提醒
        self::NEW_CHAT_CALL_REMIND               => [
            'channel'           => [self::TYPE_CHANNEL_WECHAT],
            'onSiteMessageType' => BaseMemberMessage::TYPE_COMPANY_SYSTEM,
            'emailType'         => '',
            'smsType'           => '',
            'title'             => '新招呼通知',
            'innerLink'         => '',
            'wxRemark'          => '详情请登录高校人才网-单位管理后台查看',
        ],
        //咨询回复消息提醒
        self::NEW_CHAT_CONSULT_REMIND            => [
            'channel'           => [self::TYPE_CHANNEL_WECHAT],
            'onSiteMessageType' => BaseMemberMessage::TYPE_COMPANY_SYSTEM,
            'emailType'         => '',
            'smsType'           => '',
            'title'             => '新招呼通知',
            'innerLink'         => '',
            'wxRemark'          => '详情请登录高校人才网-单位管理后台查看',
        ],
        self::CHAT_WAY_ONE_COMMUNICATION         => [
            'channel'           => [
                self::TYPE_CHANNEL_WECHAT,
            ],
            'onSiteMessageType' => BaseMemberMessage::TYPE_COMPANY_SYSTEM,
            'emailType'         => '',
            'smsType'           => '',
            'title'             => '新招呼通知提醒',
            'innerLink'         => '',
            'wxRemark'          => '单位向您发起了聊天，有消息待您查收！',
        ],
        self::CHAT_WAY_ONE_COMMUNICATION_COMPANY => [
            'channel'           => [//                self::TYPE_CHANNEL_WECHAT,
            ],
            'onSiteMessageType' => BaseMemberMessage::TYPE_COMPANY_SYSTEM,
            'emailType'         => '',
            'smsType'           => '',
            'title'             => '新招呼通知提醒',
            'innerLink'         => '',
            'wxRemark'          => '求职者向您发起了新招呼',
        ],
        self::CHAT_WAY_TWO_COMMUNICATION         => [
            'channel'           => [
                self::TYPE_CHANNEL_WECHAT,
            ],
            'onSiteMessageType' => BaseMemberMessage::TYPE_COMPANY_SYSTEM,
            'emailType'         => '',
            'smsType'           => '',
            'title'             => '咨询回复消息提醒',
            'innerLink'         => '',
            'wxRemark'          => '单位回复了您的咨询消息，请尽快查看哦！',
        ],
        self::CHAT_WAY_TWO_COMMUNICATION_COMPANY => [
            'channel'           => [//                self::TYPE_CHANNEL_WECHAT,
            ],
            'onSiteMessageType' => BaseMemberMessage::TYPE_COMPANY_SYSTEM,
            'emailType'         => '',
            'smsType'           => '',
            'title'             => '咨询回复消息提醒',
            'innerLink'         => '',
            'wxRemark'          => '求职者回复了您的咨询消息，请尽快查看哦！',
        ],
    ];

    const COMPANY_NEW_FOLLOW_KEY             = 'companyNewFollow';  //单位新增关注
    const JOB_INVITATION_KEY                 = 'jobInvitation'; //职位邀约
    const JOB_INAPPROPRIATE_KEY              = 'jobAppropriate'; //职位处理--不合适
    const JOB_APPLY_CHECK_KEY                = 'jobApplyCheck'; //简历查看
    const JOB_APPLY_APPROVE                  = 'jobApplyApprove'; //简历处理-通过初筛
    const JOB_APPLY_INVITATION               = 'jobApplyInvitation'; //简历处理-面试邀约
    const JOB_APPLY_EMPLOYED                 = 'jobApplyEmployed'; //简历处理-已录用
    const JOB_APPLY_INAPPROPRIATE_REVOCATION = 'jobApplyInappropriateRevocation'; //简历处理-不合适撤销
    const JOB_APPLY_EMPLOYED_REVOCATION      = 'jobApplyEmployedRevocation'; //简历处理-已录用撤销
    const WX_SIGN_IN                         = 'wxSignIn';//微信扫码登录成功
    const WX_SIGN_IN_COMPANY                 = 'wxSignInCompany';//单位微信扫码登录成功
    const COMPANY_VIEW_RESUME                = 'companyViewResume'; //单位查看简历
    const RESUME_VIEW_COMPANY                = 'resumeViewCompany'; //简历查看单位
    const RESUME_VIEW_COMPANY_INVITE         = 'resumeViewCompanyInvite'; //简历查看单位邀约
    const RESUME_VIEW_ADMIN_INVITE           = 'resumeViewAdminInvite'; //简历查看管理员邀约
    const ADMIN_JOB_INVITATION_KEY           = 'adminJobInvitation'; //运营后台邀约
    const JOB_SUBSCRIBE_SEND_KEY             = 'jobSubscribeSend'; //职位订阅推送
    const SIDE_DELIVERY_NOTICE_COMPANY       = 'sideDeliveryNoticeCompany'; //简历投递通知单位
    const NEW_CHAT_CALL_REMIND               = 'newChatCallRemind'; //新招呼通知提醒
    const NEW_CHAT_CONSULT_REMIND            = 'newChatConsultRemind'; //咨询回复消息提醒
    const CHAT_WAY_ONE_COMMUNICATION         = 'chatWayOne'; //直聊单向沟通
    const CHAT_WAY_ONE_COMMUNICATION_COMPANY = 'chatWayOneCompany'; //单位直聊单向沟通
    const CHAT_WAY_TWO_COMMUNICATION         = 'chatWayTwo'; //双向沟通
    const CHAT_WAY_TWO_COMMUNICATION_COMPANY = 'chatWayTwoCompany'; //单位直聊双向沟通

    //求职者实现时效内自动登录的服务号推送
    const RESUME_AUTO_LOGIN_KEY = [
        self::JOB_SUBSCRIBE_SEND_KEY,
        self::JOB_APPLY_CHECK_KEY,
        self::JOB_APPLY_APPROVE,
        self::JOB_APPLY_INVITATION,
        self::JOB_INAPPROPRIATE_KEY,
        self::JOB_INVITATION_KEY,
    ];

    // 站内信
    const TYPE_CHANNEL_ON_SITE = 1;
    // 邮箱
    const TYPE_CHANNEL_EMAIL = 2;
    // 短信
    const TYPE_CHANNEL_SMS = 3;
    // 微信通知
    const TYPE_CHANNEL_WECHAT = 4;
    // 强提醒
    const TYPE_REMIND = 5;

    public $key;

    protected $config    = [];
    protected $emailData = [];
    protected $wxData    = [];
    protected $onSiteMemberId;

    protected $channel;
    protected $onSiteMessageType;

    protected $params;

    protected $title;
    protected $content         = '';
    protected $innerLink       = '';
    protected $innerLinkParams = [];

    protected $email;
    protected $emailContent;
    protected $wxOpenId;
    protected $mobile;
    protected $mobileCode = '';
    protected $userType   = 1; // 1:个人，2：单位
    protected $emailType;
    protected $emailId;
    protected $bind       = 0; //1:已绑定，0：未绑定

    // 强提醒需要传递的一些参数
    protected $remindData = [];

    // 默认公众号类型,求职者
    public $wxPublicType = 1;

    protected $smsId = 0;

    protected $smsType;

    protected $smsExtra;

    protected function setConfig()
    {
        // 先读取配置
        $this->config = self::TYPE_CONFIG[$this->key];

        // 读取渠道
        if ($this->config['channel']) {
            $this->channel = $this->config['channel'];
        }

        // 读取各个渠道的消息类型
        if ($this->config['onSiteMessageType']) {
            $this->onSiteMessageType = $this->config['onSiteMessageType'];
        }

        // 读取标题
        if ($this->config['title']) {
            $this->title = $this->config['title'];
        }

        // 读取内链
        if ($this->config['innerLink']) {
            $this->innerLink = $this->config['innerLink'];
        }

        // 邮件类型
        if ($this->config['emailType']) {
            $this->emailType = $this->config['emailType'];
        }
    }

    protected function product()
    {
        foreach ($this->channel as $channel) {
            switch ($channel) {
                case self::TYPE_CHANNEL_ON_SITE:
                    $this->sendOnSite();
                    break;
                case self::TYPE_CHANNEL_EMAIL:
                    $this->emailId = $this->sendEmail();
                    break;
                case self::TYPE_CHANNEL_SMS:
                    $this->smsId = $this->sendSms();
                    break;
                case self::TYPE_CHANNEL_WECHAT:
                    $this->sendWx();
                    break;
                case self::TYPE_REMIND:
                    $this->sendRemind();
                    break;
            }
        }
    }

    private function sendOnSite()
    {
        if ($this->onSiteMemberId) {
            BaseMemberMessage::send($this->onSiteMemberId, $this->onSiteMessageType, $this->title, $this->content,
                $this->innerLink, $this->innerLinkParams);
        }
    }

    private function sendEmail()
    {
        if ($this->email) {
            return Producer::email($this->email, $this->userType, $this->emailType, $this->emailContent,
                $this->emailData);
        }
    }

    private function sendSms()
    {
        if ($this->mobile) {
            return Producer::sms($this->mobile, $this->userType, $this->smsType, $this->mobileCode, $this->smsExtra);
        }
    }

    public function sendWx()
    {
        // 有些环境可能是没有配置的,所以这里catch一下
        try {
            $openId = $this->wxData['openid'];
            // 如果没有openid就不用了
            if (!$openId) {
                return false;
            }

            if ($this->wxPublicType == 1) {
                if (!\Yii::$app->params['wx']['personPublic']) {
                    return true;
                }
            }

            if ($this->wxPublicType == 2) {
                if (!\Yii::$app->params['wx']['companyPublic']) {
                    return true;
                }
            }

            $wx = WxPublic::getInstance($this->wxPublicType);

            $templateId   = $this->wxData['templateId'];
            $url          = $this->wxData['url'] ?: '';
            $miniPagePath = (isset($this->wxData['miniPagePath']) && $this->wxData['miniPagePath']) ? $this->wxData['miniPagePath'] : '';
            //这里处理一下url
            if (in_array($this->key, self::RESUME_AUTO_LOGIN_KEY)) {
                //在后面加多一个token参数  authorization
                $jwt           = new JwtAuth();
                $jwt->tokenTtl = 2592000;//30天
                $jwt->createToken($this->remindData['resumeId']);
                // $url里面没有?就加上,有就加&
                if (strpos($url, '?') === false) {
                    $url .= '?wechatAuthorization=' . $jwt->token;
                } else {
                    $url .= '&wechatAuthorization=' . $jwt->token;
                }
                $url .= '&autoAuth=' . \Yii::$app->params['autoAuthKey'];
                Cache::set($jwt->token, 1, ($jwt->tokenTtl - 1000));
            }
            unset($this->wxData['openid'], $this->wxData['templateId'], $this->wxData['url'], $this->wxData['miniPagePath']);
            $res = $wx->sendTemplateMessage($openId, $templateId, $this->wxData, $url, $miniPagePath);

            if ($res['errmsg'] != 'ok') {
                DebugHelper::wxMessageLog($res);
                DebugHelper::wxMessageLog($this->wxData);
            }
        } catch (\Exception $e) {
        }
    }

    public function setBind($resumeId)
    {
        $this->bind = BaseResumeWxBind::checkWxBind($resumeId);
    }

    private function sendRemind()
    {
        if ($resumeType = $this->config['resumeRemindType']) {
            // 发给求职者
            $app = ResumeRemindApplication::getInstance();
            $app->create($resumeType, $this->remindData);
        }
    }
}
