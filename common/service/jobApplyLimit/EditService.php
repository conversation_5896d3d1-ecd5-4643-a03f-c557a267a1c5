<?php

namespace common\service\jobApplyLimit;

use common\base\models\BaseCompany;
use common\base\models\BaseJobApplyLimitCompany;
use common\base\models\BaseJobApplyLimitCompanyTag;
use common\base\models\BaseJobApplyLimitConfig;
use common\base\models\BaseJobApplyLimitResume;
use common\base\models\BaseJobApplyLimitResumeTag;
use common\base\models\BaseResume;
use common\base\models\BaseResumeTag;
use common\components\MessageException;
use common\helpers\StringHelper;

// 对外提供一些编辑服务
class EditService extends BaseService
{

    public function add($params, $adminId = 0)
    {
        $type        = $params['type'];
        $resumeType  = $params['resumeType'];
        $resumeData  = $params['resumeData'];
        $companyType = $params['companyType'];
        $companyData = $params['companyData'];
        $messageType = $params['messageType'];
        $remark      = $params['remark'];
        $id          = $params['id'];

        if (empty($type)) {
            throw new MessageException('请选择限制类型');
        }

        // 禁止投递（人才类型和数据必填）
        if (empty($resumeType)) {
            throw new MessageException('请选择人才类型');
        }

        if (empty($resumeData)) {
            throw new MessageException('请录入人才数据');
        }

        if (empty($remark)) {
            throw new MessageException('请录入备注');
        }

        if ($type == BaseJobApplyLimitConfig::TYPE_BAN) {
            if (empty($companyType)) {
                throw new MessageException('请选择禁投单位类型');
            }
            if ($companyType != BaseJobApplyLimitConfig::COMPANY_TYPE_ALL_COOPERATION) {
                if (empty($companyData)) {
                    throw new MessageException('请录入禁投单位数据');
                }
            }
            // 投递提示必填
            if (empty($messageType)) {
                throw new MessageException('请选择投递提示');
            }
        }

        if ($resumeType == BaseJobApplyLimitConfig::RESUME_TYPE_ID) {
            // 根据这堆UUID到人才表里面找，并且需要考虑是否存在
            $resumeTextList = StringHelper::textToList($resumeData);
            if (count($resumeTextList) <= 0) {
                throw new MessageException('人才数据格式错误');
            }
            $resumeList = BaseResume::find()
                ->select([
                    'uuid',
                    'id',
                ])
                ->where(['uuid' => $resumeTextList])
                ->asArray()
                ->all();

            $resumeUUidList = array_column($resumeList, 'uuid');
            $resumeIdList   = array_column($resumeList, 'id');

            $errorList = [];
            foreach ($resumeTextList as $item) {
                if (!in_array($item, $resumeUUidList)) {
                    $message     = "ID{$item}";
                    $errorList[] = $message;
                }
            }
            if (count($errorList) > 0) {
                throw new MessageException(implode(',', $errorList) . '不存在');
            }
        }

        if ($resumeType == BaseJobApplyLimitConfig::RESUME_TYPE_TAG) {
            // 根据这堆标签到标签表里面找，并且需要考虑是否存在
            $resumeTextList = explode(',', $resumeData);

            $resumeTagList = BaseResumeTag::find()
                ->select([
                    'tag',
                    'id',
                ])
                ->where(['id' => $resumeTextList])
                ->asArray()
                ->all();

            $resumeTagIdList = array_column($resumeTagList, 'id');

            $errorList = [];
            foreach ($resumeTextList as $item) {
                if (!in_array($item, $resumeTagIdList)) {
                    $message     = "标签{$item}";
                    $errorList[] = $message;
                }
            }
            if (count($errorList) > 0) {
                throw new MessageException(implode(',', $errorList) . '不存在');
            }
        }

        if ($companyType == BaseJobApplyLimitConfig::COMPANY_TYPE_ID) {
            // 根据这堆UUID到人才表里面找，并且需要考虑是否存在
            $companyTextList = StringHelper::textToList($companyData);
            $companyList     = BaseCompany::find()
                ->select([
                    'uuid',
                    'id',
                ])
                ->where(['uuid' => $companyTextList])
                ->asArray()
                ->all();

            $companyUUidList = array_column($companyList, 'uuid');
            $companyIdList   = array_column($companyList, 'id');

            $errorList = [];
            foreach ($companyTextList as $item) {
                if (!in_array($item, $companyUUidList)) {
                    $message     = "ID{$item}";
                    $errorList[] = $message;
                }
            }
            if (count($errorList) > 0) {
                throw new MessageException(implode(',', $errorList) . '不存在');
            }
        }

        if ($companyType == BaseJobApplyLimitConfig::COMPANY_TYPE_TAG) {
            // 根据这堆标签到标签表里面找，并且需要考虑是否存在
            throw new MessageException('等待完善业务逻辑');
        }

        // 开始处理报错的基本数据
        if ($id) {
            $jobApplyLimitConfigModel = BaseJobApplyLimitConfig::findOne($id);
            if (!$jobApplyLimitConfigModel) {
                throw new MessageException('数据不存在');
            }
        } else {
            $jobApplyLimitConfigModel           = new BaseJobApplyLimitConfig();
            $jobApplyLimitConfigModel->admin_id = $adminId;
        }
        $jobApplyLimitConfigModel->type         = $type;
        $jobApplyLimitConfigModel->resume_type  = $resumeType;
        $jobApplyLimitConfigModel->resume_data  = $resumeData;
        $jobApplyLimitConfigModel->company_type = $companyType;
        $jobApplyLimitConfigModel->company_data = $companyData;
        $jobApplyLimitConfigModel->message_type = $messageType;
        $jobApplyLimitConfigModel->remark       = $remark;
        if (!$jobApplyLimitConfigModel->save()) {
            throw new MessageException($jobApplyLimitConfigModel->getFirstErrorsMessage());
        }
        // 根据不同类型拆表，在处理前，先清理这个活动id对应的所有数据
        if ($id) {
            $this->cleanRelData($id);
        }

        // 处理中间表数据
        if ($resumeType == BaseJobApplyLimitConfig::RESUME_TYPE_ID) {
            foreach ($resumeIdList as $item) {
                $jobApplyLimitResumeModel                            = new BaseJobApplyLimitResume();
                $jobApplyLimitResumeModel->job_apply_limit_config_id = $jobApplyLimitConfigModel->id;
                $jobApplyLimitResumeModel->resume_id                 = $item;
                $jobApplyLimitResumeModel->save();
            }
        }

        if ($resumeType == BaseJobApplyLimitConfig::RESUME_TYPE_TAG) {
            foreach ($resumeTagIdList as $item) {
                $jobApplyLimitResumeTagModel                            = new BaseJobApplyLimitResumeTag();
                $jobApplyLimitResumeTagModel->job_apply_limit_config_id = $jobApplyLimitConfigModel->id;
                $jobApplyLimitResumeTagModel->resume_tag_id             = $item;
                $jobApplyLimitResumeTagModel->save();
            }
        }

        if ($companyType == BaseJobApplyLimitConfig::COMPANY_TYPE_ALL_COOPERATION) {
            $jobApplyLimitCompanyTagModel                            = new BaseJobApplyLimitCompanyTag();
            $jobApplyLimitCompanyTagModel->job_apply_limit_config_id = $jobApplyLimitConfigModel->id;
            $jobApplyLimitCompanyTagModel->company_feature_tag_id    = 0;
            $jobApplyLimitCompanyTagModel->save();
        }

        if ($companyType == BaseJobApplyLimitConfig::COMPANY_TYPE_ID) {
            foreach ($companyIdList as $item) {
                $jobApplyLimitResumeTagModel                            = new BaseJobApplyLimitCompany();
                $jobApplyLimitResumeTagModel->job_apply_limit_config_id = $jobApplyLimitConfigModel->id;
                $jobApplyLimitResumeTagModel->company_id                = $item;
                $jobApplyLimitResumeTagModel->save();
            }
        }

        if ($companyType == BaseJobApplyLimitConfig::COMPANY_TYPE_TAG) {
            // 待完善
        }
    }

    public function edit($params, $adminId = 0)
    {
        $id = $params['id'];
        if (!$id) {
            throw new MessageException('数据不存在');
        }

        $this->add($params, $adminId);
    }

    public function delete($id)
    {
        $model = BaseJobApplyLimitConfig::findOne($id);
        if (!$model) {
            throw new MessageException('数据不存在');
        }
        $model->delete();
        $this->cleanRelData($id);
    }

    public function check($params)
    {
    }

    // 清理中间表数据(一共有四个中间表需要处理)
    public function cleanRelData($id)
    {
        BaseJobApplyLimitResume::deleteAll(['job_apply_limit_config_id' => $id]);
        BaseJobApplyLimitResumeTag::deleteAll(['job_apply_limit_config_id' => $id]);
        BaseJobApplyLimitCompany::deleteAll(['job_apply_limit_config_id' => $id]);
        BaseJobApplyLimitCompanyTag::deleteAll(['job_apply_limit_config_id' => $id]);
    }

}
