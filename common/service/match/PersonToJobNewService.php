<?php
namespace common\service\match;

use common\base\BaseActiveRecord;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyGroupScoreSystem;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobApplyLimitResume;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobClickLog;
use common\base\models\BaseJobCollect;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseMajor;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeIntention;
use common\base\models\BaseResumeJobFootprint;
use common\base\models\BaseWelfareLabel;
use common\helpers\ArrayHelper;
use common\helpers\DebugHelper;
use common\helpers\UrlHelper;
use common\libs\Cache;
use common\service\chat\CommonRule;
use common\service\jobApplyLimit\SearchService;
use Yii;

class PersonToJobNewService extends CommonMatchService
{

    const CACHE_KEY = "TMP:PERSON_TO_JOB_NEW_SERVICE:JOB_LIST";

    const CACHE_PARAMS_KEY = "TMP:PERSON_TO_JOB_NEW_SERVICE:PARAMS";

    const  MAJOR_UNLIMITED_ID = 716;

    const MAX_MATCH_JOB = 300;

    const PAGE_SIZE = 50;

    public $allAreaList  = [];
    public $cityList     = [];
    public $provinceList = [];

    public $jobCategoryId;
    public $majorIdLevel3Id;
    public $majorIdLevel2Id;
    public $education;

    public $tmpSqlList = [];
    public $tmpLogList = [];

    public $ruleJobList = [];

    public $matchTotal = 0;

    public $page;
    public $type = 1;
    public $memberId;

    public $data;

    // 1~12
    const RULE_LIST = [
        // No1：意向职位、学科专业、学历水平、意向城市（指意向“城市”；如无意向城市则忽略该条；以下同理）都匹配
        1  => [
            'where'     => [
                'city_id',
                'job_category_id',
                'major_id',
                'education_type',
            ],
            'innerJoin' => ['major'],
        ],
        // No2：意向职位、学科专业、学历水平、意向省份（如无意向省份则忽略该条；以下同理）都匹配
        2  => [
            'where'     => [
                'province_id',
                'job_category_id',
                'major_id',
                'education_type',
            ],
            'innerJoin' => ['major'],
        ],
        // No3：意向职位、学历水平、意向城市匹配，职位需求专业为“专业不限”
        3  => [
            'where'     => [
                'city_id',
                'job_category_id',
                'education_type',
                'major_unlimited_id',
            ],
            'innerJoin' => ['major'],
        ],
        // No4：意向职位、学历水平、意向省份匹配，职位需求专业为“专业不限”
        4  => [
            'where'     => [
                'province_id',
                'job_category_id',
                'education_type',
                'major_unlimited_id',
            ],
            'innerJoin' => ['major'],
        ],
        // No5：意向职位、学科专业、学历水平匹配，意向城市所属省份匹配
        5  => [
            'where'     => [
                'province_id',
                'job_category_id',
                'major_id',
                'education_type',
            ],
            'innerJoin' => ['major'],
        ],
        // No6：意向职位、学历水平、意向城市所属省份匹配，职位需求专业为“专业不限
        6  => [
            'where'     => [
                'province_id',
                'job_category_id',
                'education_type',
                'major_unlimited_id',
            ],
            'innerJoin' => ['major'],
        ],
        // No7：意向职位、学历水平、意向城市匹配，学科为“专业未分类”
        7  => [
            'where'     => [
                'city_id',
                'job_category_id',
                'education_type',
                'major_id',
            ],
            'innerJoin' => ['major'],
        ],
        // No8：意向职位、学历水平、意向省份匹配，学科为“专业未分类”
        8  => [
            'where'     => [
                'province_id',
                'job_category_id',
                'education_type',
                'major_id',
            ],
            'innerJoin' => ['major'],
        ],
        // No9：意向职位、学历水平、意向城市所属省份匹配，学科为“专业未分类”
        9  => [
            'where'     => [
                'province_id',
                'job_category_id',
                'education_type',
                'major_id',
            ],
            'innerJoin' => ['major'],
        ],
        // No10：意向职位、意向城市匹配
        10 => [
            'where' => [
                'city_id',
                'job_category_id',
            ],
        ],
        // No11 ：意向职位、意向省份匹配
        11 => [
            'where' => [
                'province_id',
                'job_category_id',
            ],
        ],
        // No12：意向职位匹配、意向城市所属省份匹配
        12 => [
            'where' => [
                'province_id',
                'job_category_id',
            ],
        ],
        // 13 => [],

    ];

    public function getList($params)
    {
        $this->operationPlatform = $params['platform'] ?: self::PLATFORM_WEB;

        BaseActiveRecord::openDb2();
        $this->initParams($params);
        BaseActiveRecord::closeDb2();

        $return = $this->handelSpecial($this->data);

        return $return;
    }

    public function initParams($params)
    {
        $intentionId    = $params['intentionId'];
        $memberId       = $params['memberId'];
        $this->memberId = $memberId;
        // $intentionInfo = BaseResumeIntention::findOne($intentionId);
        $intentionInfo = BaseResumeIntention::find()
            ->select('area_id,resume_id,job_category_id')
            ->where(['id' => $intentionId])
            ->asArray()
            ->one();
        $areaArray     = explode(',', $intentionInfo['area_id']);

        $allAreaList  = [];
        $cityList     = [];
        $provinceList = [];

        $this->page = $params['page'] ?: 1;
        $this->type = $params['type'] ?: 1;

        foreach ($areaArray as $item) {
            $allAreaList[] = $this->getAreaInfo($item);
            if ($item['level'] == 2) {
                $cityList[] = $this->getAreaInfo($item);
            } else {
                $provinceList[] = $this->getAreaInfo($item);
            }
        }
        $this->allAreaList  = $allAreaList;
        $this->cityList     = $cityList;
        $this->provinceList = $provinceList;

        //获取简历信息
        $lastEducationId = BaseResume::findOneVal(['id' => $intentionInfo['resume_id']], 'last_education_id');
        //获取最高学历信息
        $resumeEducation = BaseResumeEducation::find()
            ->where(['id' => $lastEducationId])
            ->select('major_id,education_id')
            ->asArray()
            ->one();

        $this->jobCategoryId   = $intentionInfo['job_category_id'];
        $this->majorIdLevel3Id = $resumeEducation['major_id'];
        $majorInfo             = $this->getMajorInfo($resumeEducation['major_id']);
        $this->majorIdLevel2Id = $majorInfo['parent_id'];

        $this->education = $resumeEducation['education_id'];

        $this->getJobAll();
    }

    public function getJobAll()
    {
        foreach ($this->cityList as $city) {
            $this->getJobList($city['id'], $this->jobCategoryId, $this->majorIdLevel2Id, $this->education);
        }

        foreach ($this->provinceList as $province) {
            $this->getJobList($province['id'], $this->jobCategoryId, $this->majorIdLevel2Id, $this->education);
        }

        $list       = [];
        $jobIdsList = [];
        foreach ($this->ruleJobList as $item) {
            foreach ($item as $jobItem) {
                if (!in_array($jobItem['job_id'], $jobIdsList)) {
                    $jobIdsList[] = $jobItem['job_id'];
                    $list[]       = $jobItem;
                }
            }
        }

        if ($this->type == 2) {
            // 按照时间戳倒序
            usort($list, function ($a, $b) {
                return $b['refresh_time_t'] * 1 - $a['refresh_time_t'] * 1;
            });
        }

        $list = $this->filterBanList($list);

        // if ($this->myHistoryJobApplyList) {
        //     // list里面去掉我点击过的
        //     $list = array_filter($list, function ($item) {
        //         return !in_array($item['job_id'], $this->myHistoryJobApplyList);
        //     });
        // }

        // 最终的list根据page和pagesize决定
        $returnList = array_slice($list, ($this->page - 1) * self::PAGE_SIZE, self::PAGE_SIZE);

        foreach ($returnList as &$value) {
            if ($value['announcement_id']) {
                $value['announcement_name'] = BaseArticle::find()
                    ->alias('b')
                    ->innerJoin(['a' => BaseAnnouncement::tableName()], 'a.article_id = b.id')
                    ->where(['a.id' => $value['announcement_id']])
                    ->select('b.title')
                    ->scalar();
            }

            if ($value['min_wage'] == 0 && $value['max_wage'] == 0) {
                $value['wage'] = '面议';
            } else {
                $value['wage'] = BaseJob::formatWage($value['min_wage'], $value['max_wage'], $value['wage_type']);
            }
            $value['announcement_name'] = $value['announcement_name'] ?? '';
            $value['education']         = BaseDictionary::getEducationName($value['education_type']);
            $value['experience']        = BaseDictionary::getExperienceName($value['experience_type']);

            $city                         = BaseArea::getAreaName($value['city_id']);
            $value['area_name']           = implode('-', [
                BaseArea::getAreaName($value['province_id']),
                $city,
            ]);
            $value['city']                = $city;
            $value['company_logo']        = $value['company_logo'] ?: Yii::$app->params['defaultCompanyLogo'];
            $value['company_type_name']   = BaseDictionary::getCompanyTypeName($value['company_type']);
            $value['company_nature_name'] = BaseDictionary::getCompanyNatureName($value['company_nature']);
            //专业处理
            $major_arr = explode(',', $value['major_id']);
            $major_set = [];
            if (!empty($major_arr)) {
                foreach ($major_arr as $k => $v) {
                    $item_name = BaseMajor::getMajorName($v);
                    if ($item_name) {
                        array_push($major_set, $item_name);
                    }
                }
            }

            $value['major_set'] = $major_set;
            $value['is_fast']   = '2';
            $value['is_top']    = '2';
            if ($value['job_delivery_type'] == 2) {//职位站内
                if ($value['job_delivery_way'] == 1) {//是平台
                    $value['is_fast'] = '1';
                }
            } elseif ($value['job_delivery_type'] == 0) {//职位跟随公告
                if ($value['announcement_delivery_type'] == 2 && $value['announcement_delivery_way'] == 1) {//跟随公告且是平台
                    $value['is_fast'] = '1';
                }
            }
            if ($value['top_id'] > 0) {
                $value['is_top'] = '1';
            }
            $value['url']              = UrlHelper::createJobDetailPath($value['job_id']);
            $value['company_url']      = UrlHelper::createCompanyDetailPath($value['company_id']);
            $value['announcement_url'] = UrlHelper::createAnnouncementDetailPath($value['announcement_id']);
            $value['is_cooperation']   = '2';//写死不显示官方
            $chatButtonInfo            = CommonRule::checkButtonAuth($value['job_id'], $this->memberId);
            $value['chatButtonIsShow'] = $chatButtonInfo['isShow'];
            $value['chatButtonType']   = $chatButtonInfo['buttonType'];
            $value['chatButtonName']   = $chatButtonInfo['buttonName'];
        }

        $data = [
            'list'          => $returnList,
            "recommendList" => [],
            'total'         => count($list),
        ];

        $this->data = $data;

        return $data;
    }

    public function getJobList($areaId, $jobCategoryId, $majorId, $education)
    {
        // 上面四个参数组成一个md5key
        $md5Key = md5($areaId . '_' . $jobCategoryId . '_' . $majorId . '_' . $education);
        $key    = self::CACHE_KEY . ':' . $md5Key;

        if ($cacheData = Cache::get($key)) {
            $this->ruleJobList = json_decode($cacheData, true);
        } else {
            $areaInfo = $this->getAreaInfo($areaId);

            if ($areaInfo['level'] == 2) {
                $cityId     = $areaId;
                $provinceId = $areaInfo['parent_id'];
            } else {
                $cityId     = 0;
                $provinceId = $areaId;
            }

            $select = [
                'j.id as job_id',
                'j.name as job_name',
                'j.wage_type',
                'j.min_wage',
                'j.max_wage',
                'j.province_id',
                'j.city_id',
                'j.experience_type',
                'j.education_type',
                'j.amount',
                'j.announcement_id',
                'j.company_id',
                'j.welfare_tag',
                'j.is_establishment',
                'j.major_id',
                'j.delivery_type as job_delivery_type',
                'j.delivery_way as job_delivery_way',
                'j.is_establishment',
                'j.education_type',
                //  refresh_time 转时间戳
                'UNIX_TIMESTAMP(j.refresh_time) as refresh_time_t',
                'c.full_name as company_name',
                'c.logo_url as company_logo',
                'c.type as company_type',
                'c.nature as company_nature',
                'c.is_cooperation',
            ];

            foreach (self::RULE_LIST as $k => $ruleInfo) {
                if ($this->matchTotal >= self::MAX_MATCH_JOB) {
                    return true;
                }
                // 规则从1~12
                if ($k == 1 || $k == 3 || $k == 5 || $k == 6 || $k == 7 || $k == 9 || $k == 10 || $k == 12) {
                    // 判断一下,如果不是城市,则跳过
                    if ($areaInfo['level'] == 1) {
                        continue;
                    }
                }

                if ($k == 2 || $k == 4 || $k == 8 || $k == 10 || $k == 11) {
                    // 判断一下,如果不是省份,则跳过
                    if ($areaInfo['level'] == 2) {
                        continue;
                    }
                }

                if (!$ruleInfo['where']) {
                    continue;
                }

                $query = BaseJob::find()
                    ->select($select)
                    ->alias('j')
                    ->innerJoin([
                        'c' => BaseCompany::tableName(),
                    ], 'c.id = j.company_id')
                    ->innerJoin([
                        'cgss' => BaseCompanyGroupScoreSystem::tableName(),
                    ], 'cgss.id = c.group_score_system_id')
                    ->where(['j.status' => 1]);

                if ($ruleInfo['innerJoin']) {
                    foreach ($ruleInfo['innerJoin'] as $table) {
                        if ($table == 'major') {
                            $query->innerJoin(['jmr' => BaseJobMajorRelation::tableName()], 'j.id=jmr.job_id');
                        }
                    }
                }

                foreach ($ruleInfo['where'] as $whereItem) {
                    // j.city_id = :city_id这种格式的
                    switch ($whereItem) {
                        case 'city_id':
                            $query->andWhere(['j.city_id' => $cityId]);
                            break;
                        case 'province_id':
                            $query->andWhere(['j.province_id' => $provinceId]);
                            break;
                        case 'job_category_id':
                            $query->andWhere(['j.job_category_id' => $jobCategoryId]);
                            break;
                        case 'major_id':
                            $query->andWhere(['jmr.major_id' => $majorId]);
                            break;
                        case 'major_unlimited_id':
                            // 专业不限
                            $query->andWhere(['jmr.major_id' => self::MAJOR_UNLIMITED_ID]);
                            break;
                        case 'education_type':
                            $query->andWhere(['j.education_type' => $education]);
                            break;
                        default:
                            break;
                    }
                }

                //                $list = $query->orderBy('j.refresh_time desc, c.sort desc,j.id desc')
                $list = $query->orderBy('j.refresh_date desc, j.is_first_release asc,cgss.score desc,j.id desc')
                    ->groupBy('j.id')
                    ->limit(self::MAX_MATCH_JOB - $this->matchTotal[$k])
                    ->asArray()
                    ->all();

                $this->matchTotal[$k] = $this->matchTotal[$k] + count($list);

                $oldJobList            = $this->ruleJobList[$k] ?? [];
                $this->ruleJobList[$k] = ArrayHelper::merge($oldJobList, $list);
            }

            Cache::set($key, json_encode($this->ruleJobList), 3600 * 3);
        }

        return true;
    }

    public function getAreaInfo($areaId)
    {
        // 这里尝试去套一些缓存
        $key = self::CACHE_PARAMS_KEY . ':AREA:' . $areaId;
        if ($cacheData = Cache::get($key)) {
            $data = json_decode($cacheData, true);
        } else {
            $data = BaseArea::find()
                ->select('id,name,level,parent_id')
                ->where(['id' => $areaId])
                ->asArray()
                ->one();

            Cache::set($key, json_encode($data));
        }

        return $data;
    }

    public function getMajorInfo($majorId)
    {
        $key = self::CACHE_PARAMS_KEY . ':MAJOR:' . $majorId;
        if ($cacheData = Cache::get($key)) {
            $data = json_decode($cacheData, true);
        } else {
            $data = BaseMajor::find()
                ->select('id,name,parent_id,level')
                ->where(['id' => $majorId])
                ->asArray()
                ->one();

            Cache::set($key, json_encode($data));
        }

        return $data;
    }

    public function handelSpecial($data)
    {
        $oldList = $data['list'];
        $newList = [];
        //到这里获取一下用户ID
        $memberId = BaseResume::findOneVal(['id' => $data['resumeId']], 'member_id');
        foreach ($oldList as $item) {
            switch ($this->operationPlatform) {
                case self::PLATFORM_MINI:
                    $item['major_name']      = count($item['major_set']) > 1 ? $item['major_set'][0] . '等' : ($item['major_set'][0] ?? '');
                    $item['welfare_tag_arr'] = BaseWelfareLabel::getWelfareLabelNameList($item['welfare_tag']);
                    break;
                case self::PLATFORM_WEB_PERSON:
                case self::PLATFORM_WEB:
                case self::PLATFORM_H5:
                    $item['welfare_tag_arr'] = array_slice(BaseWelfareLabel::getWelfareLabelNameList($item['welfare_tag']),
                        0, 2);
                    break;
                default:
                    $item['welfare_tag_arr'] = [];
                    break;
            }
            $item['is_collect']   = (BaseJobCollect::checkIsCollect($memberId, $item['job_id'])) ? '1' : '2';
            $item['apply_status'] = strval(BaseJobApplyRecord::checkJobApplyStatus($data['resumeId'], $item['job_id']));

            $newList[] = $item;
        }

        $data['list'] = $newList;

        return $data;
    }

    // 过滤该人才已投递过的职位
    // 过滤该人才禁投单位的职位
    private function filterBanList($list)
    {
        $memberId = $this->memberId;
        $resumeId = BaseResume::findOneVal(['member_id' => $memberId], 'id');

        // 首先找投递过的职位
        $jobIds = BaseJobApplyRecord::find()
            ->where(['resume_id' => $resumeId])
            ->select('job_id')
            ->column();

        if (count($jobIds) > 0) {
            // 过滤一遍
            $list = array_filter($list, function ($item) use ($jobIds) {
                return !in_array($item['job_id'], $jobIds);
            });
        }

        // 找禁投单位
        $banCompanyIds = (new SearchService())->getResumeBanCompany($resumeId);

        if ($banCompanyIds) {
            // 如果banCompanyIds里面有一个为0的值，就需要过滤所有合作单位
            if (in_array(0, $banCompanyIds)) {
                $list = array_filter($list, function ($item) {
                    return $item['is_cooperation'] == BaseCompany::IS_ABROAD_NO;
                });
            }

            // 然后把0拿出来
            $banCompanyIds = array_filter($banCompanyIds, function ($item) {
                return $item != 0;
            });

            // 如果还有值，就过滤
            if ($banCompanyIds) {
                $list = array_filter($list, function ($item) use ($banCompanyIds) {
                    return !in_array($item['company_id'], $banCompanyIds);
                });
            }
        }

        return $list;
    }

}