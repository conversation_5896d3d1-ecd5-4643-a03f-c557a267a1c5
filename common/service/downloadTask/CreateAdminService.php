<?php

namespace common\service\downloadTask;

use common\base\models\BaseAdminDownloadTask;
use queue\Producer;

/**
 * 这个只是单纯的消费应该处理的任务,不做实际的下载任务,具体的任务在对应的服务里面处理
 */
class CreateAdminService extends BaseService
{

    public function run($adminId, $type, $params)
    {
        $this->type    = $type;
        $this->adminId = $adminId;
        $this->params  = $params;

        $this->check();
        $this->create();
    }

    public function check()
    {
        // 检查一下这个人是否已经在前面创建过很多任务
        if (BaseAdminDownloadTask::find()
                ->where([
                    'status'   => BaseAdminDownloadTask::STATUS_WAIT,
                    'admin_id' => $this->adminId,
                ])
                ->count() >= 3) {
            throw new \Exception('总下载任务数量不能大于3个');
        }

        if (BaseAdminDownloadTask::find()
                ->where([
                    'status'   => BaseAdminDownloadTask::STATUS_WAIT,
                    'admin_id' => $this->adminId,
                    'type'     => $this->type,
                ])
                ->count() >= 1 && !in_array($this->type, [BaseAdminDownloadTask::TYPE_STATEMENT_REPORT_BUILDER])) {
            throw new \Exception('同一类型下载任务不能超过1个');
        }
    }

    public function create()
    {
        $model           = new BaseAdminDownloadTask();
        $model->type     = $this->type;
        $model->admin_id = $this->adminId;
        if ($this->params) {
            $model->params = json_encode($this->params);
        }
        $model->status = BaseAdminDownloadTask::STATUS_WAIT;
        if (!$model->save()) {
            throw new \Exception($model->getFirstErrorsMessage());
        }
        $id = $model->id;
        Producer::adminDownloadTask($id);
    }

}
