<?php

namespace common\service\downloadTask;

use admin\models\Major;
use common\base\models\BaseActivityFormRegistrationForm;
use common\base\models\BaseDictionary;
use common\base\models\BaseFile;
use common\helpers\DebugHelper;
use common\helpers\FileHelper;
use common\helpers\TimeHelper;
use common\libs\Aliyun\Oss;
use PhpOffice\PhpWord\Shared\ZipArchive;
use Yii;

class AllRegistrationFormFileListService
{
    /**
     * @throws \Exception
     */
    public function run($params): array
    {
        $beginTime = $params['begin_time'];
        $endTime   = $params['end_time'];
        $ids       = $params['ids'] ?? []; // 新增：选中的场次ID数组

        $query = BaseActivityFormRegistrationForm::find()
            ->select([
                'file_token',
                'name',
                'school',
                'college',
                'education_id',
                'major_id',
                'major_custom',
                'mobile',
                'education_id',
                'id',
                'option_ids',
                // 新增：需要查询option_ids字段用于筛选
            ])
            ->where([
                'activity_form_id' => $params['activity_form_id'],
                'status'           => BaseActivityFormRegistrationForm::STATUS_ACTIVE,
            ])
            ->andWhere([
                '<>',
                'file_token',
                '',
            ]);

        // 新增：如果指定报名id，则按场次筛选
        if (!empty($ids)) {
            $idArray = is_array($ids) ? $ids : explode(',', $ids);
            $query->andWhere(['id' => $idArray]);
        }

        if ($beginTime) {
            $query->andWhere([
                '>=',
                'add_time',
                TimeHelper::dayToBeginTime($beginTime),
            ]);
        }

        if ($endTime) {
            $query->andWhere([
                '<=',
                'add_time',
                TimeHelper::dayToEndTime($endTime),
            ]);
        }

        $fileTokenList = $query->groupBy('option_ids,resume_id')
            ->asArray()
            ->all();

        // 命名规则（默认的时候传)
        $type = $params['type'];

        // 自定义命名类型
        /**
         * { k: 'name', v: '姓名' },
         * { k: 'major', v: '所学专业' },
         * { k: 'school', v: '毕业学校' },
         * { k: 'education', v: '学历水平' }
         */
        $fields = $params['fields'];

        $oss = new Oss();
        // 随机生成一个文件夹
        $localPath = Yii::getAlias('@adminUploads') . '/tmp/' . time() . mt_rand();
        // 创建出来
        FileHelper::createDirectory($localPath);
        // 避免文件名重复(filename=>'xxx',count=>1)
        $fileNameList = [];

        foreach ($fileTokenList as $k => $item) {
            $fileIdArr = explode(',', $item['file_token']);
            $fileList  = BaseFile::find()
                ->select('id,path,name')
                ->where(['id' => $fileIdArr])
                ->asArray()
                ->all();

            $i    = $k + 1;
            $name = $item['name'];
            foreach ($fileList as $key => $file) {
                $suffixRing = explode('.', $file['name']);
                $suffix     = $suffixRing[count($suffixRing) - 1];

                if ($type) {
                    switch ($type) {
                        case 2:
                            $suffixName = $item['school'] . '.' . $suffix;
                            break;
                        case 3:
                            $suffixName = $item['college'] . '.' . $suffix;
                            break;
                        case 4:
                            $major      = Major::getMajorName($item['major_id']) ?: $item['major_custom'];
                            $suffixName = $major . '.' . $suffix;
                            break;
                        case 5:
                            $educationName = BaseDictionary::getEducationName($item['education_id']) ?: '';
                            $suffixName    = $educationName . '.' . $suffix;
                            break;
                        case 6:
                            $suffixName = $item['mobile'] . '.' . $suffix;
                            break;
                        default:
                            $suffixName = $file['name'];
                            break;
                    }
                    $fileName = $item['id'] . '_' . $name . '_附件简历_' . ($key + 1) . '_' . $suffixName;
                }

                if ($fields) {
                    // 若选择“自定义命名”方式，则文件命名规则默认为：{选项字段1)-{选项字段2}-{选项字段3）…。若文件命名重复，则按照生成顺序，在重复文件名后拼接序号N（从2开始）。
                    $fileNameBase = '';

                    foreach ($fields as $field) {
                        switch ($field) {
                            case 'name':
                                $fileNameBase .= $item['name'] . '-';
                                break;
                            case 'major':
                                $major        = Major::getMajorName($item['major_id']) ?: $item['major_custom'];
                                $fileNameBase .= $major . '-';
                                break;
                            case 'school':
                                $fileNameBase .= $item['school'] . '-';
                                break;
                            case 'education':
                                $educationName = BaseDictionary::getEducationName($item['education_id']) ?: '';
                                $fileNameBase  .= $educationName . '-';
                                break;
                        }
                    }

                    // 去掉最后一个下划线
                    $fileNameBase = rtrim($fileNameBase, '-');

                    // 如果文件名重复了
                    if (isset($fileNameList[$fileNameBase])) {
                        $fileNameList[$fileNameBase]['count']++;
                        $fileName = $fileNameBase . $fileNameList[$fileNameBase]['count'] . '.' . $suffix;
                    } else {
                        $fileNameList[$fileNameBase] = [
                            'count' => 1,
                            'name'  => $fileNameBase,
                        ];
                        $fileName                    = $fileNameBase . '.' . $suffix;
                    }
                }

                $content = $oss->getActiveAttachmentContent($file['path']);
                // 路径
                // 文件都保存过去按照命名
                $filePath = $localPath . '/' . $fileName;
                file_put_contents($filePath, $content);
            }
        }

        // 打包成一个压缩包
        // $zipPath = $localPath . '.zip';
        // FileHelper::zip($localPath, $zipPath);

        if ($this->createZip($localPath, $localPath . '.zip')) {
            // 删除文件夹里面的全部文件

            // FileHelper::removeDirectory($localPath);
        }

        return [
            'zipPath' => $localPath . '.zip',
        ];
    }

    /**
     * 把某个文件夹打包成压缩包
     */
    function createZip($source, $destination)
    {
        if (!extension_loaded('zip') || !file_exists($source)) {
            return false;
        }

        $zip = new ZipArchive();
        if (!$zip->open($destination, ZIPARCHIVE::CREATE)) {
            return false;
        }

        $source = str_replace('\\', '/', realpath($source));

        if (is_dir($source) === true) {
            $files = new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($source),
                \RecursiveIteratorIterator::SELF_FIRST);

            foreach ($files as $file) {
                $file = str_replace('\\', '/', $file);

                // Ignore "." and ".." folders
                if (in_array(substr($file, strrpos($file, '/') + 1), [
                    '.',
                    '..',
                ])) {
                    continue;
                }

                $file = realpath($file);

                if (is_dir($file) === true) {
                    $zip->addEmptyDir(str_replace($source . '/', '', $file . '/'));
                } else {
                    if (is_file($file) === true) {
                        $zip->addFromString(str_replace($source . '/', '', $file), file_get_contents($file));
                    }
                }
            }
        } else {
            if (is_file($source) === true) {
                $zip->addFromString(basename($source), file_get_contents($source));
            }
        }

        return $zip->close();
    }

}
