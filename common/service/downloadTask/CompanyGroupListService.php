<?php
/**
 * create user：shannon
 * create time：2024/7/2 09:42
 */
namespace common\service\downloadTask;

use common\base\models\BaseAdmin;
use common\base\models\BaseAdminDownloadTask;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyGroup;
use common\base\models\BaseCompanyGroupRelation;
use common\helpers\TimeHelper;

class CompanyGroupListService extends BaseService
{
    public function run($params)
    {
        //根据条件获取数据
        $query = BaseCompanyGroup::find()
            ->where(['is_delete' => BaseCompanyGroup::IS_DELETE_NO]);

        //关键字搜索
        $query->andFilterWhere([
            'or',
            ['id' => $params['keyword']],
            [
                'like',
                'group_name',
                $params['keyword'],
            ],
        ]);
        //搜索创建时间
        if (!empty($params['add_time_start']) && !empty($params['add_time_end'])) {
            $query->andFilterWhere([
                'between',
                'add_time',
                TimeHelper::dayToBeginTime($params['add_time_start']),
                TimeHelper::dayToEndTime($params['add_time_end']),
            ]);
        }
        $groupIds = $query->select('id')
            ->column();

        //获取含有分组单位的ID
        $companyIds  = BaseCompanyGroupRelation::find()
            ->where(['group_id' => $groupIds])
            ->select('company_id')
            ->column();
        $companyData = BaseCompany::find()
            ->alias('c')
            ->select([
                'c.id',
                'c.full_name',
                'GROUP_CONCAT(cg.group_name) as groupNames',
                'GROUP_CONCAT(cg.id) as groupIds',
            ])
            ->leftJoin(['cgr' => BaseCompanyGroupRelation::tableName()], 'cgr.company_id = c.id')
            ->leftJoin(['cg' => BaseCompanyGroup::tableName()], 'cg.id = cgr.group_id')
            ->where(['c.id' => $companyIds])
            ->groupBy('c.id')
            ->asArray()
            ->all();
        $headers     = [
            '单位ID',
            '单位名称',
            '推广群组ID',
            '推广群组名称',
        ];
        $data        = [];
        foreach ($companyData as $item) {
            $data[] = [
                $item['id'],
                $item['full_name'],
                $item['groupIds'],
                $item['groupNames'],
            ];
        }

        return [
            'headers'  => $headers,
            'data'     => $data,
            'fileName' => BaseAdminDownloadTask::TYPE_COMPANY_GROUP_LIST_NAME . date('YmdHis'),
        ];
    }
}