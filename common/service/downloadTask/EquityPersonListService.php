<?php

namespace common\service\downloadTask;

use admin\models\Equity;
use admin\models\Order;
use common\base\models\BaseAdminDownloadTask;
use common\base\models\BaseResumeEquityActionRecord;
use common\base\models\BaseResumeOrder;
use common\helpers\UUIDHelper;
use common\libs\Excel;

class EquityPersonListService
{
    /**
     * 导出数据准备
     * @param $params
     * @return array
     * @throws \yii\base\Exception
     */
    public function run($params)
    {
        $fileName = BaseAdminDownloadTask::TYPE_PERSON_EQUITY_LIST_NAME . '_' . date('YmdHis');
        //获取query句柄
        $query = Equity::queryBuilder($params);
        //获取数据
        $data = $query->select([
            'rear.id',
            'rear.resume_id',
            'rear.equity_id',
            'rear.equity_type',
            'rear.action_type',
            'rear.operation_type',
            'rear.operation_id',
            'rear.relation_remark',
            'rear.add_time',
            'r.name',
            'res.name as equity_name',

        ])
            ->orderBy('rear.add_time desc')
            ->asArray()
            ->all();
        //定义表头
        $headers = [
            '简历ID',
            '姓名',
            '权益类型名称',
            '权益标识名称',
            '操作类型名称',
            '操作人',
            '操作时间',
            '备注',
        ];
        //循环处理数据
        $excel_data = [];
        foreach ($data as $value) {
            //简历ID加密
            $value['encrypt_resume_id'] = UUIDHelper::encrypt(UUIDHelper::TYPE_PERSON, $value['resume_id']);
            //权益标识名称
            $value['equity_type_name'] = BaseResumeEquityActionRecord::EQUITY_TYPE_TEXT[$value['equity_type']];
            //操作类型名称
            $value['action_type_name'] = BaseResumeEquityActionRecord::ACTION_TYPE_LIST[$value['action_type']];
            //操作人
            $value['operation_name'] = BaseResumeEquityActionRecord::getOperationName($value['operation_type'],
                $value['operation_id']);
            //释放内存
            unset($value['operation_type'], $value['operation_id'], $value['equity_id'], $value['equity_type'], $value['action_type']);
            //组装数据
            $excel_data[] = [
                $value['encrypt_resume_id'],
                $value['name'],
                $value['equity_name'],
                $value['equity_type_name'],
                $value['action_type_name'],
                $value['operation_name'],
                $value['add_time'],
                $value['relation_remark'],
            ];
        }
        //释放
        unset($data);

        return [
            'data'     => $excel_data,
            'headers'  => $headers,
            'fileName' => $fileName,
        ];
    }
}