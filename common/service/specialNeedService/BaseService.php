<?php

namespace common\service\specialNeedService;

/**
 * 整个特殊需求的服务基类
 * 在开发工程中，有很多时候会遇到一些特殊的需求，这些需求可能是一些特殊的业务逻辑，也可能是一些特殊的功能需求，但是这些需求都是不同于我们平时的需求的，所以我们需要对这些需求进行特殊的处理，这个时候我们就需要一个特殊需求的服务基类，这个基类中会包含一些特殊需求的处理逻辑，这样我们在开发的时候就可以直接继承这个基类，然后在子类中实现具体的业务逻辑，这样就可以很方便的实现特殊需求的处理。
 */
class BaseService
{

    public $config = [
        // 禅道944的配置
        '944' => [
            'announcementId' => 251855,
            'companyId'      => 1214,
            'jobRanking'     => [
                1278773,
                1278774,
                1278775,
            ],
            'jobAddMajor'    => '中共党史党建学',
        ],
    ];

    public $isOpen = false;

    /**
     * 特殊需求的处理逻辑检查，并不是所有功能都会开启，会因为某些时间段或者系统里面某些参数导致是否开启，在这里做一个统一的初始化处理
     * @param $data
     */
    public function __construct()
    {
        $this->checkSpecialNeed();
    }

    /**
     * 特殊需求的处理逻辑检查
     */
    public function checkSpecialNeed()
    {
        // 这里是特殊需求的处理逻辑检查，主要是根据当前的类和方法去初始化，是否需要调用
        // 获取当前的类名
        $className = get_class($this);
        // 获取当前的方法名
        $methodName = get_class_methods($this)[0];
        // 这里是一个简单的逻辑，根据当前的类名和方法名去判断是否需要开启特殊需求的处理逻辑
        if ($className == 'common\service\specialNeedService\AnnouncementInformationService' && $methodName == 'handelMajorRanking') {
            if (\Yii::$app->params['specialNeed']['AnnouncementInformationProfessionalRanking'] == 1) {
                $this->isOpen = true;
            }
        }

        if ($className == 'common\service\specialNeedService\AnnouncementInformationService' && $methodName == 'handelDetailInfo') {
            if (\Yii::$app->params['specialNeed']['AnnouncementInformationDetail'] == 1) {
                $this->isOpen = true;
            }
        }

        // 这里是一个简单的逻辑，根据当前的类名和方法名去判断是否需要开启特殊需求的处理逻辑
        if ($className == 'common\service\specialNeedService\AnnouncementInformationService' && $methodName == 'handelJobList') {
            // 考虑用params来处理？
            if (\Yii::$app->params['specialNeed']['AnnouncementInformationJobList'] == 1) {
                $this->isOpen = true;
            }
        }

        if ($className == 'common\service\specialNeedService\JobApplyService' && $methodName == 'check') {
            // 考虑用params来处理？
            if (\Yii::$app->params['specialNeed']['JobApplyCheck'] == 1) {
                $this->isOpen = true;
            }
        }

        if ($className == 'common\service\specialNeedService\JobInformationService' && $methodName == 'handelJobDetail') {
            // 考虑用params来处理？
            if (\Yii::$app->params['specialNeed']['JobInformationDetail'] == 1) {
                $this->isOpen = true;
            }
        }

        // 单位
        if ($className == 'common\service\specialNeedService\CompanyInformationService' && $methodName == 'handelCompanyDetail') {
            // 考虑用params来处理？
            if (\Yii::$app->params['specialNeed']['CompanyInformationDetail'] == 1) {
                $this->isOpen = true;
            }
        }

        // 单位公告列表
        if ($className == 'common\service\specialNeedService\CompanyInformationService' && $methodName == 'handelCompanyAnnouncementList') {
            // 考虑用params来处理：
            if (\Yii::$app->params['specialNeed']['CompanyInformationAnnouncementList'] == 1) {
                $this->isOpen = true;
            }
        }

        // 单位职位列表
        if ($className == 'common\service\specialNeedService\CompanyInformationService' && $methodName == 'handelCompanyJobList') {
            // 考虑用params来处理：
            if (\Yii::$app->params['specialNeed']['CompanyInformationJobList'] == 1) {
                $this->isOpen = true;
            }
        }
    }

}
