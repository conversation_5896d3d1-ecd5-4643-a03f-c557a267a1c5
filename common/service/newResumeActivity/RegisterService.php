<?php

namespace common\service\newResumeActivity;

use common\base\models\BaseNewResumeActivityAccept;
use common\base\models\BaseNewResumeActivityShare;

/**
 * 拉新活动注册服务
 */
class RegisterService extends BaseService
{
    /**
     * 创建拉新活动
     */
    public function run($token, $resumeId)
    {
        $share = BaseNewResumeActivityShare::findOne(['token' => $token]);
        if (!$share) {
            return [
                'code' => 1,
                'msg'  => '活动不存在',
            ];
        }
        if (!$share->status) {
            return [
                'code' => 1,
                'msg'  => '活动已结束',
            ];
        }

        $shareId       = $share->id;
        $shareResumeId = $share->resume_id;

        $activity                      = new BaseNewResumeActivityAccept();
        $activity->share_id            = $shareId;
        $activity->resume_id           = $resumeId;
        $activity->share_resume_id     = $shareResumeId;
        $activity->status              = BaseNewResumeActivityAccept::STATUS_WAIT;
        $activity->share_issue_status  = BaseNewResumeActivityAccept::ISSUE_STATUS_WAIT;
        $activity->accept_issue_status = BaseNewResumeActivityAccept::ISSUE_STATUS_WAIT;

        if ($activity->save()) {
            $share->accept_amount += 1;
            $share->save();
        }
    }
}
