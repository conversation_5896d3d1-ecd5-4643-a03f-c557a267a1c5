<?php
/**
 * create user：伍彦川
 * create time：2025/6/9 09:41
 */
namespace common\service\v2\announcement;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseAnnouncementLog;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseCompany;
use common\base\models\BaseJob;
use common\components\MessageException;
use common\helpers\IpHelper;
use common\helpers\StringHelper;
use common\service\company\PushEditMessageService;
use common\service\jobTop\JobTopApplication;
use yii\base\Exception;

class OfflineService extends BaseService
{
    private $isBatch = false;
    private $isJob   = false;

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 设置是否职位过来的下线
     * @param $params
     */
    public function setIsJob()
    {
        $this->isJob = true;

        return $this;
    }

    /**
     * 公告批量下线
     * @param $params
     * @return array
     * @throws Exception
     */
    public function offlineBatch($params)
    {
        $failMessage   = [];
        $this->isBatch = true;
        $this->initInfo();

        $ids = StringHelper::changeStrToFilterArr($params['announcementId'] ?? '');
        foreach ($ids as $id) {
            try {
                $this->params['announcementId'] = $id;
                $this->params['reason']         = $params['reason'];
                $this->run();
            } catch (MessageException $e) {
                $failMessage[] = $e->getMessage();
            }
        }

        return [
            'failMessage' => implode('<br>', $failMessage),
        ];
    }

    /**
     * 公告单条下线
     * @param $params
     * @return true
     * @throws Exception
     * @throws MessageException
     */
    public function offline($params)
    {
        $this->params = $params;
        $this->initInfo();
        $this->run();

        return true;
    }

    /**
     * 执行下线
     * @throws MessageException
     */
    public function run()
    {
        if (empty($this->params['announcementId'])) {
            throw new MessageException('公告ID不能为空');
        }

        $this->announcementId = $this->params['announcementId'];
        $this->setAnnouncement($this->announcementId);
        $announcementInfo          = $this->announcementInfo;
        $this->oldAnnouncementInfo = $announcementInfo;
        if (!$this->oldAnnouncementInfo) {
            throw new \Exception('公告不存在');
        }

        if ($this->operationPlatform == self::PLATFORM_ADMIN && $this->isBatch) {
            $operateRes = (new OperateService())->run('offline', $this->articleInfo->is_show,
                $this->announcementInfo->status, $this->announcementInfo->audit_status);
            if ($operateRes['disabled'] != 1) {
                throw new MessageException('公告ID:' . $this->oldAnnouncementInfo->uuid . '，公告状态不允许下线');
            }
        }

        if ($this->oldAnnouncementInfo->status == BaseAnnouncement::STATUS_OFFLINE) {
            throw new MessageException('公告ID:' . $this->oldAnnouncementInfo->uuid . '，公告状态不允许下线');
        }
        if ($this->oldAnnouncementInfo->status != BaseAnnouncement::STATUS_ONLINE && !$this->isBatch) {
            throw new MessageException('公告ID:' . $this->oldAnnouncementInfo->uuid . '，公告状态不允许下线');
        } elseif ($this->oldAnnouncementInfo->status != BaseAnnouncement::STATUS_ONLINE && $this->isBatch == false) {
            throw new \Exception('公告ID:' . $this->oldAnnouncementInfo->uuid . '，公告状态不允许下线');
        }

        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            $this->baseCheckMemberPackage();
        }

        if (!$this->params['reason'] && $this->companyInfo->is_cooperation == BaseCompany::COOPERATIVE_UNIT_YES && $this->operationPlatform == self::PLATFORM_ADMIN) {
            throw new \Exception('合作单位操作下线请填写下线原因');
        }
        $this->articleInfo->status = BaseArticle::STATUS_OFFLINE;
        if (!$this->articleInfo->save()) {
            throw new \Exception($this->articleInfo->getFirstErrorsMessage());
        }
        // 运营后台操作下线=违规
        switch ($this->operationPlatform) {
            // admin端
            case self::PLATFORM_ADMIN:
                $this->announcementInfo->offline_type = BaseAnnouncement::OFFLINE_TYPE_VIOLATION;
                break;
            // 企业端
            case  self::PLATFORM_WEB_COMPANY:
                $this->announcementInfo->offline_type = BaseAnnouncement::OFFLINE_TYPE_HAND;
                break;
            default:
                $this->announcementInfo->offline_type = BaseAnnouncement::OFFLINE_TYPE_AUTO;
                break;
        }

        $this->announcementInfo->status         = BaseAnnouncement::STATUS_OFFLINE;
        $this->announcementInfo->offline_reason = is_null($this->params['reason']) ? '' : $this->params['reason'];
        $this->announcementInfo->offline_time   = CUR_DATETIME;
        if (!$this->announcementInfo->save()) {
            throw new \Exception($this->announcementInfo->getFirstErrorsMessage());
        }

        // 删除公告文档属性
        BaseArticleAttribute::deleteAttribute($this->articleInfo->id);
        //如果是职位过来的就不需要执行此段操作
        if (!$this->isJob) {
            $this->updateJobStatus();
        }

        $this->handleLog();
        $this->log($this->isBatch ? BaseAnnouncementLog::TYPE_OFFLINE_BATCH : BaseAnnouncementLog::TYPE_OFFLINE);
        //后置处理
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            (new PushEditMessageService())->offline(['announcementId' => $this->announcementInfo['id']]);
        }

        $this->after();

        return true;
    }

    /**
     * 处理后置逻辑
     */
    private function after()
    {
        $this->updateAnnouncementPiFlag();
        $this->updateAnnouncementMiniAppType();
        $this->updateJobAnnouncementAmount();
        $this->updateAnnouncementBoShiHouTable();
        $this->updateJobAnnouncementRelation();
        $this->updateStatAnnouncementCount();

        $this->runAutoColumnAfter();
    }

    /**
     * 更新公告下职位的状态
     * @throws Exception
     */
    private function updateJobStatus()
    {
        $jobIds = BaseJob::find()
            ->select('id')
            ->where([
                'announcement_id' => $this->announcementId,
                'status'          => BaseJob::STATUS_ONLINE,
                'is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->asArray()
            ->column();
        if ($jobIds) {
            $jobIdsStr = implode(',', $jobIds);

            (new \common\service\v2\job\OfflineService())
                ->setPlatform($this->operationPlatform)
                ->setParams([
                    'jobId'  => $jobIdsStr,
                    'reason' => empty($this->params['reason']) ? '' : $this->params['reason'],
                    'isCooperation' => $this->params['isCooperation']
                ])
                ->setBatch()
                ->setIsAnnouncement()
                ->run();
            //置顶取消
            $app = JobTopApplication::getInstance();
            $app->updateStatusByJobStatus($jobIds);
        }
    }

    /**
     * 在执行后
     * @throws \yii\base\NotSupportedException
     */
    public function handleLog()
    {
        // 记录一下log数据
        $handleLogData = [
            'add_time'        => CUR_DATETIME,
            'job_id'          => 0,
            'handle_type'     => strval(BaseAnnouncementHandleLog::HANDLE_TYPE_OFFLINE),
            'handler_type'    => strval($this->params['platformType']),
            'handler_id'      => $this->params['userId'],
            'handler_name'    => $this->params['username'],
            'handle_before'   => json_encode([
                '公告状态' => BaseAnnouncement::STATUS_LIST[$this->oldAnnouncementInfo->status],
                '更新时间' => $this->oldAnnouncementInfo->update_time,
            ], JSON_UNESCAPED_UNICODE),
            'handle_after'    => json_encode([
                '公告状态' => BaseAnnouncement::STATUS_LIST[$this->announcementInfo->status],
                '下线原因' => $this->params['reason'],
                '更新时间' => CUR_DATETIME,
            ], JSON_UNESCAPED_UNICODE),
            'ip'              => IpHelper::getIpInt(),
            'announcement_id' => $this->announcementInfo->id,
        ];

        return BaseAnnouncementHandleLog::createInfo($handleLogData);
    }
}