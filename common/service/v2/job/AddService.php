<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\job;

use common\base\models\BaseCompanyPackageChangeLog;
use common\base\models\BaseJob;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseJobLog;
use common\helpers\IpHelper;
use common\helpers\UUIDHelper;
use common\libs\WxWork;
use common\service\companyPackage\CompanyPackageApplication;
use common\service\v2\announcement\AfterService;
use queue\Producer;
use Yii;
use yii\base\Exception;

/**
 * 职位添加
 * 基础建设服务类
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class AddService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 设置数据
     * @return $this
     */
    public function setParams($params)
    {
        $this->params = $params;

        return $this;
    }

    /**
     * 执行添加职位
     * @throws Exception
     */
    public function run()
    {
        //获取表单参数
        if (!$this->isBatch && empty($this->params)) {
            $this->params = Yii::$app->request->post();
        }
        //初始化一些数据
        $this->initInfo();
        //检验数据合法性
        $this->dataVerify();
        //检验一下企业的权益套餐
        $this->companySourcePackageCheck();
        //调用写表逻辑写入职位表
        $this->saveTable();
        //写入职位联系人与职位协同
        $this->contact();
        //更新职位的uuid
        $this->updateUuid();
        //处理添加后置逻辑
        $this->after();
        //最后写一下日志
        $this->handleLog();
        $this->log($this->isBatch ? BaseJobLog::TYPE_ADD_BATCH : BaseJobLog::TYPE_ADD);

        //完成返回
        return ['jobId' => $this->jobId];
    }

    /**
     * 处理所有后置逻辑
     */
    private function after()
    {
        $this->updateJobCategoryRelationTable();
        $this->updateJobWelfareRelationTable();
        $this->updateJobMajorRelationTable();
        $this->updateJobMiniAppType();
        $this->updateJobBoShiHouTable();
        $this->updateJobPiFlag();
        $this->updateStatInfo();
        $this->updateJobCompanySort();
        $this->runAutoColumnAfter();
        if ($this->jobInfo->announcement_id) {
            (new AfterService())->setPlatform($this->operationPlatform)
                ->run($this->jobInfo->announcement_id);
        }
    }

    /**
     * 处理审核日志写入
     * @return void
     * @throws Exception
     * @throws \yii\base\NotSupportedException
     */
    private function handleLog()
    {
        //操作动作入表
        $jobHandleLog = [
            'add_time'        => CUR_DATETIME,
            'job_id'          => $this->jobId,
            'handle_type'     => BaseJobHandleLog::HANDLE_TYPE_RELEASE,
            'handler_type'    => $this->params['platformType'],
            'handler_id'      => $this->params['userId'],
            'handler_name'    => $this->params['username'],
            'handle_before'   => json_encode(['职位创建时间' => CUR_DATETIME]),
            'handle_after'    => json_encode(['职位创建时间' => CUR_DATETIME]),
            'ip'              => IpHelper::getIpInt(),
            'announcement_id' => 0,
        ];
        BaseJobHandleLog::createInfo($jobHandleLog);
    }

    /**
     * 检查资源
     * @throws Exception
     */
    private function companySourcePackageCheck()
    {
        // 单位端纯职位
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY && empty($this->params['announcementId'])) {
            if ($this->params['auditStatus'] == BaseJob::AUDIT_STATUS_WAIT_AUDIT) {
                $this->baseCheckMemberPackage();
                if ($this->companyPackageConfigModel->job_amount <= 0) {
                    throw new Exception('职位发布资源不足');
                }

                // 发布职位
                $companyPackageApplication = new CompanyPackageApplication();
                $remark                    = BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME[BaseCompanyPackageChangeLog::HANDLE_TYPE_JOB_RELEASE];
                $companyPackageApplication->jobRelease($this->companyInfo->id, 1, $remark);
            }
        }
    }
}