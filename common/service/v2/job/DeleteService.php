<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\job;

use common\base\models\BaseJob;
use common\base\models\BaseJobEdit;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseJobLog;
use common\base\models\BaseUser;
use common\components\MessageException;
use common\helpers\IpHelper;
use common\helpers\StringHelper;
use yii\base\Exception;

/**
 * 职位删除
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class DeleteService extends BaseService
{
    private $handleLogData;

    public function __construct()
    {
        parent::__construct();
    }

    public function delete($params)
    {
        $this->initInfo();

        $this->setJob($params['jobId'] ?? 0)
            ->validate()
            ->beforeHandleLog()
            ->run()
            ->handleLog()
            ->log(BaseJobLog::TYPE_DELETE)
            ->afterUpdateJob();
    }

    public function deleteBatch($params)
    {
        $failMessage = [];
        $jobIds = StringHelper::changeStrToFilterArr($params['jobId'] ?? '');
        foreach ($jobIds as $jobId) {
            try {
                $this->initInfo();

                $this->setJob($jobId)
                    ->validate()
                    ->beforeHandleLog()
                    ->run()
                    ->handleLog()
                    ->log(BaseJobLog::TYPE_DELETE_BATCH)
                    ->afterUpdateJob();
            } catch (\Exception $exception) {
                $failMessage[] = '职位ID：' . $jobId . '，删除失败，原因：' . $exception->getMessage();
            }
        }

        return [
            'failMessage' => implode('<br>', $failMessage),
        ];
    }

    /**
     * 在执行后
     * @throws \yii\base\NotSupportedException
     */
    public function handleLog()
    {
        $this->handleLogData['handle_after'] = json_encode([
            // 更新后再补全
            '职位状态' => BaseJob::JOB_STATUS_NAME[BaseJob::STATUS_DELETE],
            '更新时间' => CUR_DATETIME,
        ], JSON_UNESCAPED_UNICODE);
        // 消耗套餐,写日志等等
        BaseJobHandleLog::createInfo($this->handleLogData);

        return $this;
    }

    private function run()
    {
        $this->jobInfo->status      = BaseJob::STATUS_DELETE;
        $this->jobInfo->delete_time = CUR_DATETIME;
        if (!$this->jobInfo->save()) {
            throw new Exception($this->jobInfo->getFirstErrorsMessage());
        }

        return $this;
    }

    private function beforeHandleLog()
    {
        // 记录一下log数据
        $this->handleLogData = [
            'add_time'        => CUR_DATETIME,
            'job_id'          => $this->jobInfo->id,
            'handle_type'     => BaseJobHandleLog::HANDLE_TYPE_DELETE,
            'handler_type'    => $this->params['platformType'],
            'handler_id'      => $this->params['userId'],
            'handler_name'    => $this->params['username'],
            'handle_before'   => json_encode([
                '职位状态' => BaseJob::JOB_STATUS_NAME[$this->jobInfo->status],
                '更新时间' => $this->jobInfo->update_time,
            ], JSON_UNESCAPED_UNICODE),
            'handle_after'    => '',
            'ip'              => IpHelper::getIpInt(),
            'announcement_id' => $this->jobInfo->announcement_id,
        ];

        return $this;
    }

    private function validate()
    {
        // 获取按钮是否可以按的逻辑
        $operateRes = (new OperateService())->run('delete', $this->jobInfo->audit_status, $this->jobInfo->status,
            $this->jobInfo->is_article, $this->jobInfo->is_show, 0, 0);
        if ($operateRes['disabled'] != 1) {
            throw new MessageException('操作失败，状态不符合');
        }

        return $this;
    }
}