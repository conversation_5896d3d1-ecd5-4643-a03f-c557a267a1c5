<?php

namespace common\service\jobSubscribe;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobSubscribeSendLog;
use common\helpers\ArrayHelper;
use common\helpers\TimeHelper;

class GetLogJobListService
{
    private $id;

    public function run($id)
    {
        $this->id = $id;

        return $this->getList();
    }

    private function getList()
    {
        $model = BaseJobSubscribeSendLog::findOne($this->id);

        $list = $this->searchJobList($model->job_ids);

        return $list;
    }

    private function searchJobList($ids)
    {
        $list = BaseJob::find()
            ->alias('j')
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id=j.company_id')
            ->select([
                'j.id',
                'j.name as jobName',
                'j.education_type as educationType',
                'j.major_id as majorId',
                'j.amount',
                'j.refresh_date as refreshDate',
                'j.first_release_time as firstReleaseTime',
                'c.full_name as companyName',
                'j.city_id as cityId',
                'j.min_wage as minWage',
                'j.max_wage as maxWage',
                'j.wage_type as wageType',
                'j.announcement_id as announcementId',
                'j.experience_type as experienceType',
                'j.company_id as companyId',
                'c.type',
            ])
            ->where([
                'j.id' => explode(',', $ids),
            ])
            ->orderBy([new \yii\db\Expression("FIELD (j.id, $ids)")])
            ->asArray()
            ->all();

        $visitUrl = 'http://' . str_replace('http://', '', \Yii::$app->params['pcHost']);
        foreach ($list as &$item) {
            //获取教育名称
            $item['education'] = BaseDictionary::getEducationName($item['educationType']);
            //获取专业名称
            $item['majorName'] = BaseJob::getSimpleMajorText($item['id']);
            //获取城市
            $item['city'] = BaseArea::getAreaName($item['cityId']);
            //获取职位跳转链接
            $item['url']         = $visitUrl . '/job/detail/' . $item['id'] . '.html';
            $item['address']     = 'https://img.gaoxiaojob.com/uploads/static/image/resume/address.png';
            $item['refreshDate'] = TimeHelper::formatDateByYear($item['refreshDate']);
            //获取公告
            $item['announcementName'] = BaseAnnouncement::findOneVal(['id' => $item['announcementId']], 'title');
            //获取工作经验
            $item['experience'] = BaseDictionary::getExperienceName($item['experience_type']);
            //单位类型
            $item['companyTypeName']   = BaseDictionary::getCompanyTypeName($item['type']);
            $item['companyNatureName'] = BaseDictionary::getCompanyNatureName($item['type']);
            //急聘、回复快
            $item['isTop']  = BaseJob::isTop($item['id']);
            $item['isFast'] = BaseJob::isFast($item['id']);

            //拼接工资
            if ($item['minWage'] == 0 && $item['maxWage'] == 0) {
                $item['wage'] = '面议';
            } else {
                $item['wage'] = BaseJob::formatWage($item['minWage'], $item['maxWage'], $item['wageType']);
            }
        }

        $checkData = [
            'education',
            'majorName',
            'amount',
            'city',
            'refreshDate',
        ];

        return ArrayHelper::arrayToDisplay($list, $checkData);
    }

}