<?php

namespace common\libs;

use Yii;
use common\libs\WxminiSDK\WXBizDataCrypt;
use common\libs\WxminiSDK\ErrorCode;
use yii\base\Exception;

class WxMini
{
    public function httpGet($url)
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_TIMEOUT, 500);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_URL, $url);
        $res = curl_exec($curl);
        curl_close($curl);

        return $res;
    }

    /**
     * @return array
     */
    public function getJscode2Session($data = []): array
    {
        $appid         = Yii::$app->params['wx']['appid'];
        $secret        = Yii::$app->params['wx']['secret'];
        $grant_type    = "authorization_code";
        $code          = $data['code'];
        $encryptedData = $data['encryptedData'];
        $iv            = $data['iv'];
        $signature     = $data['signature'];
        $rawData       = $data['rawData'];

        $url        = "https://api.weixin.qq.com/sns/jscode2session?" . "appid=" . $appid . "&secret=" . $secret . "&js_code=" . $code . "&grant_type=" . $grant_type;
        $res        = json_decode($this->httpGet($url), true);
        $sessionKey = $res['session_key'];
        $signature2 = sha1(htmlspecialchars_decode($rawData) . $sessionKey);
        // 验证签名
        if ($signature2 !== $signature) {
            return json("验签失败");
        }
        // 获取解密后的数据
        $pc      = new \WXBizDataCrypt($appid, $sessionKey);
        $errCode = $pc->decryptData($encryptedData, $iv, $data);
        if ($errCode == 0) {
            return $res;
        } else {
            return $errCode;
        }
    }

}
