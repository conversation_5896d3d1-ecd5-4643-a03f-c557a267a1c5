<?php

namespace common\base\models;

use common\models\ResumeSkill;

class BaseResumeSkill extends ResumeSkill
{
    const LIMIT_NUM = 5;

    public function rules()
    {
        return [
            [
                [
                    'add_time',
                    'update_time',
                    'status',
                    'resume_id',
                    'member_id',
                    'skill_id',
                    'degree_type',
                ],
                'safe',
            ],
        ];
    }


    /**
     * 获取正常数据的条数
     * @param $memberId
     * @return bool|int|string|null
     */
    public static function getRecordAmount($memberId)
    {
        return self::find()
            ->where([
                'member_id' => $memberId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->count();
    }


}