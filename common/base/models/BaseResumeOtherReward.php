<?php

namespace common\base\models;

use common\models\ResumeOtherReward;

class BaseResumeOtherReward extends ResumeOtherReward
{
    const LIMIT_NUM = 5;

    public function rules()
    {
        return [
            [
                [
                    'add_time',
                    'update_time',
                    'obtain_date',
                    'status',
                    'resume_id',
                    'member_id',
                ],
                'safe',
            ],
            [
                ['name'],
                'string',
                'max' => 128,
            ],
            [
                [
                    'level',
                    'role',
                ],
                'string',
                'max' => 32,
            ],
        ];
    }

    /**
     * 获取正常数据的条数
     * @param $memberId
     * @return bool|int|string|null
     */
    public static function getRecordAmount($memberId)
    {
        return self::find()
            ->where([
                'member_id' => $memberId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->count();
    }

}