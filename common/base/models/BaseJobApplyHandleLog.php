<?php

namespace common\base\models;

use common\models\JobApplyHandleLog;
use Yii;
use yii\base\Exception;
use yii\db\ActiveRecord;

class BaseJobApplyHandleLog extends JobApplyHandleLog
{
    const TYPE_THROUGH_FIRST            = 1;//通过初筛
    const TYPE_SEND_INVITATION          = 2;//邀请面试
    const TYPE_INAPPROPRIATE            = 3;//不合适
    const TYPE_INAPPROPRIATE_REVOCATION = 4;//撤销不合适
    const TYPE_ENTRY                    = 5;//已入职
    const TYPE_ENTRY_NO                 = 6;//未入职
    const TYPE_ENTRY_REVOCATION         = 7;//撤销已入职
    const TYPE_ENTRY_NO_REVOCATION      = 8;//撤销未入职
    const TYPE_HANDLE_WAIT              = 9;//待处理
    const TYPE_EMPLOYED_WAIT            = 10;//待录用
    const TYPE_EMPLOYED_REVOCATION      = 11;//撤销录用
    const TYPE_BLOCK_CALL               = 12;//未接通
    const TYPE_INTENTION_NO             = 13;//无意向
    const TYPE_INTERVIEW_NO             = 14;//未到面
    const TYPE_EMPLOYED                 = 15;//已录用
    const TYPE_DELIVERY                 = 20;//投递
    const TYPE_VIEW                     = 21;//查看
    const TYPE_COLLECTION_JOB           = 22;//收藏职位
    const TYPE_CONTENT                  = 23;//评论
    //之前的操作，都是针对投递的，这里是对简历的处理，区分开一下，方便后续统计维护
    const TYPE_INVITE_APPLY             = 24;//邀请投递
    const TYPE_DOWNLOAD_RESUME          = 25;//下载简历
    const TYPE_COLLECTION_RESUME        = 26;//收藏简历
    const TYPE_CANCEL_COLLECTION_RESUME = 27;//取消收藏简历
    const TYPE_VIEW_RESUME              = 28;//查看简历
    const TYPE_COMMENT_RESUME           = 29;//评论简历
    const TYPE_SHARE_RESUME             = 30;//分享简历
    const TYPE_PRINT_RESUME             = 31;//打印简历

    const HANDLER_TYPE_PERSON  = 1;
    const HANDLER_TYPE_COMPANY = 2;

    const STATUS_LIST = [
        self::TYPE_THROUGH_FIRST            => '通过初筛',
        self::TYPE_SEND_INVITATION          => '邀请面试',
        self::TYPE_INAPPROPRIATE            => '不合适',
        self::TYPE_INAPPROPRIATE_REVOCATION => '撤销不合适',
        self::TYPE_ENTRY                    => '已入职',
        self::TYPE_ENTRY_NO                 => '未入职',
        self::TYPE_ENTRY_REVOCATION         => '撤销已入职',
        self::TYPE_ENTRY_NO_REVOCATION      => '撤销未入职',
        self::TYPE_HANDLE_WAIT              => '待处理',
        self::TYPE_EMPLOYED                 => '已录用',
        self::TYPE_EMPLOYED_WAIT            => '待录用',
        self::TYPE_EMPLOYED_REVOCATION      => '撤销录用',
        self::TYPE_BLOCK_CALL               => '未接通',
        self::TYPE_INTENTION_NO             => '无意向',
        self::TYPE_INTERVIEW_NO             => '未到面',
        self::TYPE_COLLECTION_JOB           => '收藏职位',
        self::TYPE_DELIVERY                 => '投递',
        self::TYPE_VIEW                     => '查看',
        self::TYPE_CONTENT                  => '评论',
    ];

    // todo 企业返回流程信息
    const STATUS_RETURN_LIST = [
        self::TYPE_THROUGH_FIRST            => '通过了投递初筛',
        self::TYPE_SEND_INVITATION          => '邀请该用户面试',
        self::TYPE_INAPPROPRIATE            => '设置不合适',
        self::TYPE_INAPPROPRIATE_REVOCATION => '撤销不合适',
        self::TYPE_ENTRY                    => '设置已入职',
        self::TYPE_ENTRY_NO                 => '设置未入职',
        self::TYPE_ENTRY_REVOCATION         => '撤销已入职',
        self::TYPE_ENTRY_NO_REVOCATION      => '撤销未入职',
        self::TYPE_HANDLE_WAIT              => '设置待处理',
        self::TYPE_EMPLOYED                 => '设置已录用',
        self::TYPE_EMPLOYED_WAIT            => '设置待录用',
        self::TYPE_EMPLOYED_REVOCATION      => '撤销录用',
        self::TYPE_BLOCK_CALL               => '设置未接通',
        self::TYPE_INTENTION_NO             => '设置无意向',
        self::TYPE_INTERVIEW_NO             => '设置未到面',
        self::TYPE_COLLECTION_JOB           => '收藏职位',
        self::TYPE_DELIVERY                 => '投递了该职位',
        self::TYPE_VIEW                     => '查看该投递',
        self::TYPE_CONTENT                  => '发表了评论',
    ];

    const STATUS_RETURN_CHECK_LIST = [
        self::TYPE_THROUGH_FIRST,
        self::TYPE_SEND_INVITATION,
        self::TYPE_INAPPROPRIATE,
        self::TYPE_INAPPROPRIATE_REVOCATION,
        self::TYPE_ENTRY,
        self::TYPE_ENTRY_NO,
        self::TYPE_ENTRY_REVOCATION,
        self::TYPE_ENTRY_NO_REVOCATION,
        self::TYPE_HANDLE_WAIT,
        self::TYPE_EMPLOYED,
        self::TYPE_EMPLOYED_WAIT,
        self::TYPE_EMPLOYED_REVOCATION,
        self::TYPE_BLOCK_CALL,
        self::TYPE_INTENTION_NO,
        self::TYPE_INTERVIEW_NO,
        self::TYPE_COLLECTION_JOB,
        self::TYPE_DELIVERY,
        self::TYPE_VIEW,
        self::TYPE_CONTENT,
    ];

    // 用户返回流程信息
    const STATUS_PERSON_LIST = [
        self::TYPE_THROUGH_FIRST   => '通过初筛',
        self::TYPE_SEND_INVITATION => '发出邀请面试',
        self::TYPE_HANDLE_WAIT     => '移至待处理',
        self::TYPE_EMPLOYED        => '录用了您',
        self::TYPE_COLLECTION_JOB  => '收藏了职位',
        self::TYPE_DELIVERY        => '投递',
        self::TYPE_VIEW            => '查看了您',
        self::TYPE_CONTENT         => '评论',
    ];

    const STATUS_PERSON_CHECK_LIST = [
        self::TYPE_THROUGH_FIRST,
        self::TYPE_SEND_INVITATION,
        self::TYPE_HANDLE_WAIT,
        self::TYPE_EMPLOYED,
        self::TYPE_COLLECTION_JOB,
        self::TYPE_DELIVERY,
        self::TYPE_VIEW,
        self::TYPE_CONTENT,
    ];

    //投递类型
    /** 站外投递 */
    const DELIVERY_TYPE_OUTER = 1;
    /** 站内投递 */
    const DELIVERY_TYPE_OUTSIDE = 2;
    const DELIVERY_TYPE         = [
        self::DELIVERY_TYPE_OUTER,
        self::DELIVERY_TYPE_OUTSIDE,
    ];
    const DELIVERY_TYPE_NAME    = [
        self::DELIVERY_TYPE_OUTER   => '站外投递',
        self::DELIVERY_TYPE_OUTSIDE => '站内投递',
    ];

    /**
     * 创建面试记录日志
     * @param        $jobApplyId
     * @param        $handlerType
     * @param        $handlerName
     * @param        $handleType
     * @param string $title
     * @param string $content
     * @throws Exception
     */
    public static function createJobApplyHandleLogInfo(
        $jobApplyId,
        $handlerType,
        $handlerName,
        $handleType,
        string $title = '',
        string $content = ''
    ) {
        //这里都能拿到job_apply_id，查询到resume_id和company_id，保存，用于管理后台简历处理记录处显示
        $self = new self();
        if (!empty($jobApplyId)) {
            $jobApplyInfo     = BaseJobApply::findOne(['id' => $jobApplyId]);
            $self->resume_id  = $jobApplyInfo->resume_id;
            $self->company_id = $jobApplyInfo->company_id;
        }

        $self->job_apply_id = $jobApplyId;
        $self->handler_type = $handlerType;
        $self->handler_name = $handlerName;
        $self->handle_type  = $handleType;
        $self->title        = $title;
        $self->content      = $content;
        $self->handle_id    = Yii::$app->user->id;
        if (!$self->save()) {
            throw new Exception($self->getFirstErrorsMessage());
        }
    }

    /**
     * @param $where
     * @param $select
     * @return array|ActiveRecord[]
     */
    public static function selectList($where, $select): array
    {
        return self::find()
            ->where($where)
            ->select($select)
            ->asArray()
            ->all();
    }

    /**
     * 职位投递的流程
     * @return array|ActiveRecord[]
     */
    public static function jobApplyHandleLog($jobApplyId): array
    {
        $where  = ['job_apply_id' => $jobApplyId];
        $select = [
            'handle_type',
            'add_time',
            'id',
        ];

        $logList = [
            [
                'name'        => self::STATUS_LIST[self::TYPE_DELIVERY],
                'handle_type' => self::TYPE_DELIVERY,
                'time'        => '',
            ],
            [
                'name'        => self::STATUS_LIST[self::TYPE_VIEW],
                'handle_type' => self::TYPE_VIEW,
                'time'        => '',
            ],
            [
                'name'        => self::STATUS_LIST[self::TYPE_THROUGH_FIRST],
                'handle_type' => self::TYPE_THROUGH_FIRST,
                'time'        => '',
            ],
            [
                'name'        => '面试',
                'handle_type' => self::TYPE_SEND_INVITATION,
                'time'        => '',
            ],
        ];

        $jobApplyHandleList = BaseJobApplyHandleLog::find()
            ->where($where)
            ->select($select)
            ->orderBy('add_time desc')
            ->asArray()
            ->all();

        foreach ($logList as $k => $log) {
            foreach ($jobApplyHandleList as $list) {
                if ($log['handle_type'] == $list['handle_type']) {
                    $logList[$k]['time'] = $list['add_time'];
                }
            }
        }

        return $logList;
    }

    /**
     * 获取职位投递的流程（新接口）
     * @return array|ActiveRecord[]
     */
    public static function getJobApplyHandleLog($jobApplyId, $checkType, $handleId): array
    {
        if ($checkType == BaseJobApplyHandleLog::HANDLER_TYPE_COMPANY) {
            $checkList = BaseJobApplyHandleLog::STATUS_RETURN_CHECK_LIST;
        } else {
            $checkList = BaseJobApplyHandleLog::STATUS_PERSON_CHECK_LIST;
        }

        $select = [
            'id',
            'add_time',
            'job_apply_id',
            'handler_type',
            'handler_name',
            'handle_type',
            'title',
            'content',
            'handle_id',
        ];

        $query = BaseJobApplyHandleLog::find()
            ->select($select)
            ->where([
                'handle_type'  => $checkList,
                'job_apply_id' => $jobApplyId,
            ]);

        $query->andFilterCompare('handle_id', $handleId);

        return $query->orderBy(' add_time asc')
            ->asArray()
            ->all();
    }

    /**
     * 该方法用于记录单位端操作人才简历端记录
     * @param $params
     * @return int
     * @throws Exception
     */
    public static function saveCompanyHandleLog($params)
    {
        //$companyName = BaseCompany::findOneVal(['id' => $params['companyId']], 'full_name');
        $memberId    = Yii::$app->user->id;
        $handlerName = BaseCompanyMemberInfo::findOneVal(['member_id' => Yii::$app->user->id], 'contact');

        $self               = new self();
        $self->handler_type = self::HANDLER_TYPE_COMPANY;
        $self->company_id   = $params['companyId'];
        $self->resume_id    = $params['resumeId'];
        $self->handler_name = $handlerName;
        $self->handle_type  = $params['type'];
        $self->title        = $params['content'];
        $self->content      = $params['content'];
        $self->handle_id    = $memberId;
        if (!$self->save()) {
            throw new Exception($self->getFirstErrorsMessage());
        }

        return $self->id; //返回id供后续处理
    }

    /**
     * 这里会比较麻烦，需要对各个投递操作，做详细对日志记录
     * @param $applyId
     * @return string
     */
    public static function getHandleLogContent($status)
    {
        //        $applyInfo = BaseJobApply::findOne($applyId);
        switch ($status) {
            case self::TYPE_HANDLE_WAIT://待处理
            case self::TYPE_THROUGH_FIRST://通过初筛
            case self::TYPE_INAPPROPRIATE://不合适
            case self::TYPE_EMPLOYED://已录用
                $statusName = self::STATUS_LIST[$status];
                $text       = "将人才移入{$statusName}";
                break;
            case self::TYPE_SEND_INVITATION:
                //邀请面试
                $statusName = self::STATUS_LIST[$status];
                $text       = "邀请人才参加面试";
                break;
            case self::TYPE_BLOCK_CALL://未接通
            case self::TYPE_INTENTION_NO://无意向
            case self::TYPE_ENTRY://已入职
            case self::TYPE_ENTRY_NO://未入职
            case self::TYPE_INTERVIEW_NO://未到面
            case self::TYPE_EMPLOYED_WAIT://待录面
                $statusName = self::STATUS_LIST[$status];
                $text       = "操作该人才：{$statusName}";
                break;
            case self::TYPE_INAPPROPRIATE_REVOCATION:
            case self::TYPE_EMPLOYED_REVOCATION:
            case self::TYPE_ENTRY_REVOCATION:
            case self::TYPE_ENTRY_NO_REVOCATION:
                $statusName = self::STATUS_LIST[$status];
                $text       = "操作该人才：{$statusName}；人才已移至'待处理'状态";
                break;
        }

        return $text;
    }
}