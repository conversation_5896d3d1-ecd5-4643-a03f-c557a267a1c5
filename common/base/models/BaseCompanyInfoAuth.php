<?php

namespace common\base\models;

use common\models\CompanyInfoAuth;

class BaseCompanyInfoAuth extends CompanyInfoAuth
{
    // 审核未提交
    const AUDIT_STATUS_NO = 0;
    // 复审通过
    const AUDIT_STATUS_PASS = 1;
    // 初审通过 有填经办人信息，但是经办人信息有误
    const AUDIT_STATUS_SECOND_PASS_IDENTITY_REJECT = 6;
    // 初审通过 等待经办人认证
    const AUDIT_STATUS_IDENTITY_AUTH = 7;
    // 初审拒绝
    const AUDIT_STATUS_SECOND_REJECT = -8;
    // 等待初审
    const AUDIT_STATUS_SECOND_WAIT = 8;
    // 复审拒绝
    const AUDIT_STATUS_FIRST_REJECT = -9;
    // 等待复审
    const AUDIT_STATUS_FIRST_WAIT = 9;

    //审核状态列表
    const AUDIT_STATUS_LIST = [
        self::AUDIT_STATUS_FIRST_WAIT                  => '待终审',
        self::AUDIT_STATUS_SECOND_WAIT                 => '待初审',
        self::AUDIT_STATUS_IDENTITY_AUTH               => '初审通过（经办人信息有误）',
        self::AUDIT_STATUS_SECOND_PASS_IDENTITY_REJECT => '初审通过（等待经办人认证）',
        self::AUDIT_STATUS_SECOND_REJECT               => '初审拒绝',
        self::AUDIT_STATUS_FIRST_REJECT                => '终审拒绝',
    ];

    // 未提交完基本信息
    const PHASE_NO = 0;
    // 提交完基本信息进入阶段1
    const PHASE_ONE = 1;
    // 提交完审核信息进入阶段2
    const PHASE_TWO = 2;
    // 提交经办人信息或等待终审中可进入第三阶段
    const PHASE_THREE = 3;

    const ORDER_BY_DESC = 1;
    const ORDER_BY_ASC  = 2;

}