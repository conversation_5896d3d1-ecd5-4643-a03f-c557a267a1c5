<?php

namespace common\base\models;

use common\models\ResumeJobFootprint;
use common\base\models\BaseResumeEquity;
use common\base\models\BaseResumeEquitySetting;
use yii\helpers\Url;

class BaseResumeJobFootprint extends ResumeJobFootprint
{
    public static function create($resumeId, $jobId)
    {
        $date = CUR_DATE;
        // 首先找到这个求职者当天有没有这条数据
        $model = self::findOne([
            'resume_id' => $resumeId,
            'date'      => $date,
            'job_id'    => $jobId,
        ]);

        if (!$model) {
            $model            = new self();
            $model->resume_id = $resumeId;
            $model->date      = $date;
            $model->job_id    = $jobId;
        }

        $model->last_time = CUR_DATETIME;

        $model->save();

        return $model->id;
    }

    public static function getListForResume($params, $resumeId)
    {
        // 这里做去检查一下求职者是否vip会员(vip才可以查看,非vip不给看)
        // 校验是否拥有浏览足迹权益
        if (BaseResumeEquity::checkEquity($resumeId, BaseResumeEquitySetting::ID_FOOTPRINT) === false) {
            $data = [
                'list'  => [],
                'pages' => [
                    'size'  => (int)$params['pageSize'] ?: \Yii::$app->params['defaultPageSize'],
                    'total' => (int)0,
                    'page'  => (int)$params['page'],
                ],
            ];

            return $data;
        }
        // 找到90天前的日期
        $date     = date('Y-m-d', strtotime('-90 days'));
        $memberId = BaseResume::findOneVal(['id' => $resumeId], 'member_id');

        $query = self::find()
            ->select('job_id,date')
            ->where(['resume_id' => $resumeId])
            ->andWhere([
                '>=',
                'date',
                $date,
            ]);

        $paramsDate = $params['date'];
        $query->andFilterCompare('date', $paramsDate);

        $count    = $query->count();
        $pageSize = $params['pageSize'] ?: \Yii::$app->params['defaultPageSize'];

        $pages = self::setPage($count, $params['page'], $pageSize);

        $list = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('date desc,id desc')
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            // 找到职位相关的信息
            $jobInfo = BaseJob::find()
                ->select('name,full_name,refresh_date as publishDate,job.city_id,job.status,company_id,is_cooperation,apply_type,job.status,job.is_show')
                ->innerJoin(['c' => BaseCompany::tableName()], 'c.id=company_id')
                ->where(['job.id' => $item['job_id']])
                ->asArray()
                ->one();

            $areaName = self::getAreaName($jobInfo['city_id']);

            $item['jobName']     = $jobInfo['name'];
            $item['companyName'] = $jobInfo['full_name'];
            $item['publishDate'] = $jobInfo['publishDate'];
            $item['areaName']    = $areaName;
            $item['applyStatus'] = BaseJobApply::checkJobApplyStatus($memberId, $item['job_id']);
            $item['url']         = Url::toRoute([
                'job/detail',
                'id' => $item['job_id'],
            ]);
            $item['companyUrl']  = Url::toRoute([
                'company/detail',
                'id' => $jobInfo['company_id'],
            ]);
            $applyTypeArr        = explode(',', $item['applyType']);
            if (in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr)) {
                $item['isEmailApply'] = true;
            } else {
                $item['isEmailApply'] = false;
            }

            // 默认不置灰
            $item['isGrey'] = '2';
            // 在这里做一个文本的逻辑判断,
            $txt = '申请';
            if ($item['applyStatus'] == BaseJob::JOB_APPLY_STATUS_YES) {
                $txt = '已申请';
            }

            // 如果是已下线
            if ($jobInfo['status'] == BaseJob::STATUS_OFFLINE) {
                $txt            = '停止招聘';
                $item['isGrey'] = '1';
            }

            // 如果是已删除
            if ($jobInfo['status'] == BaseJob::STATUS_DELETE || $jobInfo['is_show'] == BaseJob::IS_SHOW_NO) {
                $txt            = '已删除';
                $item['isGrey'] = '1';
            }
            // 灰色以后也不可以点击申请
            if ($item['isGrey'] == '1') {
                $item['applyStatus'] = BaseJob::JOB_APPLY_STATUS_YES;
            }
            $item['opTxt'] = $txt;
        }

        $data = [
            'list'  => $list,
            'pages' => [
                'size'  => (int)$pageSize,
                'total' => (int)$count,
                'page'  => (int)$params['page'],
            ],
        ];

        return $data;
    }

    public static function getAreaName($id)
    {
        $cityName = BaseArea::findOneVal(['id' => $id], 'name');
        if (in_array($id, BaseArea::CROWN_ID_LIST)) {
            return $cityName;
        }

        // 找到省份的name
        $provinceId = BaseArea::findOneVal(['id' => $id], 'parent_id');
        $province   = BaseArea::findOneVal(['id' => $provinceId], 'name');

        return $province . '-' . $cityName;
    }

}