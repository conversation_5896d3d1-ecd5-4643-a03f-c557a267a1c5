<?php
/**
 * create user：shannon
 * create time：2024/7/1 16:33
 */
namespace common\base\models;

use common\models\CompanyGroupRelation;

class BaseCompanyGroupRelation extends CompanyGroupRelation
{

    /**
     * 获取单位的群组信息
     * @param $companyId
     * @return array
     */
    public static function getGroupInfo($companyId)
    {
        $data = self::find()
            ->alias('cgr')
            ->select([
                'cgr.group_id',
                'cg.group_name',
            ])
            ->leftJoin(['cg' => BaseCompanyGroup::tableName()], 'cg.id=cgr.group_id')
            ->where(['cgr.company_id' => $companyId])
            ->asArray()
            ->all();

        return [
            'groupNameArray' => array_column($data, 'group_name'),
            'groupIdArray'   => array_column($data, 'group_id'),
        ];
    }
}