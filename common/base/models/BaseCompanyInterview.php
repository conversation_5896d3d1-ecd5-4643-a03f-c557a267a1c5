<?php

namespace common\base\models;

use common\models\CompanyInterview;
use frontendPc\models\Resume;
use yii\base\Exception;
use yii\db\ActiveRecord;

class BaseCompanyInterview extends CompanyInterview
{
    const ORDER_BY_DESC = 1;
    const ORDER_BY_ASC  = 2;

    /**
     * 根据条件查询单挑记录
     * @param $where
     * @param $select
     * @param $andWhere
     * @param $orderBy
     * @return array|ActiveRecord|null
     */
    public static function selectInfo($where, $select, $andWhere, $orderBy)
    {
        return self::find()
            ->where($where)
            ->andWhere($andWhere)
            ->select($select)
            ->orderBy($orderBy)
            ->asArray()
            ->one();
    }

    /**
     * 新增面试邀请信息
     * @param $request
     * @throws Exception
     * @throws \Exception
     */
    public static function createCompanyInterviewInfo($request)
    {
        $self                 = new self();
        $self->job_apply_id   = $request['job_apply_id'];
        $self->job_name       = $request['job_name'];
        $self->contact        = $request['contact'];
        $self->telephone      = $request['telephone'];
        $self->address        = $request['address'];
        $self->interview_time = $request['interview_time'];
        $self->content        = $request['content'];
        if (!$self->save()) {
            throw new Exception($self->getFirstErrorsMessage());
        }
        //只要产生了面试邀请，就更新简历统计表的邀请面试记录的总条数
        $resumeId = BaseJobApply::findOneVal(['id' => $request['job_apply_id']], 'resume_id');
        BaseResume::updateInterviewRecordAmount($resumeId, 1);
    }

    /**
     * 获取用户已邀面次数
     * @param $where
     * @return bool|int|string|null
     */
    public static function getInterviewAmount($where)
    {
        return intval(self::find()
            ->where($where)
            ->asArray()
            ->count());
    }
}