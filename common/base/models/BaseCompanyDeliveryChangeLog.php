<?php

namespace common\base\models;

use common\models\CompanyDeliveryChangeLog;
use yii\db\Exception;
use Yii;

class BaseCompanyDeliveryChangeLog extends CompanyDeliveryChangeLog
{
    //操作来源
    const USER_TYPE_ADMIN   = 1;
    const USER_TYPE_COMPANY = 2;
    const USER_TYPE_NAME    = [
        self::USER_TYPE_ADMIN   => '运营后台',
        self::USER_TYPE_COMPANY => '单位端',
    ];

    /**
     * 插入单位操作日志
     * @param int   $companyId 单位ID
     * @param array $params    修改后内容
     * @param int   $userType  操作来源
     * @return bool
     * @throws Exception
     */
    public static function insertLog($companyId, $params, $userType = self::USER_TYPE_ADMIN)
    {
        //获取单位信息
        $companyInfo = BaseCompany::findOne($companyId);
        $model       = new self();
        switch ($userType) {
            case self::USER_TYPE_ADMIN:
                //获取当前登录用户信息
                $adminId = Yii::$app->user->id;
                if ($adminId <= 0) {
                    throw new Exception('用户不存在或退出登录');
                }
                $adminInfo = BaseAdmin::findOne($adminId);
                $user_id   = $adminId;
                $user_name = $adminInfo->name;
                break;
            case self::USER_TYPE_COMPANY:
                $user_id   = $companyInfo->id;
                $user_name = $companyInfo->full_name;
                break;
            default:
                throw new Exception('当前端不支持日志录入');
        }
        $model->user_id            = $user_id;
        $model->user_name          = $user_name;
        $model->description_before = $companyInfo->delivery_type;
        $model->description_after  = $params['type'];
        $model->remark             = $params['remark'];
        $model->user_type          = $userType;
        $model->company_id         = $companyInfo->id;
        $model->company_name       = $companyInfo->full_name;
        $model->add_time           = CUR_DATETIME;
        $res                       = $model->save();
        if ($res) {
            return $res;
        } else {
            return false;//记录失败日志，不影响前逻辑
        }
    }
}