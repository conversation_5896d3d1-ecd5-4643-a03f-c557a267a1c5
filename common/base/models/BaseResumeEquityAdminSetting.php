<?php
/**
 * create user：shannon
 * create time：2024/4/2 14:04
 */
namespace common\base\models;

use common\models\ResumeEquityAdminSetting;

class BaseResumeEquityAdminSetting extends ResumeEquityAdminSetting
{
    /** 定义审核状态 */
    const STATUS_SETTING_WAIT   = 1; //配置待审核
    const STATUS_REFUND_WAIT    = 2; //退款待审核
    const STATUS_PASS           = 3; //审核通过
    const STATUS_SETTING_REJECT = 4; //配置审核驳回
    const STATUS_REFUND_REJECT  = 5; //退款审核驳回

    /** 定义审核状态文案 */
    const STATUS_SETTING_WAIT_TEXT   = '配置待审核';
    const STATUS_REFUND_WAIT_TEXT    = '退款待审核';
    const STATUS_PASS_TEXT           = '审核通过';
    const STATUS_SETTING_REJECT_TEXT = '配置审核驳回';
    const STATUS_REFUND_REJECT_TEXT  = '退款审核驳回';
    const STATUS_AUDIT_LIST          = [
        self::STATUS_SETTING_WAIT   => self::STATUS_SETTING_WAIT_TEXT,
        self::STATUS_REFUND_WAIT    => self::STATUS_REFUND_WAIT_TEXT,
        self::STATUS_PASS           => self::STATUS_PASS_TEXT,
        self::STATUS_SETTING_REJECT => self::STATUS_SETTING_REJECT_TEXT,
        self::STATUS_REFUND_REJECT  => self::STATUS_REFUND_REJECT_TEXT,
    ];

    /** 删除状态定义 */
    const IS_DELETE_YES = 1;
    const IS_DELETE_NO  = 2;

    /** 是否退款状态 */
    const IS_REFUND_YES      = 1;
    const IS_REFUND_NO       = 2;
    const IS_REFUND_YES_TEXT = '是';
    const IS_REFUND_NO_TEXT  = '否';
    const IS_REFUND_LIST     = [
        self::IS_REFUND_YES => self::IS_REFUND_YES_TEXT,
        self::IS_REFUND_NO  => self::IS_REFUND_NO_TEXT,
    ];
}