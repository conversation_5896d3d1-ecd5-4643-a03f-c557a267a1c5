<?php

namespace common\base\models;

use common\libs\Cache;
use \common\models\ResumeResearchDirection;
use common\service\resume\ResumeCacheService;
use frontendPc\models\ResumeComplete;
use yii\base\Exception;

class BaseResumeResearchDirection extends ResumeResearchDirection
{
    const LIMIT_NUM = 5;

    /**
     * 获取研究方向信息（单条，只能在简历第二步调用，因为此时只有一条数据）
     * @param $memberId
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function getResearchDirectionInfo($memberId)
    {
        return self::find()
            ->where([
                'member_id' => $memberId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->select([
                'content as researchDirection',
            ])
            ->asArray()
            ->one();
    }

    /**
     * 保存研究方向
     * @param $data
     * @throws Exception
     */
    public static function saveInfo($data)
    {
        $memberId = $data['memberId'];
        $resumeId = Cache::get(Cache::PC_ALL_RESUME_ID_KEY . ':' . $memberId);

        if (empty($memberId)) {
            throw new Exception('用户登录状态错误');
        }
        if (empty($resumeId)) {
            throw new Exception('所属简历不存在');
        }
        $info = self::findOne(['member_id' => $memberId]);
        if (empty($info) && strlen($data['researchDirection']) > 0) {
            //插入
            $info            = new self();
            $info->resume_id = $resumeId;
            $info->member_id = $memberId;
            $info->content   = $data['researchDirection'];
            if (!$info->save()) {
                throw new Exception($info->getFirstErrorsMessage());
            }
        } elseif (!empty($info)) {
            //存在记录了，如果清空内容，删除数据，否则更新数据
            if (strlen($data['researchDirection']) > 0) {
                if (mb_strlen($data['researchDirection']) > 500) {
                    throw new Exception('研究方向字数超出限制');
                }
                $info->content = $data['researchDirection'];
                if (!$info->save()) {
                    throw new Exception($info->getFirstErrorsMessage());
                }
            } else {
                self::deleteAll(['member_id' => $memberId]);
            }
        }
        ResumeComplete::updateResumeCompleteInfo($memberId);
        //新增操作日志
        $log_data = [
            'content' => '保存简历研究方向信息，memberId：' . $memberId,
        ];
        // 写日志
        BaseMemberActionLog::log($log_data);
    }

    /**
     * 获取正常数据的条数
     * @param $memberId
     * @return bool|int|string|null
     */
    public static function getRecordAmount($memberId)
    {
        return self::find()
            ->where([
                'member_id' => $memberId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->count();
    }

    /**
     * 获取研究方向
     * @param $resumeId
     */
    public static function getInfo($resumeId)
    {
        //读取缓存
        return ResumeCacheService::getResearchDirection($resumeId);

        //        return self::findOne([
        //            'resume_id' => $resumeId,
        //            'status'    => self::STATUS_ACTIVE,
        //        ]);
    }
}