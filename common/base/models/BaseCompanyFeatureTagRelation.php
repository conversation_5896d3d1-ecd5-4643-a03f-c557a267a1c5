<?php

namespace common\base\models;

use common\models\CompanyFeatureTagRelation;

class BaseCompanyFeatureTagRelation extends CompanyFeatureTagRelation
{
    /**
     * 获取符合标签内容等单位id
     * @param array|string $featuredTagIds 对应特色标签
     * @return array
     */
    public static function searchCompanyByFeaturedTagIds($featuredTagIds)
    {
        if (empty($featuredTagIds)) {
            return [];
        }
        if (!is_array($featuredTagIds)) {
            $featuredTagIds = explode(',', $featuredTagIds);
        }

        return self::find()
            ->alias('tr')
            ->select('company_id')
            ->filterWhere([
                'in',
                'feature_tag_id',
                $featuredTagIds,
            ])
            ->groupBy('company_id')
            ->column(); // 获取结果为数组
    }
}