<?php

namespace common\base\models;

use common\models\JobApplyLimitConfig;

class BaseJobApplyLimitConfig extends JobApplyLimitConfig
{
    const TYPE_BAN   = 1;
    const TYPE_LOWER = 2;

    const COMPANY_TYPE_ALL_COOPERATION = 1;
    const COMPANY_TYPE_ID              = 2;
    const COMPANY_TYPE_TAG             = 3;

    const RESUME_TYPE_ID  = 1;
    const RESUME_TYPE_TAG = 2;

    const MESSAGE_TYPE_ONE = 1;
    const MESSAGE_TYPE_TWO = 2;

    const TYPE_LIST = [
        self::TYPE_BAN   => '禁止投递',
        self::TYPE_LOWER => '降权投递',
    ];

    const COMPANY_TYPE_LIST = [
        self::COMPANY_TYPE_ALL_COOPERATION => '全部合作单位',
        self::COMPANY_TYPE_ID              => '指定单位',
        // self::COMPANY_TYPE_TAG             => '指定单位标签',
    ];

    const RESUME_TYPE_LIST = [
        self::RESUME_TYPE_ID  => '指定人才',
        self::RESUME_TYPE_TAG => '指定人才标签',
    ];

    const MESSAGE_TYPE_LIST = [
        self::MESSAGE_TYPE_ONE => '对不起，您暂不符合招聘要求，建议尝试其他机会！',
        self::MESSAGE_TYPE_TWO => '系统校验到您的简历质量不佳，须进行完善。如果完善后依旧无法正常投递，请联系客服',
    ];

    const MESSGAE_LOWER = '你当前已达该单位投递次数上限 ！请等待或尝试其他机会';

}