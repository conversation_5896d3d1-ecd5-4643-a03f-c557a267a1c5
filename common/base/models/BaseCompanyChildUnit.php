<?php

namespace common\base\models;

use common\models\CompanyChildUnit;
use yii\base\Exception;

class BaseCompanyChildUnit extends CompanyChildUnit
{
    /**
     * 获取单位下的二级院校
     * @param $companyId
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getList($companyId)
    {
        return self::find()
            ->where([
                'company_id' => $companyId,
                'status'     => self::STATUS_ACTIVE,
            ])
            ->select([
                'name',
                'contact',
                'telephone',
                'fax',
            ])
            ->asArray()
            ->all();
    }

    /**
     * 单位二级院校删除
     * @param $id
     */
    public static function setChildUnitDelete($id)
    {
        $model = self::findOne(['id' => $id]);

        $model->status = self::STATUS_DELETE;
        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
    }
}