<?php

namespace common\base\models;

use common\models\ResumeEquityPackage;

class BaseResumeEquityPackage extends ResumeEquityPackage
{
    // 未过期
    const STATUS_EXPIRE = 0;
    // 已过期
    const STATUS_EXPIRED = 1;

    /**
     * 扣除对应权益的资源数量(按照消耗最先过期的资源)
     * @param $resumeId
     * @param $equityId
     */
    public static function deductEquityAmount($resumeId, $equityId)
    {
        //先获取最先过期的权益数据
        $equityPackage = BaseResumeEquityPackage::find()
            ->select([
                'id',
                'package_category_id',
                'resume_id',
                'equity_id',
                'amount',
                'expire_time',
            ])
            ->where([
                'resume_id'     => $resumeId,
                'equity_id'     => $equityId,
                'expire_status' => BaseResumeEquityPackage::STATUS_EXPIRE,
            ])
            ->andWhere([
                '>',
                'amount',
                0,
            ])
            ->orderBy([
                'expire_time' => SORT_ASC,
                'begin_time'  => SORT_ASC,
            ])
            ->asArray()
            ->one();
        //如果没有数据，直接返回，扣除失败
        if (!$equityPackage) {
            return false;
        }
        $before_amount = $equityPackage['amount'];
        //扣除数量,更新数据
        $res = BaseResumeEquityPackage::updateAllCounters(['amount' => -1], ['id' => $equityPackage['id']]);
        //成功写日志，失败不写日志
        if ($res) {
            $after_amount = $before_amount - 1;

            return [
                'before_amount'            => $before_amount,
                'after_amount'             => $after_amount,
                'package_category_id'      => $equityPackage['package_category_id'],
                'resume_equity_package_id' => $equityPackage['id'],
            ];
        } else {
            return false;
        }
    }

    /**
     * 获取权益列表
     * @param null $resumeId
     * @param null $equityIds
     * @param null $expireStatus
     */
    public static function getEquityList($resumeId = null, $equityIds = null, $expireStatus = null, $packageId = null)
    {
        $query = self::find()
            ->alias('e')
            ->select([
                'e.add_time',
                'e.begin_time',
                'e.expire_time',
                'e.expire_status',
                'DATE_FORMAT(e.add_time, "%Y-%m-%d") AS add_date',
                'DATE_FORMAT(e.expire_time, "%Y-%m-%d") AS expire_date',
                'e.equity_id',
                'e.resume_id',
                'e.package_category_id',
                'e.amount',
                'es.name',
            ])
            ->leftJoin(['es' => BaseResumeEquitySetting::tableName()], 'es.id = e.equity_id')
            ->andFilterCompare('e.resume_id', $resumeId)
            ->andFilterCompare('e.expire_status', $expireStatus)
            ->andFilterCompare('e.package_category_id', $packageId);
        if ($equityIds) {
            $query->andWhere([
                'e.equity_id' => $equityIds,
            ]);
        }

        $data = $query->orderBy('e.expire_time asc,e.begin_time asc,es.id asc')
            ->asArray()
            ->all();

        return $data;
    }

    /**
     * 通过组合分类获取组合列表id
     */
    public static function getPackageIdsByCid($resumeId)
    {
        $equity_ids = self::find()
            ->select('equity_id')
            ->where([
                'resume_id'           => $resumeId,
                'package_category_id' => BaseResumeEquityPackageCategorySetting::PACKAGE_EQUITY_CATEGORY_VIP_IDS,
                'expire_status'       => BaseResumeEquityPackage::STATUS_EXPIRE,
            ])
            ->column();

        return array_unique($equity_ids);
    }

    /**
     * 获取是否有某个套餐生效
     */
    public static function isPackageEffect($packageId, $resumeId)
    {
        return BaseResumeEquityPackage::find()
            ->where([
                'package_category_id' => $packageId,
                'resume_id'           => $resumeId,
                'expire_status'       => BaseResumeEquityPackage::STATUS_EXPIRE,
            ])
            ->exists();
    }

    /**
     * 获取是否有某个权益生效
     */
    public static function isEquityEffect($equityId, $resumeId)
    {
        return BaseResumeEquityPackage::find()
            ->where([
                'equity_id'     => $equityId,
                'resume_id'     => $resumeId,
                'expire_status' => BaseResumeEquityPackage::STATUS_EXPIRE,
            ])
            ->exists();
    }

    /**
     * 获取是否有某个权益生效
     */
    public static function equityEffectData($equityId, $resumeId)
    {
        return BaseResumeEquityPackage::find()
            ->where([
                'equity_id'     => $equityId,
                'resume_id'     => $resumeId,
                'expire_status' => BaseResumeEquityPackage::STATUS_EXPIRE,
            ])
            ->asArray()
            ->all();
    }

    /**
     * 获取某个分类权益包剩余时间
     * @param $cateId
     * @param $resumeId
     * @param $days 未来xx天过期
     * @return float|int
     */
    public static function getExpireDays($cateId, $resumeId, $days = 0)
    {
        $query = BaseResumeEquityPackage::find()
            ->select([
                'package_category_id as packageCategoryId',
                'expire_time as expireTime',
            ])
            ->where([
                'resume_id'           => $resumeId,
                'package_category_id' => $cateId,
                'expire_status'       => BaseResumeEquityPackage::STATUS_EXPIRE,
            ]);
        if ($days) {
            $expireTime = date('Y-m-d H:i:s', strtotime("+$days days"));
            $query->andWhere([
                '<',
                'expire_time',
                $expireTime,
            ]);
        }

        $expireRecord = $query->groupBy('package_category_id')
            ->asArray()
            ->one();
        if (!$expireRecord) {
            return '';
        }
        $daysNum = floor((strtotime($expireRecord['expireTime']) - time()) / 86400);
        if ($daysNum >= 1) {
            return $daysNum;
        }
        if ($daysNum == 0 && (strtotime($expireRecord['expireTime']) > time())) {
            return '今';
        }

        return '';
    }

    /**
     * 通过过期时间，计算剩余天数
     * @param $expireTime
     * @return false|float|int|string
     */
    public static function countResidueDaysByExpireTime($expireTime)
    {
        $expireDay = floor((strtotime($expireTime) - time()) / 86400);

        return $expireDay ?: 0;
    }

    /**
     * 通过过期时间，计算已过期天数
     * @param $expireTime
     * @return false|float|int
     */
    public static function countExpiredDays($expireTime)
    {
        $expiredDay = floor((time() - (strtotime($expireTime))) / 86400);

        return $expiredDay ?: 0;
    }

    /**
     * 获取过期召回天数，过期天数=100以内7的倍数时，才返回
     * @param $resumeId
     * @param $packageId
     * @return array
     */
    public static function getRecallBuyInfo($resumeId, $packageId = '')
    {
        //判断用户是否过期用户
        $vipType = BaseResume::findOneVal(['id' => $resumeId], 'vip_type');
        if ($vipType != BaseResume::VIP_TYPE_EXPIRE) {
            return [];
        }
        $query = BaseResumeEquityPackage::find()
            ->select([
                'expire_time as expireTime',
                'package_category_id as packageCategoryId',
            ])
            ->where([
                'resume_id'     => $resumeId,
                'expire_status' => BaseResumeEquityPackage::STATUS_EXPIRED,
            ]);
        $query->andFilterWhere(['package_category_id' => $packageId]);

        $packageInfo = $query->orderBy('expire_time desc')
            ->asArray()
            ->one();
        //没有记录
        if (empty($packageInfo)) {
            return [];
        }
        //计算过期了多少天
        $expiredDay = self::countExpiredDays($packageInfo['expireTime']);
        if ($expiredDay > 0 && $expiredDay < 100 && $expiredDay % 7 == 0) {
            return [
                'packageCategoryType' => BaseResumeEquityPackageCategorySetting::VIP_TYPE_TEXT_LIST[$packageInfo['packageCategoryId']],
                'expiredDay'          => $expiredDay,
            ];
        }

        return [];
    }

}