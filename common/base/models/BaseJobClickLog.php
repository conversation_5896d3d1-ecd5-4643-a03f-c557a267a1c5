<?php

namespace common\base\models;

use common\helpers\IpHelper;
use common\models\JobClickLog;
use queue\ClickJob;
use queue\Producer;
use Yii;

class BaseJobClickLog extends JobClickLog
{
    public static function create($jobId, $memberId)
    {
        if (!$_COOKIE[Yii::$app->params['userCookiesKey']] && PLATFORM != 'MINI') {
            // 过滤爬虫
            return false;
        }
        $data = [
            'type'        => ClickJob::TYPE_JOB,
            'mainId'      => $jobId,
            'ip'          => IpHelper::getIpInt(),
            'source'      => BaseMemberLoginForm::getSourceType(),
            'useragent'   => Yii::$app->request->headers['user-agent'] ?? '',
            'userCookies' => $_COOKIE[Yii::$app->params['userCookiesKey']] ?? '',
            'memberId'    => $memberId,
        ];

        Producer::click($data);
        // $model = new self();
        //
        // $model->job_id       = $jobId;
        // $model->source       = BaseMemberLoginForm::getSourceType();
        // $model->useragent    = Yii::$app->request->headers['user-agent'] ?? '';
        // $model->user_cookies = $_COOKIE[Yii::$app->params['userCookiesKey']] ?? '';
        // $model->ip           = IpHelper::getIpInt();
        // $model->member_id    = Yii::$app->user->id ?? 0;
        // if (!$model->save()) {
        //     return false;
        // }
        //
        // BaseJob::updateAllCounters(['click' => 1], ['id' => $jobId]);
    }

    public static function checkJobClickStatus($memberId, $jobId, $days)
    {
        $daysAgo = date('Y-m-d', strtotime("-$days days"));

        $info = self::find()
            ->where([
                'member_id' => $memberId,
                'job_id'    => $jobId,
            ])
            ->andWhere([
                '>=',
                'add_time',
                $daysAgo,
            ])
            ->select('id')
            ->asArray()
            ->one();
        if (!empty($info['id'])) {
            return true;
        } else {
            return false;
        }
    }
}
