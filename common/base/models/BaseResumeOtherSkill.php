<?php

namespace common\base\models;

use common\models\ResumeOtherSkill;

class BaseResumeOtherSkill extends ResumeOtherSkill
{
    const LIMIT_NUM = 5;


    /**
     * 获取正常数据的条数
     * @param $memberId
     * @return bool|int|string|null
     */
    public static function getRecordAmount($memberId)
    {
        return self::find()
            ->where([
                'member_id' => $memberId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->count();
    }
}