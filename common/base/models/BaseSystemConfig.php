<?php

namespace common\base\models;

use common\libs\Cache;
use common\models\SystemConfig;

class BaseSystemConfig extends SystemConfig
{

    const PLATFORM_TYPE_COMMON = 1; //全局
    const PLATFORM_TYPE_PC     = 2; //pc
    const PLATFORM_TYPE_H5     = 3; //h5

    const IS_SECRET_YES = 1; //是
    const IS_SECRET_NO  = 2; //否

    const IS_BUSINESS_YES = 1; //是
    const IS_BUSINESS_NO  = 2; //否

    const PLATFORM_TYPE_LIST = [
        self::PLATFORM_TYPE_COMMON => '全局',
        self::PLATFORM_TYPE_PC     => 'PC',
        self::PLATFORM_TYPE_H5     => 'H5',
    ];

    const IS_SECRET_LIST = [
        self::IS_SECRET_YES => '是',
        self::IS_SECRET_NO  => '否',
    ];

    const IS_BUSINESS_LIST = [
        self::IS_BUSINESS_YES => '是',
        self::IS_BUSINESS_NO  => '否',
    ];

    // 可以进入人才库的key
    const RESUME_LIBRARY_COMPLETE_MIN_KEY = 'resume_library_complete_min';

    // 每天单位邀约的key
    const RESUME_LIBRARY_COMPANY_INVITE_COUNT_DAY_KEY = 'resume_library_company_invite_count_day';

    // 简历库相关(精英简历下载点数的key)
    // 精英
    const RESUME_LIBRARY_DOWNLOAD_POINT_ELITE_KEY = 'resume_library_download_point_elite';
    // 优质
    const RESUME_LIBRARY_DOWNLOAD_POINT_HIGH_QUALITY_KEY = 'resume_library_download_point_high_quality';
    // 普通
    const RESUME_LIBRARY_DOWNLOAD_POINT_ORDINARY_KEY = 'resume_library_download_point_ordinary';

    //获取简历类型的名称
    const RESUME_TYPE_NAME_ELITE        = 1;
    const RESUME_TYPE_NAME_HIGH_QUALITY = 2;
    const RESUME_TYPE_NAME_ORDINARY     = 3;

    const RESUME_TYPE_TEXT_LIST = [
        self::RESUME_TYPE_NAME_ELITE        => '精英简历',
        self::RESUME_TYPE_NAME_HIGH_QUALITY => '优质简历',
        self::RESUME_TYPE_NAME_ORDINARY     => '普通简历',
    ];

    const RESUME_TYPE_EXPLAIN = [
        self::RESUME_TYPE_NAME_ELITE        => '有副高及以上职称的人才简历，或海归博士、紧缺专业博士人才的简历。',
        self::RESUME_TYPE_NAME_HIGH_QUALITY => '博士人才简历，或有中级职称、海外留学经历的硕士人才简历。',
        self::RESUME_TYPE_NAME_ORDINARY     => '除精英简历和优质简历以外的简历。',
    ];

    const RESUME_LIBRARY_LIST = [
        self::RESUME_LIBRARY_COMPLETE_MIN_KEY,
        self::RESUME_LIBRARY_COMPANY_INVITE_COUNT_DAY_KEY,
        self::RESUME_LIBRARY_DOWNLOAD_POINT_ELITE_KEY,
        self::RESUME_LIBRARY_DOWNLOAD_POINT_HIGH_QUALITY_KEY,
        self::RESUME_LIBRARY_DOWNLOAD_POINT_ORDINARY_KEY,
    ];

    // 这些是运营部门可以修改的配置
    // 首页专题展
    const PC_HOME_SUB_NAV_COLUMN_SPECIAL_KEY         = 'pc_home_sub_nav_column_special';
    const PC_HOME_SUB_NAV_COLUMN_CIA_KEY             = 'pc_home_sub_nav_column_cia';
    const PC_HOME_SUB_NAV_COLUMN_SERVICE_STATION_KEY = 'pc_home_sub_nav_column_service_station';
    const PC_HOME_HOT_TOPIC_KEY                      = 'pc_home_hot_topic';
    const PC_HOME_NAV_TALENT_TAG_KEY                 = 'pc_home_nav_talent_tag';
    const PC_SECOND_COLUMN_RESERVED_KEY              = 'pc_second_column_reserved';
    const PC_HOME_ACTIVITY_KEY                       = 'pc_home_activity';
    const H5_SPECIAL_SUBJECT_KEY                     = 'h5_special_subject';
    const H5_HOT_SEARCH_LIST_JOB_KEY                 = 'h5_hot_search_list_job';

    public static function getValue($name, $isCache = false)
    {
        if ($isCache) {
            $baseKey = Cache::ALL_SYSTEM_CONFIG_KEY . ':' . $name;
            $value   = Cache::get($baseKey);
            if ($value) {
                return $value;
            }
            $value = self::findOneVal(['name' => $name], 'value');

            if (!$value) {
                return '';
            }

            Cache::set($baseKey, $value);

            return $value;
        } else {
            return self::findOneVal(['name' => $name], 'value') ?: '';
        }
    }

    public static function setValue($name, $value)
    {
        $model = self::findOne(['name' => $name]);
        if (!$model) {
            throw new \Exception('配置不存在');
        }
        $model->value = $value;
        if (!$model->save()) {
            throw new \Exception($model->getFirstErrorsMessage());
        }

        $baseKey = Cache::ALL_SYSTEM_CONFIG_KEY . ':' . $name;

        Cache::set($baseKey, $value);
    }

    public static function setOperationSetting($key, $value)
    {
        // pc_home_sub_nav_column_cia
        switch ($key) {
            case self::PC_HOME_SUB_NAV_COLUMN_SPECIAL_KEY:
                self::setPcHomeSubNavColumnSpecial($value);
                break;
            case self::PC_HOME_SUB_NAV_COLUMN_CIA_KEY:
                self::setPcHomeSubNavColumnCIA($value);
                break;
            case self::PC_HOME_SUB_NAV_COLUMN_SERVICE_STATION_KEY:
                self::setPcHomeSubNavColumnServiceStation($value);
                break;
            case self::PC_HOME_HOT_TOPIC_KEY:
                self::setPcHomeHotTopic($value);
                break;
            case self::PC_HOME_NAV_TALENT_TAG_KEY:
                self::setPcHomeNavTalentTag($value);
                break;
            case self::PC_SECOND_COLUMN_RESERVED_KEY:
                self::setPcSecondColumnReserved($value);
                break;
            case self::PC_HOME_ACTIVITY_KEY:
                self::setPcHomeActivity($value);
                break;
            case self::H5_SPECIAL_SUBJECT_KEY:
                self::setH5SpecialSubject($value);
                break;
            case self::H5_HOT_SEARCH_LIST_JOB_KEY:
                self::setH5HotSearchListJob($value);
                break;
            default:
                throw new \Exception('配置不存在');
        }
    }

    /**
     * 专题展
     * @param $array
     * @return true
     * @throws \Exception
     */
    public static function setPcHomeSubNavColumnSpecial($array)
    {
        // 检查一下数据,必须要有moreLink和list数组,list里面每一个必须有url和name
        if (!isset($array['moreLink']) || !isset($array['list']) || !is_array($array['list'])) {
            throw new \Exception('数据格式不正确');
        }

        foreach ($array['list'] as $item) {
            if (!isset($item['url']) || !isset($item['name'])) {
                throw new \Exception('数据格式不正确');
            }
        }

        $array['name'] = '专题展';

        $json = json_encode($array);
        self::setValue(self::PC_HOME_SUB_NAV_COLUMN_SPECIAL_KEY, $json);

        return true;
    }

    /**
     * 专题展
     */
    public static function getPcHomeSubNavColumnSpecial()
    {
        $value = self::getValue(self::PC_HOME_SUB_NAV_COLUMN_SPECIAL_KEY, true);
        if (!$value) {
            return [];
        }

        return json_decode($value, true);
    }

    /**
     * 情报局
     * @param $array
     */

    public static function getPcHomeSubNavColumnCIA()
    {
        // 检查一下数据,必须要有moreLink和list数组,list里面每一个必须有url和name
        $value = self::getValue(self::PC_HOME_SUB_NAV_COLUMN_CIA_KEY, true);
        if (!$value) {
            return [];
        }

        return json_decode($value, true);
    }

    /**
     * 设置情报局
     * @param $array
     * @return true
     * @throws \Exception
     */

    public static function setPcHomeSubNavColumnCIA($array)
    {
        // 检查一下数据,必须要有moreLink和list数组,list里面每一个必须有url和name
        if (!isset($array['moreLink']) || !isset($array['list']) || !is_array($array['list'])) {
            throw new \Exception('数据格式不正确');
        }

        foreach ($array['list'] as $item) {
            if (!isset($item['url']) || !isset($item['name'])) {
                throw new \Exception('数据格式不正确');
            }
        }

        $array['name'] = '情报局';

        $json = json_encode($array);
        self::setValue(self::PC_HOME_SUB_NAV_COLUMN_CIA_KEY, $json);

        return true;
    }

    /**
     * 服务站
     * @param $array
     */

    public static function getPcHomeSubNavColumnServiceStation()
    {
        // 检查一下数据,必须要有moreLink和list数组,list里面每一个必须有url和name
        $value = self::getValue(self::PC_HOME_SUB_NAV_COLUMN_SERVICE_STATION_KEY, true);
        if (!$value) {
            return [];
        }

        return json_decode($value, true);
    }

    /**
     * 设置服务站
     * @param $array
     * @return true
     * @throws \Exception
     */

    public static function setPcHomeSubNavColumnServiceStation($array)
    {
        // 检查一下数据,必须要有moreLink和list数组,list里面每一个必须有url和name
        if (!isset($array['moreLink']) || !isset($array['list']) || !is_array($array['list'])) {
            throw new \Exception('数据格式不正确');
        }

        foreach ($array['list'] as $item) {
            if (!isset($item['url']) || !isset($item['name'])) {
                throw new \Exception('数据格式不正确');
            }
        }

        // $array['name'] = '服务站';
        // 2023-09-15 郑鑫要求改名字
        $array['name'] = '资讯栏目';

        $json = json_encode($array);
        self::setValue(self::PC_HOME_SUB_NAV_COLUMN_SERVICE_STATION_KEY, $json);

        return true;
    }

    /**
     * 热门专题
     * @param $array
     */

    public static function getPcHomeHotTopic()
    {
        // 检查一下数据,必须要有moreLink和list数组,list里面每一个必须有url和name
        $value = self::getValue(self::PC_HOME_HOT_TOPIC_KEY, true);
        if (!$value) {
            return [];
        }

        return json_decode($value, true);
    }

    /**
     * 设置热门专题
     * @param $array
     * @return true
     * @throws \Exception
     */

    public static function setPcHomeHotTopic($array)
    {
        $json = json_encode($array);
        self::setValue(self::PC_HOME_HOT_TOPIC_KEY, $json);

        return true;
    }

    /**
     * @return array|mixed
     */
    public static function getPcHomeNavTalentTag()
    {
        // 检查一下数据,必须要有moreLink和list数组,list里面每一个必须有url和name
        $value = self::getValue(self::PC_HOME_NAV_TALENT_TAG_KEY, true);
        if (!$value) {
            return [];
        }

        return json_decode($value, true);
    }

    /**
     * @param $array
     * @return bool
     * @throws \Exception
     */
    public static function setPcHomeNavTalentTag($array)
    {
        $json = json_encode($array);
        self::setValue(self::PC_HOME_NAV_TALENT_TAG_KEY, $json);

        return true;
    }

    /**
     * @return array|mixed
     */
    public static function getPcSecondColumnReserved()
    {
        $value = self::getValue(self::PC_SECOND_COLUMN_RESERVED_KEY, true);
        if (!$value) {
            return [];
        }

        return json_decode($value, true);
    }

    /**
     * @param $array
     * @return bool
     * @throws \Exception
     */
    public static function setPcSecondColumnReserved($array)
    {
        $json = json_encode($array);
        self::setValue(self::PC_SECOND_COLUMN_RESERVED_KEY, $json);

        return true;
    }

    /**
     * @return array|mixed
     */
    public static function getPcHomeActivity()
    {
        $value = self::getValue(self::PC_HOME_ACTIVITY_KEY, true);
        if (!$value) {
            return [];
        }

        return json_decode($value, true);
    }

    /**
     * @param $array
     * @return bool
     * @throws \Exception
     */
    public static function setPcHomeActivity($array)
    {
        $json = json_encode($array);
        self::setValue(self::PC_HOME_ACTIVITY_KEY, $json);

        return true;
    }

    /**
     * @return array|mixed
     */
    public static function getH5SpecialSubject()
    {
        $value = self::getValue(self::H5_SPECIAL_SUBJECT_KEY, true);
        if (!$value) {
            return [];
        }

        return json_decode($value, true);
    }

    /**
     * @param $array
     * @return bool
     * @throws \Exception
     */
    public static function setH5SpecialSubject($array)
    {
        $json = json_encode($array);
        self::setValue(self::H5_SPECIAL_SUBJECT_KEY, $json);

        return true;
    }

    /**
     * @return array|mixed
     */
    public static function getH5HotSearchListJob()
    {
        $value = self::getValue(self::H5_HOT_SEARCH_LIST_JOB_KEY, true);
        if (!$value) {
            return [];
        }

        return json_decode($value, true);
    }

    /**
     * @param $array
     * @return bool
     * @throws \Exception
     */
    public static function setH5HotSearchListJob($array)
    {
        $json = json_encode($array);
        self::setValue(self::H5_HOT_SEARCH_LIST_JOB_KEY, $json);

        return true;
    }
}