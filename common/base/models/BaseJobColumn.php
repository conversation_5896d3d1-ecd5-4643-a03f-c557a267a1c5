<?php

namespace common\base\models;

use common\models\ArticleColumn;
use common\models\JobColumn;
use yii\base\Exception;
use Yii;

class BaseJobColumn extends JobColumn
{
    /**
     * 重新建立文章和栏目之间的关系
     * @param $columnIdArr
     * @param $jobId
     */
    public static function rebuild($jobId, $columnIdArr)
    {
        if (!$jobId) {
            throw new Exception('职位id不能为空');
        }
        if (!$columnIdArr) {
            return;
        }
        $data = [];
        $key  = [
            'add_time',
            'job_id',
            'column_id',
        ];

        foreach ($columnIdArr as $item) {
            if ($item) {
                $data[] = [
                    CUR_DATETIME,
                    $jobId,
                    $item,
                ];
            }
        }

        // 先删除全部
        self::deleteAll(['job_id' => $jobId]);
        // aa($key);
        // bb($data);
        // 后批量写入
        Yii::$app->db->createCommand()
            ->batchInsert(self::tableName(), $key, $data)
            ->execute();
    }

}