<?php

namespace common\base\models;

use common\models\EmailLog;
use common\service\job\BaseService;
use common\service\job\JobApplyHandleService;
use Faker\Provider\Base;

class BaseEmailLog extends EmailLog
{
    const STATUS_SUCCESS = 1; // 发送成功
    const STATUS_SENDING = 2; // 正在发送
    const STATUS_FAIL    = -1; // 发送失败
    const STATUS_WAIT    = 9; // 等待发送

    const IS_READ_YES = 1;
    const IS_READ_NO  = 2;

    // 暂时就做一个简单已读处理算了
    public static function read($data)
    {
        $emailId = $data['emailId'];

        $model = self::findOne($emailId);
        if (!$model) {
            return;
        }
        if ($model->is_read == self::IS_READ_YES) {
            return;
        }
        $model->is_read = self::IS_READ_YES;
        $res            = $model->save();
        if ($res) {
            //成功已读后逻辑
            //解析邮件的内容
            $content = json_decode($model->content, true);
            if (isset($content['jobApplyId']) && $content['jobApplyId'] > 0) {
                //获取投递信息
                $apply_info = BaseJobApply::findOne($content['jobApplyId']);
                if ($apply_info) {
                    $equity_apply = BaseJobApplyTopEquityRecord::getLastUseEquity($apply_info->job_id,
                        $apply_info->resume_id);
                    if ($equity_apply && $apply_info->status == BaseJobApply::STATUS_HANDLE_WAIT && $apply_info->is_check == BaseJobApply::IS_CHECK_NO) {
                        //写查看日志
                        //创建查看日志
                        $service = new JobApplyHandleService();
                        $service->setOperator($apply_info->company_member_id, BaseService::OPERATOR_HANDLE_TYPE_COMPANY)
                            ->setData([
                                'id'                => $content['jobApplyId'],
                                'company_member_id' => $content['jobApplyId'],
                                'handle_type'       => BaseJobApplyHandleLog::TYPE_VIEW,
                            ])
                            ->runCheck();
                        //谁看过我
                        BaseCompanyViewResume::create($apply_info->company_id, $apply_info->resume_id);
                    }
                    //做一个PV统计
                    BaseCompanyResumePvTotal::updateDailyTotalPv($apply_info->resume_id);
                }
            }
        }
    }
}