<?php

namespace common\base\models;

use common\helpers\TimeHelper;
use common\models\HwActivitySession;
use yii\base\Exception;

class BaseHwActivitySession extends HwActivitySession
{

    public function rules()
    {
        return [
            [
                [
                    'add_time',
                    'update_time',
                    'start_date',
                    'end_date',
                ],
                'safe',
            ],
            [
                [
                    'status',
                    'activity_id',
                    'time_type',
                    'sort',
                ],
                'integer',
            ],
            [
                [
                    'name',
                    'number',
                    'custom_time',
                    'custom_address',
                    'detail_address',
                ],
                'string',
                'max' => 255,
            ],
            [
                [
                    'start_time',
                    'end_time',
                ],
                'string',
                'max' => 4,
            ],
        ];
    }

    const TIME_TYPE_LOCAL   = 1;
    const TIME_TYPE_BEIJING = 2;

    const TIME_TYPE_TEXT_LIST = [
        self::TIME_TYPE_LOCAL   => '当地时间',
        self::TIME_TYPE_BEIJING => '北京时间',
    ];

    /**
     * 删除场次
     * @param $id
     * @return bool
     * @throws Exception
     * @throws \yii\db\Exception
     */
    public static function delSession($id)
    {
        $model         = self::findOne($id);
        $model->status = self::STATUS_DELETE;
        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        //删除成功后 删除场次下面的地区
        BaseHwActivitySessionArea::deleteAll(['session_id' => $id]);

        return true;
    }

    /**
     * 设置排序
     * @param $id
     * @param $sort
     * @return bool
     * @throws Exception
     * @throws \yii\db\Exception
     */
    public static function setSort($id, $sort)
    {
        $model       = self::findOne($id);
        $model->sort = $sort;
        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }

        return true;
    }

    /**
     * 保存场次临时数据
     * @param $data
     * @return int
     * @throws Exception
     * @throws \yii\db\Exception
     */
    public static function saveInfo($data)
    {
        if ($data['id']) {
            $sessionModel = self::findOne(['id' => $data['id']]);
        } else {
            $sessionModel = new self();
        }

        // 判断结束时间必须大于开始时间
        if ($data['customTime'] == '' && ($data['startDate'] != TimeHelper::ZERO_DATE)) {
            // 非自定义时间,结束时间必须大于等于开始时间，转成时间搓去判断
            if (strtotime($data['endDate']) < strtotime($data['startDate'])) {
                throw new Exception('结束时间必须大于等于开始时间');
            }

            if ($data['startDate'] == $data['endDate'] && $data['startTime'] && $data['endTime']) {
                if ($data['startTime'] >= $data['endTime']) {
                    throw new Exception('结束时间必须大于开始时间');
                }
            }
        }

        $sessionModel->name           = $data['name'];
        $sessionModel->number         = $data['number'];
        $sessionModel->custom_time    = $data['customTime'];
        $sessionModel->start_date     = $data['startDate'] ?: TimeHelper::ZERO_DATE;
        $sessionModel->start_time     = $data['startTime'] ?: '';
        $sessionModel->end_date       = $data['endDate'] ?: TimeHelper::ZERO_DATE;
        $sessionModel->end_time       = $data['endTime'] ?: '';
        $sessionModel->time_type      = $data['timeType'];
        $sessionModel->custom_address = $data['customAddress'] ?: '';
        $sessionModel->detail_address = $data['detailAddress'] ?: '';
        $sessionModel->sort           = $data['sort'] ?: 0;

        if (!$sessionModel->save()) {
            throw new Exception($sessionModel->getFirstErrorsMessage());
        }
        $sessionId = $sessionModel->id;
        if (!empty($data['areaIds'])) {
            //如果存在地点id，先把关联表数据清空
            BaseHwActivitySessionArea::deleteAll(['session_id' => $sessionId]);
            $areaList = explode(',', $data['areaIds']);
            foreach ($areaList as $areaId) {
                $sessionAreaModel              = new BaseHwActivitySessionArea();
                $sessionAreaModel->activity_id = $sessionModel->activity_id;
                $sessionAreaModel->session_id  = $sessionId;
                $sessionAreaModel->area_id     = $areaId;
                $sessionAreaModel->level       = BaseArea::findOneVal(['id' => $areaId], 'level');
                if (!$sessionAreaModel->save()) {
                    throw new Exception($sessionAreaModel->getFirstErrorsMessage());
                }
            }
        }

        return $sessionId;
    }

    /**
     * 获取活动场次列表
     * @param $activityId
     * @return array
     */
    public static function getSessionInfoList($activityId)
    {
        $sessionList = self::find()
            ->where([
                'activity_id' => $activityId,
                'status'      => self::STATUS_ACTIVE,
            ])
            ->orderBy('sort desc')
            ->all();

        $list = [];
        foreach ($sessionList as $session) {
            $list[] = [
                'id'              => $session['id'],
                'name'            => $session['name'],
                'activityDate'    => self::getActivityDate($session['id']),
                'activityAddress' => self::getActivityAddress($session['id']),
            ];
        }

        return $list;
    }

    /**
     * 获取活动地点
     * @param $id
     * @return false|mixed|string
     */
    public static function getActivityAddress($id)
    {
        $sessionInfo = self::findOne($id);
        if ($sessionInfo['custom_address']) {
            $activityAddress = $sessionInfo['custom_address'];
        } else {
            $sessionAreaIds  = BaseHwActivitySessionArea::find()
                ->where(['session_id' => $id])
                ->select(['area_id'])
                ->column();
            $activityAddress = BaseArea::getTextByIdList($sessionAreaIds);
            if ($sessionInfo['detail_address']) {
                $activityAddress .= '   ' . $sessionInfo['detail_address'];
            }
        }

        return $activityAddress;
    }

    /**
     * 获取场次的活动时间
     * 同一天只能显示开始天
     * 同一天不同分钟要显示天+分钟范围
     * 不同天，显示开始天～结束天 + 时分(如果有)
     * 最后再看情况拼接当地时间、北京时间
     * @param $id
     * @return mixed|string
     */
    public static function getActivityDate($id)
    {
        $sessionInfo = self::findOne($id);
        if ($sessionInfo['custom_time']) {
            return $sessionInfo['custom_time'];
        }
        // $timeType = self::TIME_TYPE_TEXT_LIST[$sessionInfo['time_type']] ?: '';
        // if ($timeType) {
        //     return $sessionInfo['start_date'] . '～' . $sessionInfo['end_date'] . '(' . $timeType . ')';
        // }
        //
        // return $sessionInfo['start_date'] . '～' . $sessionInfo['end_date'];

        // 先处理时间 第二位后面加上:
        if ($sessionInfo['start_time']) {
            $sessionInfo['start_time'] = substr_replace($sessionInfo['start_time'], ':', 2, 0);
        }

        if ($sessionInfo['end_time']) {
            $sessionInfo['end_time'] = substr_replace($sessionInfo['end_time'], ':', 2, 0);
        }

        if ($sessionInfo['start_date'] == $sessionInfo['end_date']) {
            if ($sessionInfo['start_time'] == $sessionInfo['end_time']) {
                // 需要考虑start_time或者end_time不存在的情况
                if ($sessionInfo['start_time']) {
                    $date = $sessionInfo['start_date'] . ' ' . $sessionInfo['start_time'];
                } else {
                    $date = $sessionInfo['start_date'];
                }
            } else {
                // 需要考虑start_time或者end_time不存在的情况
                if ($sessionInfo['start_time'] && $sessionInfo['end_time']) {
                    $date = $sessionInfo['start_date'] . ' ' . $sessionInfo['start_time'] . '～' . $sessionInfo['end_time'];
                } elseif ($sessionInfo['start_time']) {
                    $date = $sessionInfo['start_date'] . ' ' . $sessionInfo['start_time'];
                } elseif ($sessionInfo['end_time']) {
                    $date = $sessionInfo['start_date'] . ' ' . $sessionInfo['end_time'];
                } else {
                    $date = $sessionInfo['start_date'] . ' ' . $sessionInfo['end_time'];
                }
            }
        } else {
            // 需要考虑start_time或者end_time不存在的情况
            if ($sessionInfo['start_time'] && $sessionInfo['end_time']) {
                $date = $sessionInfo['start_date'] . ' ' . $sessionInfo['start_time'] . '～' . $sessionInfo['end_date'] . ' ' . $sessionInfo['end_time'];
            } elseif ($sessionInfo['start_time']) {
                $date = $sessionInfo['start_date'] . ' ' . $sessionInfo['start_time'] . '～' . $sessionInfo['end_date'];
            } elseif ($sessionInfo['end_time']) {
                $date = $sessionInfo['start_date'] . '～' . $sessionInfo['end_date'] . ' ' . $sessionInfo['end_time'];
            } else {
                $date = $sessionInfo['start_date'] . '～' . $sessionInfo['end_date'];
            }
        }

        $timeType = self::TIME_TYPE_TEXT_LIST[$sessionInfo['time_type']] ?: '';
        if ($timeType) {
            return $date . '(' . $timeType . ')';
        }

        return $date;
    }

}