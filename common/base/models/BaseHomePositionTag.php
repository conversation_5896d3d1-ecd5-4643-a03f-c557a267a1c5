<?php

namespace common\base\models;

use common\helpers\ArrayHelper;
use common\models\HomePositionTag;

class BaseHomePositionTag extends HomePositionTag
{
    /**
     * 获取单位特色标签列表
     * @return array|bool
     */
    public static function getTagList()
    {
        $field = [
            'id as k',
            'tag as v',
        ];

        $list     = self::find()
            ->select($field)
            ->orderBy('id desc')
            ->asArray()
            ->all();

        return ArrayHelper::objMoreArr($list);
    }
}