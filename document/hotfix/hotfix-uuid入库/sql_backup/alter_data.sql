ALTER TABLE resume
    ADD COLUMN `uuid` varchar(64) NOT NULL DEFAULT '' COMMENT 'uuid';
ALTER TABLE company
    ADD COLUMN `uuid` varchar(64) NOT NULL DEFAULT '' COMMENT 'uuid';
ALTER TABLE job
    ADD COLUMN `uuid` varchar(64) NOT NULL DEFAULT '' COMMENT 'uuid';
ALTER TABLE announcement
    ADD COLUMN `uuid` varchar(64) NOT NULL DEFAULT '' COMMENT 'uuid';

-- 上面四个字段都需要添加索引
ALTER TABLE resume ADD INDEX `idx_uuid` (`uuid`);
ALTER TABLE company ADD INDEX `idx_uuid` (`uuid`);
ALTER TABLE job ADD INDEX `idx_uuid` (`uuid`);
ALTER TABLE announcement ADD INDEX `idx_uuid` (`uuid`);

-- resume表的uuid是 1开头后面拼接00+id组成的10000002 这样的格式,下面是四个表的修改语句
UPDATE resume SET uuid = CONCAT('1', LPAD(id, 7, '0'));
UPDATE company SET uuid = CONCAT('2', LPAD(id, 7, '0'));
UPDATE job SET uuid = CONCAT('3', LPAD(id, 7, '0'));
UPDATE announcement SET uuid = CONCAT('4', LPAD(id, 7, '0'));