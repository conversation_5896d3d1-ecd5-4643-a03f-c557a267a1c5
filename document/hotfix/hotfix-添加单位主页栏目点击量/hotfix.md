# ReadMe

### 禅道bug(罗列bug地址或者id)

- 研发需求44

***

### 参与人员

-

***

### 分支

|仓库|bug分支|备注|
|:----:|:----:|:----:|
|new_gaoxiao_yii|hotfix/添加栏目类型 |-|
|new_gaoxiao_company_pc_vue|||
|new_gaoxiao_admin_pc_vue|||

***

### 上线部署步骤

`内容为空,填写"-"即可`

|步骤|是否执行|执行内容|
|:----|:----:|:----|
|提醒前端提前合并代码到master和添加权限节点|-|-|
|执行sql语句|-|添加home_column_dictionary_relationship整张表|
|更新后端代码|是|-|
|composer安装|-|见下方"composer安装"|
|更新配置|-|见下方"更新配置"|
|创建队列|-|见下方"创建队列"|
|执行脚本|-|见下方"执行脚本"|
|删除redis缓存|-|见下方"删除redis缓存"|
|重启队列|-|上线前, 暂停所有队列, 上线后再重启|
|更新前端代码|-|-|
|添加定时任务|-|见下方"定时任务"|
|群内通知部署完毕|-|-|

#### composer安装

#### 执行sql语句(按顺序执行)

* base.sql

#### 执行脚本(普通脚本建议支持后台运行,特殊脚本需要注明执行顺序)

无
