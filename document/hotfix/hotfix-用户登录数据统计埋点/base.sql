
CREATE TABLE `buried_point_log`
(
    `id`            int(10) NOT NULL AUTO_INCREMENT,
    `add_time`      datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `ip`            int(11) unsigned NOT NULL DEFAULT '0' COMMENT '最近一次登录的ip',
    `user_cookies`  varchar(64)  NOT NULL DEFAULT '' COMMENT '用户的cookie',
    `action_type`   tinyint(1) NOT NULL DEFAULT '0' COMMENT '（1、点击切换方式 2、点击提交 3、点击发送验证码 4、扫码 5、发送验证码数据接收 6、提交手机登录数据接收 7提交账号登录数据接收）\n',
    `action_url`    varchar(255) NOT NULL DEFAULT '' COMMENT '操作所在链接',
    `params`        varchar(255) NOT NULL DEFAULT '' COMMENT '参数',
    `action_module` tinyint(1) NOT NULL DEFAULT '0' COMMENT '（1首页手机登录 2首页账号登录 3首页扫码登录 4注册页手机登录 5注册页账号登录 6注册页扫码登录 7登录页手机登录 8登录页账号登录 9登录页扫码登录 10弹窗手机登录 11弹窗账号登录 12弹窗扫码登录 13详情页手机登录 14页面底部手机登录）\n',
    `member_id`     int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=169 DEFAULT CHARSET=utf8mb4 COMMENT='埋点操作记录';