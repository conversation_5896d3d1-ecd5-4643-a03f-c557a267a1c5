# ReadMe

- [STORY #846 学科调整：一级学科隐藏 - 新官网 - 禅道 (jugaocai.com)](https://zentao.jugaocai.com/index.php?m=story&f=view&id=846)

***

### 参与人员

- 龚传栋
- 李志豪

***

### 分支

|仓库|bug分支|备注|
|:----:|:----:|:----:|
|new_gaoxiao_yii|hotfix/major_update3|-|



***

### 上线部署步骤

`内容为空,填写"-"即可`

|步骤| 是否执行 |执行内容|
|:----|:----:|:----|
|提醒前端提前合并代码到master和添加权限节点|  -   |-|
|执行sql语句|  是   |见下方"执行sql语句"|
|更新后端代码|  是   |-|
|composer安装|  -   |见下方"composer安装"|
|更新配置|  -   |见下方"更新配置"|
|创建队列|  -   |见下方"创建队列"|
|执行脚本|  -   |见下方"执行脚本"|
|删除redis缓存|  -   |见下方"删除redis缓存"|
|重启队列|  -   |上线前, 暂停所有队列, 上线后再重启|
|更新前端代码|  是   |-|
|添加定时任务|  -   |见下方"定时任务"|
|群内通知部署完毕|  -   |-|

#### 执行脚步

执行前看major表的id，到多少了，应该是到815的

![image-20240720111125132](http://img.ideaboat.cn/uPic/image-20240720111125132.png)

```
php timer_yii major-update/update-nine
```

#### 删除缓存key

```
ALL:MAJOR:SEARCH:IDS:TO:LEVEL3
ALL:TABLE:MAJOR
ADMIN:MAJOR:AI_TEXT:LIST
```

执行完上面的脚步，应该去检查一下，求职者、单位、运营后台的学科是否都正常了，并这些major的id需要和预发布是一致的



然后正式执行下面的时候，求职者那边的数量应该是15695（7.20的数据），在dev执行脚步，数据是3276，执行用时38.5，正式环境判断用时3～4分钟

```
迁移求职者信息
php timer_yii major-update/update-eighth
```
