SET
FOREIGN_KEY_CHECKS=0;

ALTER TABLE `new_gaoxiaojob`.`showcase`
    ADD COLUMN `is_packing` tinyint(1) NOT NULL DEFAULT 2 COMMENT '是否打包管理(打包管理的广告位更新是需要一起更新的，1:是；2:否' AFTER `is_show`;

CREATE TABLE `new_gaoxiaojob`.`showcase_packing`
(
    `id`       int(11) NOT NULL AUTO_INCREMENT,
    `add_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `remark`   varchar(256) NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`id`) USING BTREE
);

CREATE TABLE `new_gaoxiaojob`.`showcase_packing_relationship`
(
    `id`                  int(11) NOT NULL AUTO_INCREMENT,
    `add_time`            datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `showcase_id`         int(11) NOT NULL DEFAULT 0 COMMENT '广告位id',
    `showcase_packing_id` int(11) NOT NULL DEFAULT 0 COMMENT '打包的id',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX                 `idx_showcase_id`(`showcase_id`) USING BTREE,
    INDEX                 `idx_showcase_packing_id`(`showcase_packing_id`) USING BTREE
);

SET
FOREIGN_KEY_CHECKS=1;