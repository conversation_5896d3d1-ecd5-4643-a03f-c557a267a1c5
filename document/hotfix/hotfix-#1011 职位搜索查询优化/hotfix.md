# ReadMe


***

### 参与人员

- 伍彦川

***

### 分支

|仓库|    bug分支     |备注|
|:----:|:------------:|:----:|
|new_gaoxiao_yii| hotfix/1011职位搜索查询优化 |-|



***

### 上线部署步骤

`内容为空,填写"-"即可`

|步骤| 是否执行 | 执行内容         |
|:----|:----:|:-------------|
|提醒前端提前合并代码到master和添加权限节点|  -   | -            |
|执行sql语句|  是   | 见下方"执行sql语句" |
|更新后端代码|  是   | -            |
|composer安装|  -   | -            |
|更新配置|  -   | -            |
|创建队列|  -   | -            |
|执行脚本|  -   | -            |
|删除redis缓存|  -   | -            |
|重启队列|  -   | -            |
|更新前端代码|  是   | -            |
|添加定时任务|  -   | 见下方"定时任务"    |
|群内通知部署完毕|  -   | -            |


#### 执行sql语句(按顺序执行)

* alter_data.sql

```
选定预热关键字 - 晚上一点执行一次
php timer_yii job/preprocess-job-keywords
```

```
根据关键字-预热查询职位 - 半小时执行一次
php timer_yii job/preprocess-job-ids-by-keywords
```
