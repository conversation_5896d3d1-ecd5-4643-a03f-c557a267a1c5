# ReadMe

### 2.7公告&职位优化

-

***

### 参与人员

- 伍彦川
- 陈钊发
- 单文超
- 杜孙鹤

***

### 分支

|             仓库             |        开发分支         |        提測分支         | 备注 |
|:--------------------------:|:-------------------:|:-------------------:|:--:|
|      new_gaoxiao_yii       | feature/V2.7公告_职位优化 | feature/V2.7公告_职位优化 | -  |
|  new_gaoxiao_admin_pc_vue  |     hotfix/v2.7     |     hotfix/v2.7     | -  |
| new_gaoxiao_company_pc_vue |     hotfix/v2.7     |     hotfix/v2.7     | -  |

***

### 上线部署步骤

`内容为空,填写"-"即可`

| 步骤                       | 是否执行 | 执行内容         |
|:-------------------------|:----:|:-------------|
| 提醒前端提前合并代码到master和添加权限节点 |  -   | -            |
| 执行sql语句                  |  -   | 见下方"执行sql语句" |
| 更新后端代码                   |  是   |              |
| composer安装               |  -   | -            |
| 更新配置                     |  -   | -            |
| 创建队列                     |  -   | -            |
| 执行脚本                     |  是   | -            |
| 删除redis缓存                |  -   | -            |
| 重启队列                     |  -   | -            |
| 更新前端代码                   |  -   | -            |
| 添加定时任务                   |  是   | -            |
| 群内通知部署完毕                 |  -   | -            |

#### 执行sql语句(按顺序执行)

    1、menu.sql 替换成脚本 php timer_yii script/update-menu
    php timer_yii script/update-menu
    2、alter_data.sql(执行时间1112秒)
    已更新结构，新的只要230秒
    3、update_feild.sql(执行时间30秒)
    40秒

#### 定时任务

    1、php timer_yii job/offline 修改为 php timer_yii job/offline-v2 （执行时间3s)
    2、php timer_yii announcement/offline 修改为 php timer_yii announcement/offline-v2 （602秒）
    没有时间
    3、php timer_yii script/update-announcement-status(一次性脚本)
    总时长 0.24904203414917
    4、php timer_yii script/update-job-status(一次性脚本 执行时间47秒)
     总时长 48.74666595459

#### 最后执行

    # 用文章状态更新公告状态 --4秒
    UPDATE announcement as a left JOIN article art ON art.id = a.article_id SET a.STATUS = art.STATUS;
    3秒
    
    备份 announcement_edit和job_edit
    
    TRUNCATE TABLE `announcement_edit`;
    TRUNCATE TABLE `job_edit`;
    
    # 清空表数据 备份navicat 备份announcement_edit、job_edit两张表数据 然后清空两张表
    # 清空表数据 备份navicat 备份announcement_edit、job_edit两张表数据 然后清空两张表
    # 清空表数据 备份navicat 备份announcement_edit、job_edit两张表数据 然后清空两张表
    # 一些统计脚本
    php timer_yii script/company-stata-data-total （609秒）
    php timer_yii script/announcement-job-total （1454秒）

