# ReadMe

### 关联产品原型版本

- [1.2.1_V1.2_置顶功能开发]([蓝湖 (lanhuapp.com)](https://lanhuapp.com/web/#/item/project/product?tid=8d951de4-aefb-40f7-954e-366683d68331&pid=fa546800-9234-4ce0-8bc0-b63e0af32f27&versionId=f4a284ae-8894-48b0-baa6-efa3e4cbc91d&docId=1667a601-6eb7-4322-a70a-972aad98ddbb&docType=axure&pageId=f46f248ced2e43e0b42785ed21c0779d&image_id=1667a601-6eb7-4322-a70a-972aad98ddbb&parentId=8a973856-699e-4669-b9fb-1ee82705bb5f))

***

### 参与人员

- 林建炫

***

### 分支

|仓库|开发分支|提測分支|备注|
|:----:|:----:|:----:|:----:|
|new_gaoxiao_yii|feature/V1.2_职位置顶算法|release/V1.2_职位置顶算法|-|
***

### 上线部署步骤

`内容为空,填写"-"即可`

|步骤| 是否执行 |执行内容|
|:----|:----:|:----|
|提醒前端提前合并代码到master和添加权限节点|  -   |路由名称：name=置顶配置，key=configurationJobTopList|
|执行sql语句|  -   |见下方"执行sql语句"|
|更新后端代码|  是   |-|
|composer安装|  -   |见下方"composer安装"|
|更新配置|  -   |见下方"更新配置"|
|创建队列|  -   |见下方"创建队列"|
|执行脚本|  是   |见下方"执行脚本"|
|删除redis缓存|  -   |见下方"删除redis缓存"|
|重启队列|  -   |上线前, 暂停所有队列, 上线后再重启|
|更新前端代码|  -   |-|
|添加定时任务|  -   |见下方"定时任务"|
|群内通知部署完毕|  -   |-|

#### 执行脚本（更新缓存）
````
php ./timer_yii job/job-list-cache