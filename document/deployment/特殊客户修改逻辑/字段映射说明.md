# 特殊需求配置字段映射说明

## 1. 概述

特殊需求配置系统使用统一的字段名称来管理不同平台的字段差异。系统会根据实际数据结构自动将统一字段名映射到具体的实现字段名。

## 2. 统一字段映射表

### 2.1 专业要求字段 (major)
**统一字段名**: `major`

**映射的实际字段**:
- `major` - 基础字段（通常是ID）
- `majorTxt` - H5/PC端文本字段
- `major_txt` - 小程序端文本字段  
- `majorName` - H5/PC端显示名称
- `major_name` - 小程序端显示名称

**使用示例**:
```sql
-- 正确：使用统一字段名
INSERT INTO special_need_config (field_name, field_value) 
VALUES ('major', '计算机科学与技术');

-- 错误：使用具体实现字段名
INSERT INTO special_need_config (field_name, field_value) 
VALUES ('majorTxt', '计算机科学与技术');
```

### 2.2 学历要求字段 (education)
**统一字段名**: `education`

**映射的实际字段**:
- `education` - 通用学历字段
- `minEducation` - PC端最低学历要求
- `educationText` - H5端学历文本
- `minEducationName` - 小程序端最低学历名称
- `educationName` - 小程序端学历名称

**使用示例**:
```sql
-- 正确：使用统一字段名
INSERT INTO special_need_config (field_name, field_value) 
VALUES ('education', '硕士研究生及以上');

-- 错误：使用具体实现字段名
INSERT INTO special_need_config (field_name, field_value) 
VALUES ('minEducation', '硕士研究生及以上');
```

### 2.3 招聘人数字段 (recruitAmount)
**统一字段名**: `recruitAmount`

**映射的实际字段**:
- `amount` - 基础招聘人数字段
- `recruitAmount` - 招聘人数
- `jobRecruitAmount` - 职位招聘人数
- `announcement_recruit_amount` - 小程序端公告招聘人数

**使用示例**:
```sql
-- 正确：使用统一字段名
INSERT INTO special_need_config (field_name, field_value) 
VALUES ('recruitAmount', '1~2人');

-- 错误：使用具体实现字段名
INSERT INTO special_need_config (field_name, field_value) 
VALUES ('amount', '1~2人');
```

### 2.4 报名方式字段 (applyType)
**统一字段名**: `applyType`

**映射的实际字段**:
- `applyType` - 基础报名方式字段（通常是ID）
- `applyTypeText` - H5/PC端报名方式文本
- `apply_type_text` - 小程序端报名方式文本

**使用示例**:
```sql
-- 正确：使用统一字段名
INSERT INTO special_need_config (field_name, field_value) 
VALUES ('applyType', '网上报名');

-- 错误：使用具体实现字段名
INSERT INTO special_need_config (field_name, field_value) 
VALUES ('applyTypeText', '网上报名');
```

### 2.5 职位类别字段 (jobCategory)
**统一字段名**: `jobCategory`

**映射的实际字段**:
- `jobCategory` - 职位类别

**使用示例**:
```sql
-- 正确：使用统一字段名
INSERT INTO special_need_config (field_name, field_value) 
VALUES ('jobCategory', '教学科研岗');
```

## 3. 字段映射原理

### 3.1 自动映射机制
系统在应用配置时会自动进行字段映射：

1. **检查数据结构**: 系统检查目标数据中存在哪些字段
2. **选择最佳字段**: 根据优先级选择最合适的实际字段名
3. **应用配置值**: 将配置值应用到选中的字段
4. **同步相关字段**: 自动同步更新所有相关字段

### 3.2 优先级规则

#### 专业要求字段优先级
1. `major_name` (小程序端显示名称)
2. `major_txt` (小程序端文本)  
3. `majorName` (H5/PC端显示名称)
4. `majorTxt` (H5/PC端文本)
5. `major` (基础ID字段)

#### 学历要求字段优先级
1. `educationName` (小程序端学历名称)
2. `minEducationName` (小程序端最低学历名称)
3. `educationText` (H5端学历文本)
4. `minEducation` (PC端最低学历要求)
5. `education` (通用学历字段)

#### 招聘人数字段优先级
1. `amount` (基础招聘人数)
2. `recruitAmount` (招聘人数)
3. `jobRecruitAmount` (职位招聘人数)
4. `announcement_recruit_amount` (公告招聘人数)

#### 报名方式字段优先级
1. `apply_type_text` (小程序端报名方式文本)
2. `applyTypeText` (H5/PC端报名方式文本)
3. `applyType` (基础报名方式ID)

## 4. 特殊处理规则

### 4.1 追加模式
当字段值以逗号开头时，表示追加模式：

```sql
-- 追加专业要求
INSERT INTO special_need_config (field_name, field_value) 
VALUES ('major', ',中共党史党建学');
-- 结果：原有专业 + ",中共党史党建学"
```

### 4.2 替换模式
当字段值不以逗号开头时，表示替换模式：

```sql
-- 替换专业要求
INSERT INTO special_need_config (field_name, field_value) 
VALUES ('major', '计算机科学与技术');
-- 结果：完全替换为 "计算机科学与技术"
```

### 4.3 同步更新
系统会自动同步更新所有相关字段，确保数据一致性：

```sql
-- 配置一个字段
INSERT INTO special_need_config (field_name, field_value) 
VALUES ('major', '计算机科学与技术');

-- 系统会自动同步更新：
-- majorTxt = '计算机科学与技术'
-- major_txt = '计算机科学与技术'  
-- majorName = '计算机科学与技术'
-- major_name = '计算机科学与技术'
```

## 5. 常见错误和解决方案

### 5.1 错误：使用具体实现字段名
```sql
-- ❌ 错误做法
INSERT INTO special_need_config (field_name, field_value) 
VALUES ('majorTxt', '计算机科学与技术');

-- ✅ 正确做法
INSERT INTO special_need_config (field_name, field_value) 
VALUES ('major', '计算机科学与技术');
```

### 5.2 错误：平台特定字段名
```sql
-- ❌ 错误做法
INSERT INTO special_need_config (field_name, field_value) 
VALUES ('major_name', '计算机科学与技术');

-- ✅ 正确做法
INSERT INTO special_need_config (field_name, field_value) 
VALUES ('major', '计算机科学与技术');
```

### 5.3 错误：混用字段名
```sql
-- ❌ 错误做法：同一个配置使用不同的字段名
INSERT INTO special_need_config (field_name, field_value) 
VALUES ('majorTxt', '计算机科学与技术');
INSERT INTO special_need_config (field_name, field_value) 
VALUES ('major_name', '软件工程');

-- ✅ 正确做法：统一使用标准字段名
INSERT INTO special_need_config (field_name, field_value) 
VALUES ('major', '计算机科学与技术');
INSERT INTO special_need_config (field_name, field_value) 
VALUES ('major', '软件工程');
```

## 6. 验证方法

### 6.1 检查字段名称使用
```sql
-- 检查是否使用了非标准字段名
SELECT field_name, COUNT(*) as count
FROM special_need_config 
WHERE field_name NOT IN ('major', 'education', 'recruitAmount', 'applyType', 'jobCategory')
GROUP BY field_name;
```

### 6.2 检查配置完整性
```sql
-- 检查各类型配置的字段分布
SELECT type, field_name, COUNT(*) as count
FROM special_need_config 
GROUP BY type, field_name
ORDER BY type, field_name;
```

## 7. 最佳实践

1. **始终使用统一字段名**: 在数据迁移和配置创建时，始终使用统一的字段名称
2. **避免平台特定字段**: 不要直接使用平台特定的字段名称
3. **利用自动映射**: 信任系统的自动字段映射机制
4. **测试多平台**: 确保配置在所有平台上都能正确生效
5. **文档记录**: 在配置的remark字段中记录详细说明

通过遵循这些规则，可以确保特殊需求配置系统的正确性和一致性。
