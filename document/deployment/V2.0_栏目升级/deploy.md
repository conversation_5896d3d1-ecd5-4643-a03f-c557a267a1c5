# ReadMe

### 关联产品原型版本
- [团队文件 - 蓝湖 (lanhuapp.com)](https://lanhuapp.com/dashboard/#/item?tid=8d951de4-aefb-40f7-954e-366683d68331)

***

### 参与人员

- 全员

***

### 分支

|仓库|开发分支|提測分支|备注|
|:----:|:----:|:----:|:----:|
|new_gaoxiao_yii|||-|



***

### 上线部署步骤

`内容为空,填写"-"即可`

|步骤|是否执行|执行内容|
|:----|:----:|:----|
|提醒前端提前合并代码到master和添加权限节点|-|-|
|执行sql语句|是|见下方"执行sql语句"|
|更新后端代码|是|-|
|composer安装|是|见下方"composer安装"|
|更新配置|-|见下方"更新配置"|
|创建队列|-|见下方"创建队列"|
|执行脚本|是|见下方"执行脚本"|
|删除redis缓存|-|见下方"删除redis缓存"|
|重启队列|-|上线前, 暂停所有队列, 上线后再重启|
|更新前端代码|-|-|
|添加定时任务|是|见下方"定时任务"|
|群内通知部署完毕|是|-|



## 更新配置文件



这个版本上线的时候，因为需要做一个海外的二级域名，并且把现在pc的登录用户信息和这个二级域名互通，所以mian-local是需要做一些更改的,这个是frontendPd的local，域名那边

```
<?php

$config = [
    'as cors'    => [
        'class' => \yii\filters\Cors::className(),
        'cors'  => [
            // restrict access to
            'Origin'                           => [
                'http://127.0.0.1:5501',
                'http://127.0.0.1:5500',
                'http://frp.gc.ideaboat.cn',
                'http://127.0.0.1:53757',
                'http://frp2.gc.jugaocai.com',
            ],
            'Access-Control-Request-Method'    => [
                'GET',
                'POST',
                'PUT',
                'PATCH',
                'DELETE',
                'HEAD',
                'OPTIONS',
            ],
            'Access-Control-Request-Headers'   => ['*'],
            'Access-Control-Allow-Credentials' => true,
            'Access-Control-Max-Age'           => 86400,
            'Access-Control-Expose-Headers'    => [],

        ],
    ],
    'components' => [
        'request' => [
            'cookieValidationKey' => '4tCyb80HdgFCczncTIzrxNfT0ecglI2m',
        ],
        'user'         => [
            'class'           => 'yii\web\User',
            'identityClass'   => 'common\base\models\BaseMember',
            'enableAutoLogin' => true,
            'identityCookie'  => [
                'name'     => '_identity',
                'httpOnly' => true,
            ],
            'idParam'         => '__member',
        ],
        'session'      => [
            'name' => 'advanced',
            'cookieParams' => [
                'domain' => '.gaoxiaojob.com', // 设置为共享的主域名
            ],
        ],
    ],
];

if (!YII_ENV_TEST) {
    $config['bootstrap'][]      = 'debug';
    $config['modules']['debug'] = [
        'class' => 'yii\debug\Module',
    ];

    $config['bootstrap'][]    = 'gii';
    $config['modules']['gii'] = [
        'class' => 'yii\gii\Module',
    ];
}

return $config;

```

这边是新的站点haiTalentGloabl

```
<?php

$config = [
    'as cors'    => [
        'class' => \yii\filters\Cors::className(),
        'cors'  => [
            'Origin'                           => [
                'http://127.0.0.1:5501',
                'http://127.0.0.1:5500',
                'http://frp.gc.ideaboat.cn',
                'http://127.0.0.1:53757',
                'http://frp2.gc.jugaocai.com',
            ],
            'Access-Control-Request-Method'    => [
                'GET',
                'POST',
                'PUT',
                'PATCH',
                'DELETE',
                'HEAD',
                'OPTIONS',
            ],
            'Access-Control-Request-Headers'   => ['*'],
            'Access-Control-Allow-Credentials' => true,
            'Access-Control-Max-Age'           => 86400,
            'Access-Control-Expose-Headers'    => [],

        ],
    ],
    'components' => [
        'request' => [
            'cookieValidationKey' => '4tCyb80HqgFCczncTIzrxNfT0ecglI2m',
        ],
        'user'    => [
            'class'           => 'yii\web\User',
            'identityClass'   => 'common\base\models\BaseMember',
            'enableAutoLogin' => true,
            'identityCookie'  => [
                'name'     => '_identity',
                'httpOnly' => true,
            ],
            'idParam'         => '__member',
        ],
        'session' => [
            'name'         => 'advanced',
            'cookieParams' => [
                'domain' => '.gaoxiaojob.com',
                // 设置为共享的主域名
            ],
        ],
    ],
];

if (!YII_ENV_TEST) {
    $config['bootstrap'][]      = 'debug';
    $config['modules']['debug'] = [
        'class' => 'yii\debug\Module',
    ];

    $config['bootstrap'][]    = 'gii';
    $config['modules']['gii'] = [
        'class' => 'yii\gii\Module',
    ];
}

return $config;

```

#### 一次性脚步
#### 公告匹配 发布日期>=2024.7.1并且包含推荐+焦点的在线公告是么
```
php ./timer_yii script/hw-announcement
```

#### 定时任务

更新单位分值(明天晚上执行)

```
php timer_yii hw/update-overseas-point
```

更新活动状态(每分钟执行)

```
php timer_yii hw/update-activity-status
```

更新推广位置(每天执行)

```
php timer_yii hw/update-ad-position-status
```

