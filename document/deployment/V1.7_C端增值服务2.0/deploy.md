# ReadMe

### 关联产品原型版本

- [蓝湖 (lanhuapp.com)](https://lanhuapp.com/web/#/item/project/product?type=share_mark&tab=product&pid=8f28a6bf-03f0-4814-93ff-5d16019734e7&versionId=977f6ca3-eee7-48e5-9305-c2573fc7fdd2&docId=4d2bf0e3-bb85-4c28-888b-bb4b10e14186&image_id=4d2bf0e3-bb85-4c28-888b-bb4b10e14186&docType=axure&imgId=undefined&pageId=7e09a3980be648c887a741f9bf297a5e&teamId=8d951de4-aefb-40f7-954e-366683d68331&tid=8d951de4-aefb-40f7-954e-366683d68331&parentId=305e92fa-fe3d-471c-9059-2d5378b85ac0)

***

### 参与人员

- 单文超
- 杜孙鹤

***

### [alter_data.sql](sql_backup/alter_data.sql) 分支

|仓库|开发分支|提測分支|备注|
|:----:|:----:|:----:|:----:|
|new_gaoxiao_yii|origin/feature/V1.7_C端增值服务2.0|origin/release/V1.7_C端增值服务2.0|-|
|new_gaoxiao_admin_pc_vue|origin/feature/V1.7_C端增值服务2.0|origin/release/V1.7_C端增值服务2.0|-|
|new_gaoxiao_company_pc_vue|origin/feature/V1.7_C端增值服务2.0|origin/release/V1.7_C端增值服务2.0|-|
|new_gaoxiao_person_pc_vue|origin/feature/V1.7_C端增值服务2.0|origin/release/V1.7_C端增值服务2.0|-|

***

### 前端路由

### 上线部署步骤

`内容为空,填写"-"即可`

|步骤|是否执行|执行内容|
|:----|:----:|:----|
|提醒前端提前合并代码到master和添加权限节点|-|是|
|执行sql语句|是|见下方"执行sql语句"|
|更新后端代码|是|-|
|composer安装|是|见下方"composer安装"|
|更新配置|-|见下方"更新配置"|
|创建队列|-|见下方"创建队列"|
|执行脚本|是|见下方"执行脚本"|
|删除redis缓存|-|见下方"删除redis缓存"|
|重启队列|-|上线前, 暂停所有队列, 上线后再重启|
|更新前端代码|-|是|
|添加定时任务|是|见下方"定时任务"|
|群内通知部署完毕|是|-|

#### 执行sql语句(按顺序执行)

- alter_data.sql

#### 执行脚本

- php timer_yii script/update-resume-member-level(一次性脚本)
- php timer_yii script/update-equity-item-data(一次性脚本)
- php timer_yii resume-top-config/run(0 5 0 * * ?)
- php timer_yii resume-refresh/run(0 0 9 * * ?)
- php timer_yii job-apply-top-equity/run(0 0/30 * * * ?)
- php timer_yii job-apply-notice-push/run(0 0 */1 * * ?)
- php timer_yii resume-equity-service-about/run(0 0 10 * * ?)
- php timer_yii resume-member-name-change/run(VIP套餐名称变更---本版本上线完成后再处理)

#### 菜单

- key：configurationEquityPackageConfig
- name：权益套餐配置