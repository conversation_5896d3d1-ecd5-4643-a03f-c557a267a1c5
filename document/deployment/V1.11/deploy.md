# ReadMe

### 关联产品原型版本

-

***

### 参与人员

- 单文超
- 王昕

***

### 分支

|            仓库            |      开发分支       |      提測分支       | 备注 |
|:------------------------:|:---------------:|:---------------:|:--:|
| new_gaoxiao_admin_pc_vue | feature/V1.11    | release/V1.11     | -  |
|     new_gaoxiao_yii      | feature/V1.11代付 | release/V1.11代付 | -  |

***

### 前端路由

`"运营管理"一级模块下新增"人才套餐配置"`
name:setMeal

### 上线部署步骤

`内容为空,填写"-"即可`

| 步骤                       | 是否执行 | 执行内容         |
|:-------------------------|:----:|:-------------|
| 提醒前端提前合并代码到master和添加权限节点 |  -   | -            |
| 执行sql语句                  |  是   | 见下方"执行sql语句" |
| 更新后端代码                   |  是   | -            |
| composer安装               |  -   | -            |
| 更新配置                     |  -   | -            |
| 创建队列                     |  -   | -            |
| 执行脚本                     |  -   | -            |
| 删除redis缓存                |  -   | -            |
| 重启队列                     |  -   | -            |
| 更新前端代码                   |  是   | -            |
| 添加定时任务                   |  是   | -            |
| 群内通知部署完毕                 |  -   | -            |

#### 执行sql语句(按顺序执行)

- alter_data.sql

#### 推送消息机器人配置 params-local.php

```php
'wxWorkRobot' => [
    'adminPackageSetting' => [
        'key' => '422b773a-010c-4c1c-a0df-3952206ffb95',
    ],
],
