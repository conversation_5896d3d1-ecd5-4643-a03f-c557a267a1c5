CREATE TABLE `company_member_wx_bind`
(
    `id`                int(11)       NOT NULL AUTO_INCREMENT,
    `add_time`          datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       datetime      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`            tinyint(1)    NOT NULL DEFAULT '0' COMMENT '状态',
    `company_id`        int(11)       NOT NULL DEFAULT '0' COMMENT '单位id',
    `openid`            varchar(64)   NOT NULL DEFAULT '' COMMENT 'openid',
    `unionid`           varchar(64)   NOT NULL DEFAULT '' COMMENT 'unionid',
    `is_subscribe`      tinyint(1)    NOT NULL DEFAULT '2' COMMENT '是否关注1是 2否',
    `company_member_id` int(11)       NOT NULL DEFAULT '0' COMMENT '账号id',
    `wx_name`           varchar(100)  NOT NULL DEFAULT '' COMMENT '微信名称',
    `wx_avatar`         varchar(2550) NOT NULL DEFAULT '' COMMENT '微信头像',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_openid` (`openid`) USING BTREE,
    KEY `idx_company_id` (`company_id`),
    KEY `idx_unionid` (`unionid`),
    KEY `idx_ company_member_id` (`company_member_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;


CREATE TABLE `company_member_wx_bind_callback_log`
(
    `id`       int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `add_time` datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `openid`   varchar(64)   NOT NULL DEFAULT '' COMMENT 'openid',
    `data`     varchar(2048) NOT NULL DEFAULT '' COMMENT '实际的内容，使用json保存',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_openid` (`openid`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `company_member_message_config`
(
    `id`               int(11)    NOT NULL,
    `add_time`         datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
    `update_time`      datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `member_id`        int(11)    NOT NULL DEFAULT '0' COMMENT '账号ID',
    `company_id`       int(11)    NOT NULL DEFAULT '0' COMMENT '单位ID(隶属单位)',
    `delivery_message` tinyint(2) NOT NULL DEFAULT '1' COMMENT '简历投递消息提醒开关 0关  1开',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_member_id` (`member_id`) USING BTREE,
    KEY `idx_company_id` (`company_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位消息配置表';


alter table company_member_info
    add column is_wx_bind tinyint(2) not null default 0 comment '是否绑定微信0未绑定 1绑定';