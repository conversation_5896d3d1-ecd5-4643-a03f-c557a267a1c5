# ReadMe

### 关联产品原型版本
- ([优化版本-合作单位支持其他应聘方式）-蓝湖 (lanhuapp.com)](https://lanhuapp.com/web/#/item/project/product?pid=55d8227a-e4a5-4962-a858-6d6c8b9aa1a6&image_id=13ccb965-ee5c-40aa-b458-f6af54c95b41&versionId=b0a80302-d6d6-4e24-be2f-d143c95b2250&docId=13ccb965-ee5c-40aa-b458-f6af54c95b41&docType=axure&pageId=d2bfd3575c9f44698016e3e0e12e79e7))

***

### 参与人员

- 赵华聪
- 王昕

***

### 分支

|仓库|开发分支|提測分支|备注|
|:----:|:----:|:----:|:----:|
|new_gaoxiao_yii|feature-V1.0.1-合作单位支持其他应聘方式|release-V1.0.1-合作单位支持其他应聘方式|-|
|new_gaoxiao_company_admin_pc_vue|feature-V1.0.1-合作单位支持其他应聘方式|release-V1.0.1-合作单位支持其他应聘方式|-|
|new_gaoxiao_company_pc_vue|feature-V1.0.1-合作单位支持其他应聘方式|release-V1.0.1-合作单位支持其他应聘方式|-|
|new_gaoxiao_person_pc_vue |feature-V1.0.1-合作单位支持其他应聘方式|release-V1.0.1-合作单位支持其他应聘方式|-|



***

### 上线部署步骤

`内容为空,填写"-"即可`

|步骤|是否执行|执行内容|
|:----|:----:|:----|
|提醒前端提前合并代码到master和添加权限节点|-|-|
|执行sql语句|-|见下方"执行sql语句"|
|更新后端代码|是|-|
|composer安装|-|见下方"composer安装"|
|更新配置|-|见下方"更新配置"|
|创建队列|-|见下方"创建队列"|
|执行脚本|-|见下方"执行脚本"|
|删除redis缓存|-|见下方"删除redis缓存"|
|重启队列|是|上线前, 暂停所有队列, 上线后再重启|
|更新前端代码|是|-|
|添加定时任务|-|见下方"定时任务"|
|群内通知部署完毕|-|-|








