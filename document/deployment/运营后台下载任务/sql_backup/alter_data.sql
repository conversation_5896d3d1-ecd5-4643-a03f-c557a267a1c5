CREATE TABLE `admin_download_task` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `begin_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '开始下载的时间',
  `finish_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '结束下载的时间',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
  `admin_id` int(11) NOT NULL DEFAULT '0' COMMENT '简历id',
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型',
  `params` varchar(512) NOT NULL DEFAULT '' COMMENT '参数，根据每个下载任务不一样，放在json在里面',
  `path` varchar(512) NOT NULL DEFAULT '' COMMENT '路径',
  `reason` varchar(512) NOT NULL DEFAULT '' COMMENT '下载失败的原因，',
  `fail_count` int(11) NOT NULL DEFAULT '0' COMMENT '失败次数，这里包含一个重试机制',
  PRIMARY KEY (`id`),
  KEY `idx_add_time` (`add_time`),
  KEY `idx_admin_id` (`admin_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='运营后台下载任务';