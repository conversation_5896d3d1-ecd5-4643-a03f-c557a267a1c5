# ReadMe

### 关联产品原型版本

- [蓝湖 (lanhuapp.com)](https://lanhuapp.com/web/#/item/project/product?tid=8d951de4-aefb-40f7-954e-366683d68331&pid=994d7bc5-4c31-4b74-a5c3-71ea6dc0688f&versionId=eb52d6d3-a591-4340-9401-ebf786484ee8&docId=0f3fdada-c023-4f55-a6d1-cf9614609f7c&docType=axure&pageId=b15e8495a7ec4d2fade0c4758cf23b25&image_id=0f3fdada-c023-4f55-a6d1-cf9614609f7c&parentId=988cebd0-e00d-4afb-a73c-fc7fa65fb627)

***

### 参与人员

- 单文超
- 王昕
- 丘群森

***

###  [alter_data.sql](sql_backup/alter_data.sql) 分支

|仓库|开发分支|提測分支|备注|
|:----:|:----:|:----:|:----:|
|new_gaoxiao_yii|origin/feature/V1.4_子账号|-|-|
|new_gaoxiao_admin_pc_vue|origin/feature/V1.4|-|-|
|new_gaoxiao_company_pc_vue|origin/feature/V1.4|-|-|

***

### 前端路由

`"单位管理"一级模块下新增"账号查询","创建子账号","账号设置","账号日志"`
name:accountList,addAccount,accountSetting,accountLog

### 上线部署步骤

`内容为空,填写"-"即可`

|步骤|是否执行|执行内容|
|:----|:----:|:----|
|提醒前端提前合并代码到master和添加权限节点|-|是|
|执行sql语句|是|见下方"执行sql语句"|
|更新后端代码|是|-|
|composer安装|是|见下方"composer安装"|
|更新配置|-|见下方"更新配置"|
|创建队列|-|见下方"创建队列"|
|执行脚本|是|见下方"执行脚本"|
|删除redis缓存|-|见下方"删除redis缓存"|
|重启队列|-|上线前, 暂停所有队列, 上线后再重启|
|更新前端代码|-|是|
|添加定时任务|是|见下方"定时任务"|
|群内通知部署完毕|是|-|

#### 执行sql语句(按顺序执行)

- alter_data.sql

#### 执行脚本

- php timer_yii script/update-job-apply-handle-log
- php timer_yii script/update-resume-library-invite-log
- php timer_yii script/update-company-member-info
- php timer_yii script/update-job-apply-count
- php timer_yii script/update-job-apply-interview
- php timer_yii script/fix-job-contact
- php timer_yii script/update-resume-library-collect
- php timer_yii script/update-company-resume-library-member

