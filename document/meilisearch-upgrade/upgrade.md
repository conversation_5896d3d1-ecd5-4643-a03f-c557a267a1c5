# meilisearch版本升级

## 对迁移到新版本meilisearch做准备工作

##### 1、查看meilisearch的版本

```
    curl \
    -X GET 'http://localhost:7700/version' \
    -H 'Authorization: Bearer 0df84bbba7db46680e83843dd6454257161a282f'
```

执行后返回的结果-查看当前meilisearch的版本信息

```
    {
        "commitSha":"0df84bbba7db46680e83843dd6454257161a282f",
        "commitDate":"2024-06-27T12:47:08.000000000Z",
        "pkgVersion":"1.1.1"
    }
```

###### 2、查看meilisearch的索引

```
    curl \
    -X GET 'http://localhost:7700/indexes' \
    -H 'Authorization: Bearer 0df84bbba7db46680e83843dd6454257161a282f'
```

返回结果-查看当前meilisearch的索引信息

```
    ["*"]
```

##### 3、备份数据

```
    curl \
    -X POST 'http://localhost:7700/dumps' \
    -H 'Authorization: Bearer 4b953d62fbab81278324e71b4037eb06355dd49a'
```

返回结果-查看备份信息

```
    {
        "taskUid":21662,
        "indexUid":null,
        "status":"enqueued",
        "type":"dumpCreation",
        "enqueuedAt":"2024-07-18T03:56:21.416288Z"
    }
```

##### 4、停止meilisearch

##### 5、复制备份文件到新建tmp目录

官方采用移动的方式，这里采用复制的方式，复制到新建的tmp目录下

```
    # mv /path/to/your/meilisearch/directory/meilisearch/data.ms /tmp/
    # mv /path/to/your/meilisearch/directory/meilisearch /tmp/
    # 这里建议用cp命令采用复制
    cp -rp meilisearch tmp
```

##### 6、安装新版本

安装后最新版本是1.9.0版本

```
    curl -L https://install.meilisearch.com | sh
    chmod +x meilisearch
```

##### 7、启动新版本meilsearch导入数据

```
    ./meilisearch --import-dump ../tmp/meilisearch/dumps/20240718-035621423.dump --master-key="
    0df84bbba7db46680e83843dd6454257161a282f"
```



