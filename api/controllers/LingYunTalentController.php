<?php
/**
 * create user：shannon
 * create time：2024/8/20 16:38
 */
namespace api\controllers;

use api\models\LingYunTalent;
use Yii;

/**
 * 凌云英才专题页
 */
class LingYunTalentController extends BaseApiController
{
    /**
     * 实力单位
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCompanyList()
    {
        $companyIds = Yii::$app->request->get('companyIds');
        if (empty($companyIds)) {
            return $this->success([]);
        }
        if (!is_array($companyIds)) {
            $companyIds = explode(',', $companyIds);
        }

        // 获取在线直聊列表
        return $this->success(LingYunTalent::getCompanyList($companyIds));
    }

    /**
     * 全球招募人才--高层次人才
     */
    public function actionGlobalRecruit()
    {
        $params = Yii::$app->request->get();

        return $this->success(LingYunTalent::getGlobalRecruit($params));
    }

    /**
     * 全球招募人才--高技能人才
     */
    public function actionGlobalRecruitBySkill()
    {
        $params = Yii::$app->request->get();

        return $this->success(LingYunTalent::getGlobalRecruitBySkill($params));
    }

    /**
     * 海外人才良机
     */
    public function actionGlobalRecruitByAbroad()
    {
        return $this->success(LingYunTalent::getGlobalRecruitByAbroad());
    }

}