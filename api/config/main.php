<?php
$params = array_merge(require __DIR__ . '/../../common/config/params.php',
    require __DIR__ . '/../../common/config/params-local.php', require __DIR__ . '/params.php',
    require __DIR__ . '/params-local.php');

return [
    'id'                  => 'app-api',
    'basePath'            => dirname(__DIR__),
    'controllerNamespace' => 'api\controllers',
    'bootstrap'           => ['log'],
    'defaultRoute'        => 'home',

    'components' => [
        'urlManager'   => [
            'enablePrettyUrl' => true,
            'showScriptName'  => false,
        ],
        'errorHandler' => [
            'class' => 'api\components\CommonErrorHandler',
        ],
        'log'    => [
            'targets' => [
                [
                    'class'   => 'yii\log\FileTarget',
                    'levels'  => [
                        'error',
                        'warning',
                        'info',
                        'trace',
                    ],
                    'logVars' => [],

                    'logFile' => '@log/api/1.log',

                ],

            ],
        ],
    ],
    'params'     => $params,
];
