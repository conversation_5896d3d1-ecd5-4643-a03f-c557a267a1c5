<?php

namespace admin\controllers;

use admin\models\VerificationCodeLog;

class VerificationCodeLogController extends BaseAdminController
{
    /**
     * 获取数据
     */
    public function actionGetList()
    {
        return $this->success(VerificationCodeLog::getList(\Yii::$app->request->get()));
    }

    // 申请获取
    public function actionApply()
    {
        try {
            $request = \Yii::$app->request->post();
            $adminId = \Yii::$app->user->id;
            if (!$adminId) {
                throw new \Exception('非法访问');
            }

            $type              = $request['type'];
            $attachmentFileIds = $request['attachmentFileIds'];
            $remark            = $request['remark'];

            if (!$type) {
                throw new \Exception('请先选择类型');
            }

            if (!$attachmentFileIds) {
                throw new \Exception('请先上传附件');
            }

            if (!$remark) {
                throw new \Exception('请填写备注');
            }

            VerificationCodeLog::apply($request, $adminId);

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

}