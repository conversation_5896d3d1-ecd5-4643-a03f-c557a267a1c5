<?php

namespace admin\controllers;

use admin\models\ActivityFormIntentionOption;
use common\base\models\BaseActivityFormIntentionOption;
use common\base\models\BaseActivityFormOptionSign;
use common\base\models\BaseAdminDownloadTask;
use common\base\models\BaseUploadForm;
use common\helpers\ArrayHelper;
use common\service\downloadTask\ActivityFormOptionSignService;
use common\service\downloadTask\DownLoadTaskApplication;
use Yii;
use yii\base\Exception;
use yii\console\Response;

class ActivityFormIntentionOptionController extends BaseAdminController
{
    /**
     * 保存报名意向
     * @return Response|\yii\web\Response
     */
    public function actionSaveActivityFormIntentionOption()
    {
        $request = Yii::$app->request->post();
        $adminId = Yii::$app->user->id;
        try {
            if (!$adminId) {
                throw new Exception('非法访问');
            }

            return $this->success(['id' => ActivityFormIntentionOption::saveActivityFormIntentionOption($request)]);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取活动表单报名意向选项说明
     * @return Response|\yii\web\Response
     */
    public function actionGetTypeList()
    {
        try {
            return $this->success(ArrayHelper::obj22Arr(BaseActivityFormIntentionOption::TYPE_LIST));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取活动表单前端显示题目
     * @return Response|\yii\web\Response
     */
    public function actionGetShowMessageList()
    {
        try {
            return $this->success(ArrayHelper::obj22Arr(BaseActivityFormIntentionOption::SHOW_MESSAGE_LIST));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取活动表单活动场次信息
     * 传参数：activityFormId 活动表单id
     * @return Response|\yii\web\Response
     */
    public function actionGetIntentionOptionList()
    {
        try {
            $request = Yii::$app->request->get();
            if (!$request['activityFormId']) {
                return $this->fail('参数错误：活动表单id不能为空');
            }

            return $this->success(BaseActivityFormIntentionOption::getIntentionOptionList($request));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 导出活动场次签到信息
     * @return Response|\yii\web\Response
     */
    public function actionExportActivityFormOptionSign()
    {
        $request = Yii::$app->request->get();
        $adminId = Yii::$app->user->id;
        // 关闭内存限制
        ini_set('memory_limit', '1024M');
        // 关闭超时限制
        set_time_limit(0);
        try {
            if (!$adminId) {
                throw new Exception('非法访问');
            }
            $checkOne = BaseActivityFormOptionSign::findOne(['option_id' => $request['optionId']]);
            // 开始结束时间
            if (!$checkOne) {
                return $this->success('该场次暂无报名数据哦！');
            }
            $app = DownLoadTaskApplication::getInstance();
            $app->createAdmin($adminId, BaseAdminDownloadTask::TYPE_ACTIVITY_FORM_OPTION_SIGN, $request);

            return $this->success('数据开始导出,成功下载后会在企业微信通知,请后续留意');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取活动表单列表
     * @return Response|\yii\web\Response
     */
    public function actionGetFormOptionsList()
    {
        $request = Yii::$app->request->get();

        return $this->success([
            'list' => ArrayHelper::arr2KV(BaseActivityFormIntentionOption::getFormOptionsList($request), 'id', 'title'),
        ]);
    }
}