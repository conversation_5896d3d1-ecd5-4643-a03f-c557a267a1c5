<?php

namespace admin\controllers;

use admin\models\DeliveryInvite;
use admin\models\Equity;
use common\base\models\BaseResumeEquityActionRecord;
use common\base\models\BaseResumeEquitySetting;
use common\helpers\ArrayHelper;
use Yii;
use yii\base\Exception;

class EquityController extends BaseAdminController
{
    /**
     * 权益列表
     */
    public function actionIndex()
    {
        try {
            //获取参数
            $params = Yii::$app->request->get();
            //获取数据
            $data = Equity::index($params);

            $this->success($data);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 权益导出
     */
    public function actionExport()
    {
        try {
            //获取参数
            $params = Yii::$app->request->get();
            //获取数据
            Equity::export($params);

            return $this->success('数据开始导出,成功下载后会在企业微信通知,请后续留意');
            //            $this->success($data);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 权益筛选项
     */
    public function actionFilter()
    {
        return $this->success([
            'equity_filter'    => ArrayHelper::arr2KV(BaseResumeEquitySetting::getEquityFilterList(), 'id', 'name'),
            'operation_filter' => ArrayHelper::obj22Arr(BaseResumeEquityActionRecord::ACTION_TYPE_LIST),
        ]);
    }
}