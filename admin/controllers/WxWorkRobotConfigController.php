<?php

namespace admin\controllers;

use admin\models\SystemConfig;
use admin\models\WxWorkRobotConfig;
use Yii;

class WxWorkRobotConfigController extends BaseAdminController
{

    public function actionGetList()
    {
        return $this->success(WxWorkRobotConfig::getList(Yii::$app->request->get()));
    }

    public function actionAdd()
    {
        $transaction = Yii::$app->db->beginTransaction();

        // key name token 都是必须的
        $post = Yii::$app->request->post();
        if (empty($post['key']) || empty($post['name']) || empty($post['token'])) {
            return $this->fail('key name token 都是必须的');
        }

        try {
            WxWorkRobotConfig::create(Yii::$app->request->post());
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    public function actionLogList()
    {
        return $this->success(WxWorkRobotConfig::getLogList(Yii::$app->request->get()));
    }

    public function actionDelete()
    {
        $id = Yii::$app->request->post('id');

        if (empty($id)) {
            return $this->fail('id 不能为空');
        }

        $model = WxWorkRobotConfig::findOne($id);

        if (empty($model)) {
            return $this->fail('数据不存在');
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $model->delete();
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

}