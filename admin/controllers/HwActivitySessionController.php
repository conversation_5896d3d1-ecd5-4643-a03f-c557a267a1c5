<?php

namespace admin\controllers;

use admin\models\HwActivitySession;
use common\base\BaseActiveRecord;
use common\base\models\BaseHwActivitySession;
use Yii;
use yii\base\Exception;

class HwActivitySessionController extends BaseAdminController
{
    /**
     * 删除场次
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelSession()
    {
        $sessionId   = Yii::$app->request->post('id');
        $transaction = Yii::$app->db->beginTransaction();

        try {
            if (!$sessionId) {
                return $this->fail('缺少必填参数');
            }

            $model = BaseHwActivitySession::findOne($sessionId);
            if (!$model) {
                return $this->fail('活动场次不存在');
            }
            if ($model->status == BaseActiveRecord::STATUS_DELETE) {
                return $this->success();
            }
            BaseHwActivitySession::delSession($sessionId);
            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 设置排序
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSetSort()
    {
        $activityId  = Yii::$app->request->post('id');
        $sort        = Yii::$app->request->post('sort');
        $transaction = Yii::$app->db->beginTransaction();

        try {
            if (!$activityId || !$sort) {
                return $this->fail('缺少必填参数');
            }

            $model = BaseHwActivitySession::findOne($activityId);
            if (!$model) {
                return $this->fail('活动场次不存在');
            }
            BaseHwActivitySession::setSort($activityId, $sort);
            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 保存临时场次信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSaveTempData()
    {
        $request = Yii::$app->request->post();
        $adminId = Yii::$app->user->id;
        try {
            if (!$adminId) {
                throw new Exception('非法访问');
            }
            $id = BaseHwActivitySession::saveInfo($request);

            $sessionInfo = HwActivitySession::getEditInfo($id);

            return $this->success([$sessionInfo]);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

}