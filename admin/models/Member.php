<?php

namespace admin\models;

use common\base\models\BaseArea;
use common\base\models\BaseChatRoom;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyMemberOperationLog;
use common\base\models\BaseDictionary;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobSubscribe;
use common\base\models\BaseMember;
use common\base\models\BaseOffSiteJobApply;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAcademicBook;
use common\base\models\BaseResumeAcademicPage;
use common\base\models\BaseResumeAcademicPatent;
use common\base\models\BaseResumeAcademicReward;
use common\base\models\BaseResumeOtherReward;
use common\base\models\BaseResumeResearchDirection;
use common\base\models\BaseResumeSetting;
use common\base\models\BaseResumeTag;
use common\base\models\BaseResumeWxBind;
use common\helpers\IpHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\helpers\UUIDHelper;
use common\libs\Cache;
use common\libs\Excel;
use common\models\ResumeEducation;
use common\models\ResumeIntention;
use frontendPc\models\Dictionary;
use Yii;
use yii\base\Exception;

class Member extends BaseMember
{
    public static function getSearchPersonList($searchData)
    {
        $query = self::find()
            ->alias('m')
            ->where(['m.type' => Member::TYPE_PERSON])
            ->innerJoin(['r' => Resume::tableName()], 'r.member_id = m.id')
            ->leftJoin(['e' => ResumeEducation::tableName()], 'e.id = r.last_education_id')
            ->leftJoin(['s' => ResumeSetting::tableName()], 's.resume_id = r.id')
            ->leftJoin(['w' => ResumeWork::tableName()], 'w.id = r.last_work_id')
            //            ->leftJoin(['i' => ResumeIntention::tableName()], 'i.resume_id = r.id')
            ->leftJoin(['rs' => ResumeStatData::tableName()], 'rs.resume_id = r.id')
            //            ->leftJoin(['osja' => BaseOffSiteJobApply::tableName()], 'osja.resume_id = r.id')
            ->leftJoin(['jar' => BaseJobApplyRecord::tableName()], 'jar.resume_id = r.id and jar.delivery_type = 1')
            ->leftJoin(['b' => BaseResumeWxBind::tableName()], 'b.resume_id = r.id')
            ->leftJoin(['js' => BaseJobSubscribe::tableName()], 'js.resume_id = r.id')
            ->leftJoin(['cr' => BaseChatRoom::tableName()], 'cr.resume_member_id = m.id');

        //是否绑定微信
        if ($searchData['isWxBind'] == 1) {
            $query->andFilterWhere([
                '>',
                'b.id',
                0,
            ]);
        } elseif ($searchData['isWxBind'] == 2) {
            $null = new \Yii\db\Expression('null');
            $query->andFilterWhere([
                'is',
                'b.id',
                $null,
            ]);
        }

        //人才检索
        if (strlen($searchData['userKeyword']) == 8 && !empty(UUIDHelper::decryption($searchData['userKeyword']))) {
            $userKeyWordId = UUIDHelper::decryption($searchData['userKeyword']);
            if ($userKeyWordId) {
                $query->andFilterWhere([
                    '=',
                    'r.id',
                    $userKeyWordId,

                ]);
            }
        } else {
            $query->andFilterWhere([
                'or',
                [
                    'like',
                    'r.name',
                    $searchData['userKeyword'],
                ],
                [
                    'like',
                    'm.username',
                    $searchData['userKeyword'],
                ],

            ]);
        }

        //最高学历
        if ($searchData['educationId']) {
            $educationId = BaseDictionary::getEducationValueByKey($searchData['educationId']);
            $query->andWhere(['e.education_id' => $educationId]);
        }

        if ($searchData['majorId']) {
            // 有时候前端会传一个空数组,或者第一个元素为空的,需要重置一下
            if (is_array($searchData['majorId'])) {
                if ($searchData['majorId'][0] == '') {
                    $searchData['majorId'] = [];
                }
            } else {
                // 正常情况下都是逗号隔开的
                $searchData['majorId'] = explode(',', $searchData['majorId']);
            }
        }

        //学科专业
        $query->andFilterWhere(['e.major_id' => $searchData['majorId']]);

        //毕业时间
        if ($searchData['graduateBeginDate'] && $searchData['graduateEndDate']) {
            $query->andFilterWhere([
                'between',
                'e.end_date',
                TimeHelper::dayToBeginTime($searchData['graduateBeginDate']),
                TimeHelper::dayToEndTime($searchData['graduateEndDate']),
            ]);
        }

        // 毕业院校
        $query->andFilterWhere([
            'like',
            'e.school',
            $searchData['school'],
        ]);

        //毕业院校
        $query->andFilterWhere([
            'like',
            'e.school',
            $searchData['schoolName'],
        ]);

        //获取工作年限
        //        $workYearInfo = \Yii::$app->params['workYearsList'][$searchData['workYears']];
        $workYearInfo = BaseDictionary::WORK_YEARS_LIST[$searchData['workYears']];
        $query->andFilterWhere([
            'between',
            'r.work_experience',
            $workYearInfo['min'],
            $workYearInfo['max'],
        ]);

        //求职状态
        $query->andFilterWhere(['r.work_status' => $searchData['workStatus']]);
        //户籍国籍
        if (!empty($searchData['householdRegisterId'])) {
            $query->andWhere(['r.household_register_id' => explode(',', $searchData['householdRegisterId'])]);
        }
        //注册时间
        $query->andFilterWhere([
            'between',
            'm.add_time',
            TimeHelper::dayToBeginTime($searchData['startCreateTime']),
            TimeHelper::dayToEndTime($searchData['endCreateTime']),
        ]);

        //最近登录时间
        $query->andFilterWhere([
            'between',
            'm.last_login_time',
            TimeHelper::dayToBeginTime($searchData['startLastLoginTime']),
            TimeHelper::dayToEndTime($searchData['endLastLoginTime']),
        ]);

        //最近活跃时间
        $query->andFilterWhere([
            'between',
            'm.last_active_time',
            TimeHelper::dayToBeginTime($searchData['startLastActiveTime']),
            TimeHelper::dayToEndTime($searchData['endLastActiveTime']),
        ]);
        //最近更新时间（最后投递时间）
        $query->andFilterWhere([
            'between',
            'r.last_update_time',
            TimeHelper::dayToBeginTime($searchData['startLastUpdateTime']),
            TimeHelper::dayToEndTime($searchData['endLastUpdateTime']),
        ]);

        if ($searchData['startComplete'] && $searchData['startComplete'] != 0) {
            $query->andWhere([
                '>=',
                'r.complete',
                $searchData['startComplete'],
            ]);
        }

        if ($searchData['endComplete'] && $searchData['endComplete'] != 100) {
            $query->andWhere([
                '<=',
                'r.complete',
                $searchData['endComplete'],
            ]);
        }

        //职称
        if (!empty($searchData['titleId'])) {
            $titleIdArr = explode(',', $searchData['titleId']);
            $query->andWhere(['r.title_id' => $titleIdArr]);
        }

        //统计需要子查询的数量
        $childrenQueryNum = 0;
        //意向职能
        if (!empty($searchData['jobCategory'])) {
            $memberIdArr1     = ResumeIntention::find()
                ->where(['job_category_id' => $searchData['jobCategory']])
                ->select('member_id')
                ->groupBy('member_id')
                ->asArray()
                ->column();
            $childrenQueryNum += 1;
        }
        //意向城市
        if (!empty($searchData['cityId'])) {
            $cityArr   = explode(',', $searchData['cityId']);
            $cityQuery = self::formatFindInSetQuery($cityArr, 'area_id');

            if ($cityQuery) {
                $memberIdArr2     = ResumeIntention::find()
                    ->where($cityQuery)
                    ->select('member_id')
                    ->groupBy('member_id')
                    ->asArray()
                    ->column();
                $childrenQueryNum += 1;
            }
        }
        //工作性质
        if (!empty($searchData['natureType'])) {
            $memberIdArr3     = ResumeIntention::find()
                ->where(['nature_type' => $searchData['natureType']])
                ->select('member_id')
                ->groupBy('member_id')
                ->asArray()
                ->column();
            $childrenQueryNum += 1;
        }

        //薪资要求
        if (!empty($searchData['wageId'])) {
            $wageInfo       = Dictionary::getMinAndMaxWage($searchData['wageId']);
            $intentionQuery = ResumeIntention::find()
                ->where(1);

            //面议
            if ($wageInfo['min'] == 0 && $wageInfo['max'] == 0) {
                $intentionQuery->andWhere([
                    'min_wage' => 0,
                ]);
                $intentionQuery->andWhere([
                    'max_wage' => 0,
                ]);
            } else {
                if ($wageInfo['min'] > 0) {
                    $intentionQuery->andWhere([
                        '>=',
                        'min_wage',
                        (int)$wageInfo['min'],
                    ]);
                }

                if ($wageInfo['max'] > 0) {
                    $intentionQuery->andWhere([
                        '<=',
                        'max_wage',
                        (int)$wageInfo['max'],
                    ]);
                }
                //除了以上条件，还必须保证两个不能同事为空
                $intentionQuery->andWhere([
                    'or',
                    [
                        '>',
                        'max_wage',
                        0,
                    ],
                    [
                        '>',
                        'min_wage',
                        0,
                    ],
                ]);
            }

            $memberIdArr4 = $intentionQuery->select('member_id')
                ->groupBy('member_id')
                ->asArray()
                ->column();

            $childrenQueryNum += 1;
        }

        //所在地区
        if (!empty($searchData['areaId'])) {
            $areaIdArr = explode(',', $searchData['areaId']);
            $areaQuery = self::formatFindInSetQuery($areaIdArr, 'residence');

            if ($areaQuery) {
                $memberIdArr5     = Resume::find()
                    ->where($areaQuery)
                    ->select('member_id')
                    ->groupBy('member_id')
                    ->asArray()
                    ->column();
                $childrenQueryNum += 1;
            }
        }
        //海外经历查询
        if (!empty($searchData['isAbroad'])) {
            // 这里考虑了一下兼容2的情况
            $isAbroad = $searchData['isAbroad'] == 1 ? 1 : 0;

            $memberIdArr6 = ResumeWork::find()
                ->where(['is_abroad' => $isAbroad])
                ->select('member_id')
                ->groupBy('member_id')
                ->asArray()
                ->column();

            // 还有海外教育经历
            $memberIdArr7 = ResumeEducation::find()
                ->where(['is_abroad' => $isAbroad])
                ->select('member_id')
                ->groupBy('member_id')
                ->asArray()
                ->column();

            $memberIdArr6 = array_merge($memberIdArr6, $memberIdArr7);

            $childrenQueryNum += 1;
        }

        $memberIdArr  = [];
        $intersectNum = 0;
        if (count($memberIdArr1) > 0) {
            $intersectNum  += 1;
            $memberIdArr[] = $memberIdArr1;
        }
        if (count($memberIdArr2) > 0) {
            $intersectNum  += 1;
            $memberIdArr[] = $memberIdArr2;
        }
        if (count($memberIdArr3) > 0) {
            $intersectNum  += 1;
            $memberIdArr[] = $memberIdArr3;
        }
        if (count($memberIdArr4) > 0) {
            $intersectNum  += 1;
            $memberIdArr[] = $memberIdArr4;
        }
        if (count($memberIdArr5) > 0) {
            $intersectNum  += 1;
            $memberIdArr[] = $memberIdArr5;
        }
        if (count($memberIdArr6) > 0) {
            $intersectNum  += 1;
            $memberIdArr[] = $memberIdArr6;
        }

        //如果数组数量大于1，取交集
        if ($intersectNum > 1) {
            //如果数组超过2组数组元素，取交集
            $memberIdArr = call_user_func_array('array_intersect', $memberIdArr);
        } elseif ($intersectNum == 1) {
            //如果刚好等于1，取第一个
            $memberIdArr = $memberIdArr[0];
        }
        //否则不做处理，即空数组

        //如果子查询数量大于0，说明有查询，此时需把条件加入查询
        if ($childrenQueryNum > 0) {
            //联合三个条件，查询用户id
            $query->andWhere([
                'in',
                'm.id',
                $memberIdArr,
            ]);
        } else {
            $query->andFilterWhere([
                'in',
                'm.id',
                $memberIdArr,
            ]);
        }

        //到岗时间
        $query->andFilterWhere(['r.arrive_date_type' => $searchData['arriveDateType']]);

        //是否开通代投
        $query->andFilterWhere(['s.is_proxy_deliver' => $searchData['isProxyDeliver']]);
        //手机号码
        $query->andFilterWhere(['m.mobile_code' => StringHelper::getMobileCodeNumber($searchData['mobileCode'])]);
        $query->andFilterWhere(['m.mobile' => $searchData['mobile']]);
        //求职者身份选择（-1缺失，1职场人，2应届生）
        if (!empty($searchData['identityType'])) {
            $query->andWhere(['r.identity_type' => $searchData['identityType']]);
        }
        //简历开放状态
        $query->andFilterWhere(['s.is_hide_resume' => $searchData['isResumeStatus']]);
        //会员类型
        if (strlen($searchData['vipType']) > 0) {
            if ($searchData['vipType'] == 2) {
                $query->andWhere([
                    'r.vip_type'  => BaseResume::VIP_TYPE_ACTIVE,
                    'r.vip_level' => BaseResume::VIP_LEVEL_DIAMOND,
                ]);
            } elseif ($searchData['vipType'] == 1) {
                $query->andWhere([
                    'r.vip_type'  => $searchData['vipType'],
                    'r.vip_level' => BaseResume::VIP_LEVEL_GOLD,
                ]);
            } else {
                $query->andWhere(['r.vip_type' => $searchData['vipType'],]);
            }
        }

        //海外经历
        // $query->andFilterHaving(['w.is_abroad' => $searchData['isAbroad']]);

        //985/211
        $query->andFilterWhere(['e.is_project_school' => $searchData['isProjectSchool']]);

        //博士后经历 - 优化：直接使用resume表的冗余字段
        $query->andFilterWhere(['r.is_postdoc' => $searchData['isPostdoc']]);

        //年龄
        if (!empty($searchData['ageId'])) {
            $wageInfo = Resume::getMixAndMaxAge($searchData['ageId']);
            if ($wageInfo['min'] > 0) {
                $query->andWhere([
                    '>=',
                    'r.age',
                    (int)$wageInfo['min'],
                ]);
            }

            if ($wageInfo['max'] > 0) {
                $query->andWhere([
                    '<=',
                    'r.age',
                    (int)$wageInfo['max'],
                ]);
            }
        }

        //        //职称
        //        $query->andFilterHaving(['r.title_id' => $searchData['titleId']]);

        //性别
        $query->andFilterWhere(['r.gender' => $searchData['gender']]);

        //注册来源
        $query->andFilterWhere(['m.source_type' => $searchData['sourceType']]);
        //是否开启订阅条件
        if ($searchData['isSubscribe'] == 1) {
            $query->andWhere([
                '>',
                'js.id',
                0,
            ]);
        } elseif ($searchData['isSubscribe'] == 2) {
            $query->andWhere([
                'js.id' => null,
            ]);
        }
        //刷新时间
        if (!empty($searchData['refreshTimeStart']) && !empty($searchData['refreshTimeEnd'])) {
            $query->andWhere([
                'between',
                'r.refresh_time',
                TimeHelper::dayToBeginTime($searchData['refreshTimeStart']),
                TimeHelper::dayToEndTime($searchData['refreshTimeEnd']),
            ]);
        }

        // textCombination 研究方向/学术成果/荣誉奖励  researchDirection
        // academicAchievement
        // reward
        if (!empty($searchData['textCombination']) && empty($searchData['researchDirection']) && empty($searchData['academicAchievement']) && empty($searchData['reward'])) {
            $query->leftJoin(['rrd' => BaseResumeResearchDirection::tableName()], 'rrd.member_id = m.id');
            // 学术成果 = 学术论文 学术专利 学术专著
            $query->leftJoin(['rap' => BaseResumeAcademicPage::tableName()], 'rap.member_id = m.id');
            $query->leftJoin(['rra' => BaseResumeAcademicPatent::tableName()], 'rra.member_id = m.id');
            $query->leftJoin(['rrb' => BaseResumeAcademicBook::tableName()], 'rrb.member_id = m.id');
            // 荣誉奖励 = 学术奖励 其他荣誉
            $query->leftJoin(['rar' => BaseResumeAcademicReward::tableName()], 'rar.member_id = m.id');
            $query->leftJoin(['rro' => BaseResumeOtherReward::tableName()], 'rro.member_id = m.id');

            $query->andFilterWhere([
                'or',
                [
                    'like',
                    'rrd.content',
                    $searchData['textCombination'],
                ],
                [
                    'like',
                    'rap.description',
                    $searchData['textCombination'],
                ],
                [
                    'like',
                    'rra.description',
                    $searchData['textCombination'],
                ],
                [
                    'like',
                    'rrb.name',
                    $searchData['textCombination'],
                ],
                [
                    'like',
                    'rar.name',
                    $searchData['textCombination'],
                ],
                [
                    'like',
                    'rro.name',
                    $searchData['textCombination'],
                ],
            ]);
        }

        // 研究方向 researchDirection
        if (!empty($searchData['researchDirection'])) {
            $query->innerJoin(['rrd' => BaseResumeResearchDirection::tableName()], 'rrd.member_id = m.id');
            $query->andFilterWhere([
                'like',
                'rrd.content',
                $searchData['researchDirection'],
            ]);
        }

        // 学术成果 academicAchievement
        if (!empty($searchData['academicAchievement'])) {
            // 学术成果 = 学术论文 学术专利 学术专著
            $query->innerJoin(['rap' => BaseResumeAcademicPage::tableName()], 'rap.member_id = m.id');
            $query->innerJoin(['rra' => BaseResumeAcademicPatent::tableName()], 'rra.member_id = m.id');
            $query->innerJoin(['rrb' => BaseResumeAcademicBook::tableName()], 'rrb.member_id = m.id');

            $query->andFilterWhere([
                'or',
                [
                    'like',
                    'rap.description',
                    $searchData['academicAchievement'],
                ],
                [
                    'like',
                    'rra.description',
                    $searchData['academicAchievement'],
                ],
                [
                    'like',
                    'rrb.name',
                    $searchData['academicAchievement'],
                ],
            ]);
        }

        // 荣誉奖励 reward
        if (!empty($searchData['reward'])) {
            // 荣誉奖励 = 学术奖励 其他荣誉
            $query->innerJoin(['rar' => BaseResumeAcademicReward::tableName()], 'rar.member_id = m.id');
            $query->innerJoin(['rro' => BaseResumeOtherReward::tableName()], 'rro.member_id = m.id');

            $query->andFilterWhere([
                'or',
                [
                    'like',
                    'rar.name',
                    $searchData['reward'],
                ],
                [
                    'like',
                    'rro.name',
                    $searchData['reward'],
                ],
            ]);
        }

        // 人才标签
        if (!empty($searchData['resumeTagIds'])) {
            $resumeTagArr = explode(',', $searchData['resumeTagIds']);
            $query->innerJoin(['rt' => BaseResumeTag::tableName()], 'rt.resume_id = r.id');
            $query->andFilterWhere([
                'rt.tag_id' => $resumeTagArr,
            ]);
        }

        $query->select([
            'm.id as memberId',
            'm.status',
            'w.is_abroad',
            'r.id as resumeId',
            'r.title_id',
            'w.is_postdoc',
            'r.name',
            'm.username',
            'r.age',
            'r.gender',
            'e.major_id',
            'e.education_id',
            's.is_hide_resume',
            'r.work_experience',
            'r.household_register_id',
            'r.work_status',
            'r.arrive_date',
            'r.arrive_date_type',
            'r.vip_type',
            'r.vip_level',
            'r.vip_expire_time',
            'm.add_time as createTime',
            'm.last_login_time',
            'm.last_active_time',
            'm.source_type',
            'e.school',
            'r.last_apply_job_time',
            'r.member_id',
            'r.refresh_time',
            //            'i.area_id',
            'r.last_update_time',
            'rs.on_site_apply_amount',
            'rs.off_site_apply_amount',
            'rs.interview_record_amount',
            'rs.resume_download_amount',
            'rs.resume_library_download_amount as resumeLibraryDownloadAmount',
            'rs.resume_library_collect_amount as resumeLibraryCollectAmount',
            'r.native_place_area_id',
            //            'i.nature_type',
            'sum(if(jar.delivery_way=3,1,0)) as linkCount',
            'sum(if(jar.delivery_way=2,1,0)) as emailCount',
            'b.id as bindId',
            'js.id as subscribeId',
            'm.is_chat',
            'COUNT(CASE WHEN cr.creator_type=' . BaseChatRoom::CREATOR_TYPE_PERSON . ' THEN 1 END)  as ownChatNum',
            'COUNT(CASE WHEN cr.creator_type=' . BaseChatRoom::CREATOR_TYPE_COMPANY . ' THEN 1 END)  as otherChatNum',
        ])
            ->groupBy('r.id');
        $count    = $query->count();
        $sortText = '';
        //根据创建时间排序
        if (!empty($searchData['sortCreateTime'])) {
            if ($searchData['sortCreateTime'] == Member::SORT_CREATE_TIME_ASC) {
                $sortText = 'm.add_time asc';
            } elseif ($searchData['sortCreateTime'] == Member::SORT_CREATE_TIME_DESC) {
                $sortText = 'm.add_time desc';
            }
        }
        //根据最近登陆时间排序
        if (!empty($searchData['sortLastLoginTime'])) {
            if ($searchData['sortLastLoginTime'] == Member::SORT_LAST_LOGIN_TIME_ASC) {
                $sortText = 'm.last_login_time asc';
            } elseif ($searchData['sortLastLoginTime'] == Member::SORT_LAST_LOGIN_TIME_DESC) {
                $sortText = 'm.last_login_time desc';
            }
        }
        //根据最近活跃时间排序
        if (!empty($searchData['sortLastActiveTime'])) {
            if ($searchData['sortLastActiveTime'] == Member::SORT_LAST_LOGIN_TIME_ASC) {
                $sortText = 'm.last_active_time asc';
            } elseif ($searchData['sortLastActiveTime'] == Member::SORT_LAST_LOGIN_TIME_DESC) {
                $sortText = 'm.last_active_time desc';
            }
        }

        //根据最近更新时间排序
        if (!empty($searchData['sortLastUpdateTime'])) {
            if ($searchData['sortLastUpdateTime'] == Member::SORT_LAST_LOGIN_TIME_ASC) {
                $sortText = 'r.last_update_time asc';
            } elseif ($searchData['sortLastUpdateTime'] == Member::SORT_LAST_LOGIN_TIME_DESC) {
                $sortText = 'r.last_update_time desc';
            }
        }

        //简历刷新时间排序
        if (!empty($searchData['sortRefreshTime'])) {
            if ($searchData['sortRefreshTime'] == Member::SORT_LAST_LOGIN_TIME_ASC) {
                $sortText = 'r.refresh_time asc';
            } elseif ($searchData['sortRefreshTime'] == Member::SORT_LAST_LOGIN_TIME_DESC) {
                $sortText = 'r.refresh_time desc';
            }
        }

        //根据站内投递数量排序
        if (!empty($searchData['sortOnSiteApplyAmount'])) {
            if ($searchData['sortOnSiteApplyAmount'] == self::SORT_ON_SITE_APPLY_AMOUNT_ASC) {
                $sortText = 'rs.on_site_apply_amount asc';
            } elseif ($searchData['sortOnSiteApplyAmount'] == self::SORT_ON_SITE_APPLY_AMOUNT_DESC) {
                $sortText = 'rs.on_site_apply_amount desc';
            }
        }

        //根据站外投递数量排序
        if (!empty($searchData['sortOffSiteApplyAmount'])) {
            if ($searchData['sortOffSiteApplyAmount'] == self::SORT_OFF_SITE_APPLY_AMOUNT_ASC) {
                $sortText = 'rs.off_site_apply_amount asc';
            } elseif ($searchData['sortOffSiteApplyAmount'] == self::SORT_OFF_SITE_APPLY_AMOUNT_DESC) {
                $sortText = 'rs.off_site_apply_amount desc';
            }
        }

        //根据约面数量排序
        if (!empty($searchData['sortInterviewAmount'])) {
            if ($searchData['sortInterviewAmount'] == self::SORT_INTERVIEW_AMOUNT_ASC) {
                $sortText = 'rs.interview_record_amount asc';
            } elseif ($searchData['sortInterviewAmount'] == self::SORT_INTERVIEW_AMOUNT_DESC) {
                $sortText = 'rs.interview_record_amount desc';
            }
        }

        //根据下载数量排序
        if (!empty($searchData['sortDownloadAmount'])) {
            if ($searchData['sortDownloadAmount'] == self::SORT_RESUME_DOWNLOAD_AMOUNT_ASC) {
                $sortText = 'rs.resume_download_amount asc';
            } elseif ($searchData['sortDownloadAmount'] == self::SORT_RESUME_DOWNLOAD_AMOUNT_DESC) {
                $sortText = 'rs.resume_download_amount desc';
            }
        }

        //根据人才库收藏数量排序
        if (!empty($searchData['sortResumeLibraryCollectAmount'])) {
            if ($searchData['sortResumeLibraryCollectAmount'] == self::SORT_RESUME_DOWNLOAD_AMOUNT_ASC) {
                $sortText = 'rs.resume_library_collect_amount asc';
            } elseif ($searchData['sortResumeLibraryCollectAmount'] == self::SORT_RESUME_DOWNLOAD_AMOUNT_DESC) {
                $sortText = 'rs.resume_library_collect_amount desc';
            }
        }

        //根据人才库下载数量排序
        if (!empty($searchData['sortResumeLibraryDownloadAmount'])) {
            if ($searchData['sortResumeLibraryDownloadAmount'] == self::SORT_RESUME_DOWNLOAD_AMOUNT_ASC) {
                $sortText = 'rs.resume_library_download_amount asc';
            } elseif ($searchData['sortResumeLibraryDownloadAmount'] == self::SORT_RESUME_DOWNLOAD_AMOUNT_DESC) {
                $sortText = 'rs.resume_library_download_amount desc';
            }
        }

        // 更加简历完整度排序
        if (!empty($searchData['sortComplete'])) {
            if ($searchData['sortComplete'] == self::SORT_RESUME_COMPLETE_ASC) {
                $sortText = 'r.complete asc';
            } elseif ($searchData['sortComplete'] == self::SORT_RESUME_COMPLETE_DESC) {
                $sortText = 'r.complete desc';
            }
        }

        // 会员过期时间排序
        if (!empty($searchData['sortVipExpireTime'])) {
            if ($searchData['sortVipExpireTime'] == BaseMember::SORT_ASC) {
                $sortText = 'r.vip_expire_time asc';
            } elseif ($searchData['sortVipExpireTime'] == BaseMember::SORT_DESC) {
                $sortText = 'r.vip_expire_time desc';
            }
        }

        // 站外投递（邮件）排序
        if (!empty($searchData['sortEmailCount'])) {
            if ($searchData['sortEmailCount'] == self::SORT_EMAIL_COUNT_ASC) {
                $sortText = 'emailCount asc';
            } elseif ($searchData['sortEmailCount'] == self::SORT_EMAIL_COUNT_DESC) {
                $sortText = 'emailCount desc';
            }
        }

        // 站外投递（链接）排序
        if (!empty($searchData['sortLinkCount'])) {
            if ($searchData['sortLinkCount'] == self::SORT_LINK_COUNT_ASC) {
                $sortText = 'linkCount asc';
            } elseif ($searchData['sortLinkCount'] == self::SORT_LINK_COUNT_DESC) {
                $sortText = 'linkCount desc';
            }
        }

        // 主/被动次数排序
        if (!empty($searchData['sortOwnChatNum'])) {
            if ($searchData['sortOwnChatNum'] == self::SORT_ASC) {
                $sortText = 'ownChatNum asc';
            } elseif ($searchData['sortOwnChatNum'] == self::SORT_DESC) {
                $sortText = 'ownChatNum desc';
            }
        }

        if (!empty($searchData['sortOtherChatNum'])) {
            if ($searchData['sortOtherChatNum'] == self::SORT_ASC) {
                $sortText = 'otherChatNum asc';
            } elseif ($searchData['sortOtherChatNum'] == self::SORT_DESC) {
                $sortText = 'otherChatNum desc';
            }
        }

        if (!empty($sortText) && empty($searchData['sortCreateTime'])) {
            $sortText = $sortText . ',m.add_time desc';
        }
        if (empty($sortText)) {
            $sortText = 'm.add_time desc';
        }

        $query->orderBy($sortText);

        $pageSize = $searchData['pageSize'] ?: \Yii::$app->params['defaultPageSize'];

        $pages = self::setPage($count, $searchData['page'], $pageSize);

        $list = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->asArray()
            ->all();

        foreach ($list as &$record) {
            $genderName        = Resume::getGenderName($record['gender']) ?: '-';                       //性别
            $educationName     = Dictionary::getEducationName($record['education_id']) ?: '-';          //学历
            $majorName         = Major::getMajorName($record['major_id']) ?: '-';                       //专业
            $nativePlaceAreaId = Area::getAreaName($record['native_place_area_id']) ?: '-';           //户籍
            $age               = $record['age'] ? $record['age'] . '岁' : '-';                          //年龄
            //            $workExperience         = $record['work_experience'] ? $record['work_experience'] . '年' : '-';  //工作经验
            $identityExperienceText = BaseResume::getIdentityExperienceText($record['resumeId']) ?: '-';
            $record['baseInfo']     = $age . '/' . $genderName . '/' . $educationName . '/' . $identityExperienceText . '/' . $majorName . '/' . $nativePlaceAreaId;
            //获取简历完成度
            $record['resumeCompletePercent'] = BaseResume::getComplete($record['member_id']) . '%';
            $record['uid']                   = UUIDHelper::encrypt(UUIDHelper::TYPE_PERSON, $record['resumeId']);

            //            $record['workStatusTxt'] = $jobStatusList[$record['work_status']] ?: '';
            $record['sourceTypeTxt'] = self::SOURCE_TYPE_LIST[$record['source_type']];
            if ($record['vip_type'] == BaseResume::VIP_TYPE_ACTIVE) {
                $record['vip_type_name'] = BaseResume::VIP_LEVEL_LIST[$record['vip_level']];
            } else {
                $record['vip_type_name'] = BaseResume::VIP_TYPE_LIST[$record['vip_type']] ?: '';
            }
            $record['vip_expire_time'] = ($record['vip_expire_time'] != '0000-00-00 00:00:00' && !empty($record['vip_expire_time'])) ? $record['vip_expire_time'] : '';

            //获取到岗时间
            $workStatus = Dictionary::getJobStatusName($record['work_status']);
            if ($record['arrive_date_type'] != Resume::CUSTOM_ARRIVE_DATE_TYPE) {
                $arriveDate = Dictionary::getArriveDateName($record['arrive_date_type']);
            } else {
                $arriveDate = $record['arrive_date'];
            }
            $record['workStatusTxt'] = $workStatus . '-' . $arriveDate;
            $record['bindTxt']       = BaseResumeWxBind::checkWxBind($record['resumeId']) ? '已绑定' : '未绑定';
            //是否开启职位订阅
            if ($record['subscribeId'] > 0) {
                $record['isSubscribe'] = '是';
            } else {
                $record['isSubscribe'] = '否';
            }
            //简历类型
            $resume_type_data          = BaseResume::getResumeLevel($record['resumeId']);
            $record['resume_type_tag'] = $resume_type_data['name'];
            //简历开放状态
            $record['resume_status_name'] = $record['is_hide_resume'] == BaseResumeSetting::IS_HIDE_RESUME_YES ? '否' : '是';
            //主、被动发起直聊次数统计
            //$record['own_chat_num']   = BaseChatRoom::getCreatorTypeCount($record['memberId']);
            //$record['other_chat_num'] = BaseChatRoom::getCreatorTypeCount($record['memberId'],
            //    BaseChatRoom::CREATOR_TYPE_COMPANY);
        }

        $data = [
            'list' => $list,
            'page' => [
                'limit' => $pages['limit'],
                'count' => (int)$count,
                'page'  => $pages['page'],
            ],
        ];

        return $data;
    }

    /**
     * 统计用户数据
     * @return array
     */
    public static function getPersonStatisticsData(): array
    {
        //获取人才总数
        $data['personTotalAmount'] = Member::find()
            ->where(['type' => Member::TYPE_PERSON])
            ->andWhere(['status' => Member::STATUS_ACTIVE])
            ->count();

        $resumeStatData = ResumeStatData::find()
            ->where(['status' => ResumeStatData::STATUS_ACTIVE])
            ->select([
                'sum(on_site_apply_amount) as onSiteApplyTotalAmount',
                'sum(off_site_apply_amount) as offSiteApplyTotalAmount',
                'sum(interview_record_amount) as interviewTotalAmount',
            ])
            ->asArray()
            ->one();
        //统计站外投递
        $offApplyData = BaseOffSiteJobApply::find()
            ->select([
                'sum(if(email="",1,0)) as linkCount',
                'sum(if(email="",0,1)) as emailCount',
            ])
            ->andWhere(['source' => BaseOffSiteJobApply::SOURCE_WEBSITE])
            ->asArray()
            ->one();
        $data         = array_merge($data, $resumeStatData, $offApplyData);

        return $data;
    }

    /**
     * 日志列表
     * @param $params
     * @throws Exception
     */
    public static function getLogList($params)
    {
        if (empty($params['id'])) {
            throw new Exception('ID必须');
        }
        //获取账号信息
        $member = self::findOne($params['id']);

        $query = MemberActionLog::find()
            ->alias('mal');
        //        if ($params['logType'] == 2) {
        //            $query->andFilterWhere([
        //                'mal.member_id' => $params['id'],
        //            ]);
        //        } else {
        //            //获取当前单位信息
        //            $company_member_info = BaseCompanyMemberInfo::findOne(['member_id' => $params['id']]);
        //            if ($company_member_info->company_member_type == BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN) {
        //                $query->andWhere(['cmi.company_id' => $company_member_info->company_id]);
        //            } else {
        //                $query->andWhere(['mal.member_id' => $params['id']]);
        //            }
        //        }

        if ($member->type == BaseMember::TYPE_PERSON) {
            $query->andFilterWhere([
                'mal.member_id' => $params['id'],
            ]);
            $select = [
                'mal.add_time',
                'mal.content',
                'mal.ip',
                'mal.platform',
            ];
        } else {
            $query->leftJoin(['cmi' => BaseCompanyMemberInfo::tableName()], 'cmi.member_id=mal.member_id');
            $select = [
                'mal.add_time',
                'mal.content',
                'mal.ip',
                'mal.platform',
                'cmi.contact',
                'cmi.department',
            ];
            if (isset($params['isCompany']) && $params['isCompany'] == 2) {
                $query->andWhere(['mal.member_id' => $params['id'],]);
            } else {
                $company_member_info = BaseCompanyMemberInfo::findOne(['member_id' => $params['id']]);
                $query->andWhere(['cmi.company_id' => $company_member_info->company_id]);
            }
            $query->andFilterWhere([
                'like',
                'cmi.contact',
                $params['contact'],
            ]);
        }

        if ($params['isLogTab'] == MemberActionLog::IS_LOGIN_YES) {
            $query->andWhere([
                'mal.is_login' => MemberActionLog::IS_LOGIN_YES,
            ]);
        } else {
            $query->andWhere([
                'mal.is_login' => MemberActionLog::IS_LOGIN_NO,
            ]);
        }
        $query->andFilterWhere([
            'like',
            'mal.content',
            $params['keyword'],
        ]);
        $count    = $query->count();
        $pageSize = $params['pageSize'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $params['page'], $pageSize);

        $list = $query->offset($pages['offset'])
            ->select($select)
            ->limit($pages['limit'])
            ->orderBy('mal.add_time desc')
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            if (!empty($item['ip'])) {
                $item['ip']           = long2ip($item['ip']);
                $item['ascriptionIp'] = IpHelper::getFullAreaByIp($item['ip']);
            }
        }

        return [
            'list'  => $list,
            'pages' => [
                'size'  => (int)$pageSize,
                'total' => (int)$count,
            ],
        ];
    }

    /**
     * 禁用账号
     * @param $idj
     */
    public function lockAccount($memberId, $adminId)
    {
        $member = self::findOne($memberId);

        if (!$member) {
            throw new Exception('账号不存在');
        }

        // TODO 这里应该再做一些账号相关的检查

        $member->status = self::STATUS_ILLEGAL;
        if (!$member->save()) {
            throw new Exception($member->getFirstErrorsMessage());
        }
        if ($member->type == self::TYPE_COMPANY) {
            //获取单位信息
            $company_info = BaseCompany::findOne(['member_id' => $member->id]);
            //主账号禁用日志
            $main_data = [
                'member_id'         => $memberId,
                'company_id'        => $company_info->id,
                'type'              => BaseCompanyMemberOperationLog::OPERATION_TYPE_STATUS,
                'operation_id'      => $adminId,
                'operation_port'    => BaseCompanyMemberOperationLog::OPERATION_PORT_ADMIN,
                'operation_content' => $member->company_member_type == BaseMember::COMPANY_MEMBER_TYPE_MAIN ? '禁用单位' : '禁用子账号',
            ];
            BaseCompanyMemberOperationLog::addLog($main_data);

            //禁用成功后判断一下账号类型
            //1、单位主账号
            if ($member->company_member_type == BaseMember::COMPANY_MEMBER_TYPE_MAIN) {
                //禁用主账号下所有子账号
                $subMemberList = BaseCompanyMemberInfo::find()
                    ->where(['company_id' => $company_info->id])
                    ->andWhere(['company_member_type' => BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_SUB])
                    ->asArray()
                    ->all();

                //禁用子账号
                $member_ids = array_column($subMemberList, 'member_id');
                if (count($member_ids) > 0) {
                    BaseMember::updateAll(['status' => self::STATUS_ILLEGAL], ['id' => $member_ids]);
                    //循环写入子账号日志
                    foreach ($subMemberList as $subMember) {
                        $sub_data = [
                            'member_id'         => $subMember['member_id'],
                            'company_id'        => $company_info->id,
                            'type'              => BaseCompanyMemberOperationLog::OPERATION_TYPE_STATUS,
                            'operation_id'      => $adminId,
                            'operation_port'    => BaseCompanyMemberOperationLog::OPERATION_PORT_ADMIN,
                            'operation_content' => '禁用子账号',
                        ];
                        BaseCompanyMemberOperationLog::addLog($sub_data);
                    }
                }
            }
        }
    }

    /**
     * 启用账号
     * @param $idj
     */
    public function unlockAccount($memberId, $adminId)
    {
        $member = self::findOne($memberId);

        if (!$member) {
            throw new Exception('账号不存在');
        }
        if ($member->type == self::TYPE_COMPANY) {
            //获取账号信息
            $company_member_info = BaseCompanyMemberInfo::findOne(['member_id' => $memberId]);
            //获取单位信息
            $company_info = BaseCompany::findOne($company_member_info->company_id);
            if ($member->company_member_type == BaseMember::COMPANY_MEMBER_TYPE_SUB) {
                //要看一下主账号是不是启用的
                $mainMember = BaseMember::findOne(['id' => $company_info->member_id]);
                if ($mainMember->status == self::STATUS_ILLEGAL) {
                    throw new Exception('主账号已被禁用，无法启用子账号');
                }
            }
            $member->status = self::STATUS_ACTIVE;
            if (!$member->save()) {
                throw new Exception($member->getFirstErrorsMessage());
            }
            //日志
            $main_data = [
                'member_id'         => $memberId,
                'company_id'        => $company_info->id,
                'type'              => BaseCompanyMemberOperationLog::OPERATION_TYPE_STATUS,
                'operation_id'      => $adminId,
                'operation_port'    => BaseCompanyMemberOperationLog::OPERATION_PORT_ADMIN,
                'operation_content' => $member->company_member_type == BaseMember::COMPANY_MEMBER_TYPE_MAIN ? '启用单位' : '启用子账号',
            ];
            BaseCompanyMemberOperationLog::addLog($main_data);

            //启用成功后判断一下账号类型
            //1、单位主账号
            if ($member->company_member_type == BaseMember::COMPANY_MEMBER_TYPE_MAIN) {
                //启用主账号下所有子账号
                $subMemberList = BaseCompanyMemberInfo::find()
                    ->select('')
                    ->where(['company_id' => $company_info->id])
                    ->andWhere(['company_member_type' => BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_SUB])
                    ->asArray()
                    ->all();

                //启用子账号
                $member_ids = array_column($subMemberList, 'member_id');
                if (count($member_ids) > 0) {
                    BaseMember::updateAll(['status' => self::STATUS_ACTIVE], ['id' => $member_ids]);
                    //循环写入子账号日志
                    foreach ($subMemberList as $subMember) {
                        $sub_data = [
                            'member_id'         => $subMember['member_id'],
                            'company_id'        => $company_info->id,
                            'type'              => BaseCompanyMemberOperationLog::OPERATION_TYPE_STATUS,
                            'operation_id'      => $adminId,
                            'operation_port'    => BaseCompanyMemberOperationLog::OPERATION_PORT_ADMIN,
                            'operation_content' => '启用子账号',
                        ];
                        BaseCompanyMemberOperationLog::addLog($sub_data);
                    }
                }
            }
        } else {
            $member->status = self::STATUS_ACTIVE;
            if (!$member->save()) {
                throw new Exception($member->getFirstErrorsMessage());
            }
        }
    }

    /**
     * 注册单位列表
     * 已完成帐号注册，尚未提交单位资质认证的单位。
     * @param $params
     * @return array
     */
    public static function getRegisterSearchList($params)
    {
        $query = self::find()
            ->alias('m')
            ->leftJoin(['cia' => CompanyInfoAuth::tableName()], 'cia.member_id = m.id')
            ->where(['cia.audit_status' => CompanyInfoAuth::AUDIT_STATUS_NO]);

        //单位名称
        $query->andFilterWhere([
            'like',
            'cia.full_name',
            $params['fullName'],
        ]);

        //所在地区
        if ($params['area']) {
            $query->andFilterWhere([
                'in',
                'cia.city_id',
                $params['area'],
            ]);
        }

        //联系号码
        $query->andFilterWhere([
            'or',
            [
                'm.mobile' => $params['mobile'],
            ],
            [
                'cia.mobile' => $params['mobile'],
            ],
        ]);

        //单位性质
        $query->andFilterWhere([
            'cia.nature' => $params['nature'],
        ]);
        //单位类型
        $query->andFilterWhere([
            'cia.type' => $params['type'],
        ]);
        //所属行业
        if ($params['industryId']) {
            $query->andFilterWhere([
                'in',
                'cia.industry_id',
                $params['industryId'],
            ]);
        }
        //注册邮箱
        $query->andFilterCompare('m.email', $params['email'], 'like');
        //注册时间
        if ($params['addTimeFrom'] && $params['addTimeTo']) {
            $query->andFilterWhere([
                'between',
                'm.add_time',
                TimeHelper::dayToBeginTime($params['addTimeFrom']),
                TimeHelper::dayToEndTime($params['addTimeTo']),
            ]);
        }
        $orderBy = 'cia.add_time desc';
        // 单位列表头排序
        if ($params['sortAddTime'] == self::ORDER_BY_DESC) {
            $orderBy = 'cia.add_time desc';
        } else {
            if ($params['sortAddTime'] == self::ORDER_BY_ASC) {
                $orderBy = 'cia.add_time asc';
            }
        }

        $select = [
            'm.username',
            'm.add_time',
            'm.mobile as mMobile',
            'm.email as mEmail',
            'cia.mobile as cMobile',
            'cia.email as cEmail',
            'cia.full_name',
            'cia.contact',
            'cia.department',
            'cia.address',
            'cia.telephone',
            'cia.nature',
            'cia.type',
            'cia.industry_id',
            'cia.member_id',
            'cia.province_id',
            'cia.city_id',
            'cia.district_id',
            'cia.telephone',
        ];

        $count    = $query->count();
        $pageSize = $params['pageSize'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $params['page'], $pageSize);

        $list = $query->offset($pages['offset'])
            ->select($select)
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        //获取地区表缓存
        $cache     = Yii::$app->cache;
        $areaCache = $cache->get(Cache::PC_ALL_AREA_TABLE_KEY);
        if (!$areaCache) {
            $areaCache = BaseArea::setAreaCache();
        }

        foreach ($list as &$item) {
            $item['natureTxt']   = Dictionary::getCompanyNatureName($item['nature']);
            $item['typeTxt']     = Dictionary::getCompanyTypeName($item['type']);
            $item['industryTxt'] = Trade::getIndustryName($item['industry_id']);
            $item['address']     = $areaCache[$item['province_id']]['name'] . $areaCache[$item['city_id']]['name'] . $item['address'];
            $item['mMobile']     = $item['mMobile'] ?: '-';
            $item['mEmail']      = $item['mEmail'] ?: '-';
            $item['cMobile']     = $item['cMobile'] ?: '-';
            $item['cEmail']      = $item['cEmail'] ?: '-';
        }

        // 判断是否导出
        if ($params['export']) {
            $headers = [
                '单位名称',
                '帐号',
                '联系人',
                '所在部门',
                '固定电话',
                '注册手机',
                '注册邮箱',
                '联系手机',
                '联系邮箱',
                '单位性质',
                '单位类型',
                '所属行业',
                '注册时间',
                '所在地',
            ];

            $data = [];
            foreach ($list as $val) {
                $data[] = [
                    $val['full_name'] ?: '',
                    $val['username'] ?: '',
                    $val['contact'] ?: '',
                    $val['department'] ?: '',
                    $val['telephone'] ?: '',
                    $val['mMobile'] ?: '-',
                    $val['mEmail'] ?: '-',
                    $val['cMobile'] ?: '-',
                    $val['cEmail'] ?: '-',
                    $val['natureTxt'] ?: '',
                    $val['typeTxt'] ?: '',
                    $val['industryTxt'] ?: '',
                    $val['addTime'] ?: '',
                    $val['address'] ?: '',
                ];
            }

            $excel    = new Excel();
            $fileName = $excel->export($data, $headers);

            return [
                'excelUrl' => $fileName,
            ];
        } else {
            return [
                'list'  => $list,
                'pages' => [
                    'size'  => (int)$pageSize,
                    'total' => (int)$count,
                ],
            ];
        }
    }
}
