<?php

namespace admin\models;

use common\base\models\BaseFriendLinkConfig;
use common\libs\Cache;
use Yii;
use common\base\BaseActiveRecord;
use common\helpers\FormatConverter;
use yii\base\Exception;

class FriendLinkConfig
{
    /**
     * 获取友情链接列表
     */
    public static function index($params)
    {
        //格式化参数
        $params = FormatConverter::convertHump($params);
        //query句柄
        $query = BaseFriendLinkConfig::find();
        //模糊搜索链接标题
        if (!empty($params['title'])) {
            $query->andWhere([
                'like',
                'title',
                $params['title'],
            ]);
        }
        if (!empty($params['status'])) {
            $query->andWhere(['status' => $params['status']]);
        }
        //总数
        $total = $query->count();
        //排序
        $order = 'status asc,sort_number desc,add_time desc';
        //分页
        //获取分页参数
        $page_size = $params['page_size'] ?: Yii::$app->params['defaultPageSize'];
        $pages     = BaseActiveRecord::setPage($total, $params['page'], $page_size);
        //获取数据
        $data = $query->select([
            'id',
            'title',
            'link_url',
            'status',
            'add_time',
            'update_time',
            'sort_number',
            'is_nofollow',
        ])
            ->orderBy($order)
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->asArray()
            ->all();
        //循环处理数据
        foreach ($data as &$value) {
            //状态文本
            $value['status_text']      = BaseFriendLinkConfig::STATUS_LIST_TEXT[$value['status']];
            $value['is_nofollow_text'] = BaseFriendLinkConfig::IS_NOFOLLOW_LIST_TEXT[$value['is_nofollow']];
        }

        return [
            'data'  => $data,
            'pages' => [
                'total' => (int)$total,
                'limit' => (int)$page_size,
                'page'  => (int)$pages['page'],
            ],
        ];
    }

    /**
     * 友情链接添加
     * @param $params
     * @return bool
     * @throws Exception
     */
    public static function add($params)
    {
        //格式化参数
        $params = FormatConverter::convertHump($params);
        //标题不允许为空
        if (empty($params['title'])) {
            throw new Exception('链接标题不能为空');
        }
        //链接不允许为空
        if (empty($params['link_url'])) {
            throw new Exception('链接不能为空');
        }
        //判断链接是否是一个有效的url
        if (!filter_var($params['link_url'], FILTER_VALIDATE_URL)) {
            throw new Exception('链接格式不正确');
        }
        //sort_number不为空时候必须是一个数字
        if (!empty($params['sort_number']) && !is_numeric($params['sort_number'])) {
            throw new Exception('排序必须是一个数字');
        }
        //添加数据
        $model = new BaseFriendLinkConfig();
        //链接标题
        $model->title = $params['title'];
        //链接
        $model->link_url    = $params['link_url'] ?: '';
        $model->sort_number = $params['sort_number'] ?: 0;
        $model->status      = BaseFriendLinkConfig::STATUS_SHOW;
        $model->is_nofollow = $params['is_nofollow'] ?: BaseFriendLinkConfig::IS_NOFOLLOW_NO;
        //添加时间
        $model->add_time = CUR_DATETIME;
        //修改时间
        $model->update_time = CUR_DATETIME;

        //保存及返回
        if (!$model->save()) {
            throw new Exception('添加失败');
        }

        //操作一下缓存先删除后更新
        $cache_key = Cache::PC_HOME_FRIEND_LINK_KEY;
        Cache::delete($cache_key);
        BaseFriendLinkConfig::getList();

        return true;
    }

    /**
     * 友情链接编辑
     * @param $params
     * @return bool
     * @throws Exception
     */
    public static function edit($params)
    {
        //格式化参数
        $params = FormatConverter::convertHump($params);
        if ($params['id'] <= 0) {
            throw new Exception('参数错误');
        }
        //模板记录存不存在
        $model = BaseFriendLinkConfig::findOne($params['id']);
        if (empty($model)) {
            throw new Exception('记录不存在');
        }
        //标题不允许为空
        if (empty($params['title'])) {
            throw new Exception('链接标题不能为空');
        }
        //链接不允许为空
        if (empty($params['link_url'])) {
            throw new Exception('链接不能为空');
        }
        //判断链接是否是一个有效的url
        if (!filter_var($params['link_url'], FILTER_VALIDATE_URL)) {
            throw new Exception('链接格式不正确');
        }
        //sort_number不为空时候必须是一个数字
        if (!empty($params['sort_number']) && !is_numeric($params['sort_number'])) {
            throw new Exception('排序必须是一个数字');
        }
        //链接标题
        $model->title = $params['title'];
        //链接
        $model->link_url    = $params['link_url'] ?: '';
        $model->sort_number = $params['sort_number'] ?: 0;
        $model->is_nofollow = $params['is_nofollow'] ?: BaseFriendLinkConfig::IS_NOFOLLOW_NO;
        //修改时间
        $model->update_time = CUR_DATETIME;

        //保存及返回
        if (!$model->save()) {
            throw new Exception('操作失败');
        }
        //操作一下缓存先删除后更新
        $cache_key = Cache::PC_HOME_FRIEND_LINK_KEY;
        Cache::delete($cache_key);
        BaseFriendLinkConfig::getList();

        return true;
    }

    /**
     * 友情链接编辑初始化
     * @param $id
     * @return array|\yii\db\ActiveRecord|null
     * @throws Exception
     */
    public static function editInit($id)
    {
        if ($id <= 0) {
            throw new Exception('参数错误');
        }
        $info = BaseFriendLinkConfig::findOne($id);
        if (empty($info)) {
            throw new Exception('模板记录不存在');
        }

        return $info;
    }

    /**
     * 友情链接删除
     * @param $id
     * @param $status
     * @return bool
     * @throws Exception
     */
    public static function status($id, $status)
    {
        if ($id <= 0 || !in_array($status, BaseFriendLinkConfig::STATUS_LIST)) {
            throw new Exception('参数错误');
        }
        //记录存不存在
        $model = BaseFriendLinkConfig::findOne($id);
        if (empty($model)) {
            throw new Exception('记录不存在');
        }
        //状态
        $model->status = $status;
        //修改时间
        $model->update_time = CUR_DATETIME;

        //保存及返回
        if (!$model->save()) {
            throw new Exception('操作失败');
        }
        //操作一下缓存先删除后更新
        $cache_key = Cache::PC_HOME_FRIEND_LINK_KEY;
        Cache::delete($cache_key);
        BaseFriendLinkConfig::getList();

        return true;
    }
}