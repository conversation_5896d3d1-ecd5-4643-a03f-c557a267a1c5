<?php

namespace admin\models;

use common\base\models\BaseAdminDownloadTask;
use Yii;

class DownloadTask extends BaseAdminDownloadTask
{

    /**
     * @param $adminId
     * @param $params
     */
    public static function getList($adminId, $params)
    {
        $query = self::find()
            ->select('id, type, status, add_time,begin_time,finish_time')
            ->where(['admin_id' => $adminId]);

        $count    = $query->count();
        $pageSize = $params['pageSize'] ?: Yii::$app->params['defaultPageSize'];

        $pages = self::setPage($count, $params['page'], $pageSize);

        $list = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('id desc')
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['statusTxt'] = self::STATUS_LIST[$item['status']];
            $item['typeTxt']   = self::TYPE_LIST[$item['type']];
        }

        return [
            'list'  => $list,
            'pages' => [
                'size'  => (int)$pageSize,
                'total' => (int)$count,
            ],
        ];
    }

    public static function getUrl($id)
    {
        $model = self::findOne($id);

        if (!$model) {
            return '';
        }

        return $model->url;
    }
}
