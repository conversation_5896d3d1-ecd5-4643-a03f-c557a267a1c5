<?php

namespace admin\models;

use common\base\models\BaseArea;
use common\base\models\BaseChatCommonGreetingSystem;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyGroup;
use common\base\models\BaseCompanyGroupScoreSystem;
use common\base\models\BaseCompanyInfoAuth;
use common\base\models\BaseCompanyMemberConfig;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyMemberMessageConfig;
use common\base\models\BaseCompanyMemberOperationLog;
use common\base\models\BaseCompanyPackageChangeLog;
use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseCompanyPackageSystemConfig;
use common\base\models\BaseMember;
use common\base\models\BaseMemberLoginForm;
use common\base\models\BaseMemberMessage;
use common\helpers\FileHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\helpers\ValidateHelper;
use common\libs\Cache;
use common\libs\EmailQueue;
use common\libs\Excel;
use common\libs\SmsQueue;
use common\service\companyPackage\ConfigureService;
use queue\MeilisearchJob;
use queue\Producer;
use Yii;
use yii\base\Exception;

class CompanyInfoAuth extends BaseCompanyInfoAuth
{
    /**
     * 单位新增
     * @param $data
     * @throws Exception
     */
    public static function create($data)
    {
        if (empty($data['fullName'])) {
            throw new Exception('单位名称不能为空');
        }
        if (empty($data['type'])) {
            // throw new Exception('请选择单位类型不');
        }
        if (empty($data['nature'])) {
            // throw new Exception('请选择单位性质');
        }
        if (empty($data['groupIds'])) {
            throw new Exception('请选择单位群组');
        }

        // 判断是合作单位
        if ($data['isCooperation'] == Company::COOPERATIVE_UNIT_YES) {
            if (empty($data['industryId'])) {
                throw new Exception('请选择所属行业');
            }
            if (empty($data['area'])) {
                throw new Exception('请选择地区');
            }
            if (empty($data['address'])) {
                throw new Exception('请填写详细地址');
            }
            if (empty($data['contact'])) {
                throw new Exception('请填写联系人');
            }
            if (empty($data['department'])) {
                throw new Exception('请填写所在部门');
            }
            if (empty($data['email']) && empty($data['mobile'])) {
                throw new Exception('邮箱与手机号二者必填其一');
            }
            if (empty($data['licensePath'])) {
                throw new Exception('请填单位资质认证');
            }
            if (empty($data['personInfoPath'])) {
                throw new Exception('请填经办人身份证明路径');
            }
            if (empty($data['deliveryType'])) {
                throw new Exception('请选择投递配置');
            }
        }

        $memberModel          = new Member();
        $companyInfoAuthModel = new self();
        $companyModel         = new Company();

        if ($data['telephone']) {
            $companyInfoAuthModel->telephone = $data['telephone'];
        }
        if (!empty($data['email'])) {
            if (!ValidateHelper::isEmail($data['email'])) {
                throw new Exception('邮箱格式错误');
            }
        }
        if (!empty($data['mobile'])) {
            if (!ValidateHelper::isMobileCN($data['mobile'])) {
                throw new Exception('联系人手机号格式错误');
            }
        }

        if ($data['email']) {
            $inEmail = $memberModel::findOne([
                'email'                 => $data['email'],
                'type'                  => Member::TYPE_COMPANY,
                'email_register_status' => Member::EMAIL_REGISTER_STATUS_NORMAL,
            ]);
            if ($inEmail) {
                throw new Exception('该邮箱已被使用');
            }
        }

        if ($data['mobile']) {
            $inMobile = $memberModel::findOne([
                'mobile' => $data['mobile'],
                'type'   => Member::TYPE_COMPANY,
            ]);
            if ($inMobile) {
                throw new Exception('该手机号码已被使用');
            }
        }

        if (Company::findOne(['full_name' => trim($data['fullName'])])) {
            throw new Exception('该单位名称已存在');
        }

        // 判断是合作单位
        if ($data['isCooperation'] == Company::COOPERATIVE_UNIT_YES) {
            $memberModel->status  = Member::STATUS_AUDIT;
            $companyModel->status = self::AUDIT_STATUS_FIRST_WAIT;//等待复审
        }
        $memberModel->status = Member::STATUS_NO_COOPERATION;// 非合作单位帐号状态
        $memberModel->type   = Member::TYPE_COMPANY;
        $memberModel->mobile = $data['mobile'] ?: '';
        //预留参数，没有就取默认值
        $memberModel->mobile_code           = StringHelper::getMobileCodeNumber($data['mobileCode']) ?: BaseMemberLoginForm::DEFAULT_MOBILE_CODE;
        $memberModel->username              = Member::createUserName();
        $memberModel->password              = Member::COMPANY_USER_PASSWORD;
        $memberModel->email                 = $data['email'] ?: '';
        $memberModel->source_type           = Member::SOURCE_TYPE_PC;
        $memberModel->email_register_status = Member::EMAIL_REGISTER_STATUS_NORMAL;
        $memberModel->is_chat_window        = BaseMember::IS_CHAT_WINDOW_NO;
        $memberModel->is_greeting           = BaseMember::IS_GREETING_YES;
        $memberModel->greeting_type         = BaseMember::GREETING_TYPE_SYSTEM;
        $memberModel->greeting_default_id   = BaseChatCommonGreetingSystem::getDefaultId(BaseMember::TYPE_COMPANY);
        if (!$memberModel->save()) {
            throw new Exception($memberModel->getFirstErrorsMessage());
        }
        // 新增会员的ID
        $memberInsertId = $memberModel->attributes['id'];

        /**
         * 新增单位基础信息
         */
        $companyModel->member_id       = $memberInsertId;
        $companyModel->is_cooperation  = $data['isCooperation'];
        $companyModel->source_type     = Company::TYPE_SOURCE_ADD;
        $companyModel->status          = self::AUDIT_STATUS_SECOND_WAIT;
        $companyModel->full_name       = '';
        $companyModel->admin_id        = $data['adminId'];
        $companyModel->type            = $data['type'];
        $companyModel->nature          = $data['nature'];
        $companyModel->industry_id     = $data['industryId'];
        $companyModel->province_id     = $data['area'][0] ?: 0;
        $companyModel->city_id         = $data['area'][1] ?: 0;
        $companyModel->district_id     = $data['area'][2] ?: 0;
        $companyModel->address         = $data['address'] ?: '';
        $companyModel->contact         = $data['contact'] ?: '';
        $companyModel->department      = $data['department'] ?: '';
        $companyModel->telephone       = $data['telephone'] ?: '';
        $companyModel->create_admin_id = Yii::$app->user->id;
        $companyModel->account_nature  = $data['accountNature'] ?: 0;
        $companyModel->delivery_type   = $data['deliveryType'];
        $companyModel->is_hide         = BaseCompany::IS_HIDE_NO;
        $companyModel->is_miniapp      = BaseCompany::IS_MINIAPP_NO;
        $companyModel->is_manual_tag   = BaseCompany::IS_MANUAL_TAG_NONE;
        //群组处理
        $groupScoreSystemId                  = BaseCompanyGroupScoreSystem::getSystemScoreId($data['groupIds']);
        $companyModel->group_score_system_id = $groupScoreSystemId;

        if (!$companyModel->save()) {
            throw new Exception($companyModel->getFirstErrorsMessage());
        }
        //写入分组
        BaseCompany::editGroup($companyModel->id, $data['groupIds']);
        // 新增单位的ID
        $companyInsertId = $companyModel->attributes['id'];
        $rule_model      = new RuleCompany();
        $company_bool    = $rule_model->exec($companyInsertId);
        $company_info    = BaseCompany::findOne($companyInsertId);
        if ($company_bool) {
            $company_info->is_miniapp = BaseCompany::IS_MINIAPP_YES;
        }
        if (!$company_info->save()) {
            throw new Exception($company_info->getFirstErrorsMessage());
        }
        /**
         * 会员地址
         */
        $memberAddress = new MemberAddress();

        $memberAddress->member_id   = $memberInsertId;
        $memberAddress->province_id = $data['areaId'][0] ?: 0;
        $memberAddress->city_id     = $data['areaId'][1] ?: 0;
        $memberAddress->district_id = $data['areaId'][2] ?: 0;
        $memberAddress->detail      = $data['address'];
        if (!$memberAddress->save()) {
            throw new Exception($memberAddress->getFirstErrorsMessage());
        }

        // 判断是合作单位
        if ($data['isCooperation'] == Company::COOPERATIVE_UNIT_YES) {
            /**
             * 新增审核信息
             */
            $companyInfoAuthModel->member_id         = $memberInsertId;
            $companyInfoAuthModel->company_id        = $companyInsertId;
            $companyInfoAuthModel->audit_status      = self::AUDIT_STATUS_FIRST_WAIT;
            $companyInfoAuthModel->phase             = self::PHASE_THREE;//第三阶段
            $companyInfoAuthModel->full_name         = trim($data['fullName']);
            $companyInfoAuthModel->type              = $data['type'];
            $companyInfoAuthModel->nature            = $data['nature'];
            $companyInfoAuthModel->industry_id       = $data['industryId'];
            $companyInfoAuthModel->province_id       = $data['area'][0] ?: 0;
            $companyInfoAuthModel->city_id           = $data['area'][1] ?: 0;
            $companyInfoAuthModel->district_id       = $data['area'][2] ?: 0;
            $companyInfoAuthModel->address           = $data['address'] ?: '';
            $companyInfoAuthModel->contact           = $data['contact'] ?: '';
            $companyInfoAuthModel->department        = $data['department'] ?: '';
            $companyInfoAuthModel->mobile            = $data['mobile'] ?: '';
            $companyInfoAuthModel->email             = $data['email'] ?: '';
            $companyInfoAuthModel->license_path      = $data['licensePath'] ?: '';
            $companyInfoAuthModel->person_info_path  = $data['personInfoPath'] ?: '';
            $companyInfoAuthModel->telephone         = $data['telephone'] ?: '';
            $companyInfoAuthModel->submit_audit_time = CUR_DATETIME;
            if (!$companyInfoAuthModel->save()) {
                throw new Exception($companyInfoAuthModel->getFirstErrorsMessage());
            }
            //初始化单位账号信息
            //这里处理一下账号信息
            $insert_data = [
                'member_id'           => $memberInsertId,
                'company_id'          => $companyInsertId,
                'contact'             => $data['contact'],
                'department'          => $data['department'],
                'member_rule'         => BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_SUPER,
                'company_member_type' => BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN,
                'create_id'           => Yii::$app->user->id,
                'source_type'         => BaseCompany::TYPE_SOURCE_ADD,
                'is_wx_bind'          => BaseCompanyMemberInfo::IS_WX_BIND_NO,
            ];
            BaseCompanyMemberInfo::add($insert_data);
        }

        // 判断非合作单位
        if ($data['isCooperation'] == Company::COOPERATIVE_UNIT_NO) {
            /**
             * 新增联系信息部分
             */
            $companyContactModel             = new CompanyContact();
            $companyContactModel->member_id  = $memberInsertId;
            $companyContactModel->company_id = $companyInsertId;
            $companyContactModel->status     = CompanyContact::STATUS_ACTIVE;
            $companyContactModel->name       = $data['contact'] ?: '';
            $companyContactModel->department = $data['department'] ?: '';
            $companyContactModel->mobile     = $data['mobile'] ?: '';
            $companyContactModel->telephone  = $data['telephone'] ?: '';
            $companyContactModel->email      = $data['email'] ?: '';
            if (!$companyContactModel->save()) {
                throw new Exception($companyContactModel->getFirstErrorsMessage());
            }
        }
    }

    /**
     * 文件模版上传
     * @param $fileObj
     * @return false
     * @throws Exception
     */
    public static function importTemplate($fileObj)
    {
        if ($fileObj) {
            $path = 'uploads/template/' . 'company_unit_no_template.' . $fileObj->extension;

            if ($fileObj->extension != 'xlsx') {
                throw new Exception('仅支持xlsx文件导入');
            }

            if (!$fileObj->saveAs($path)) {
                throw new Exception('上传成功');
            } else {
                throw new Exception('上传失败');
            }
        } else {
            return false;
        }
    }

    /**
     * 非合作单位批量导入数据
     */
    public static function companyImport($filePath)
    {
        $fieldArr = [
            '单位名称'        => 'full_name',
            '单位类型'        => 'type',
            '单位性质'        => 'nature',
            '一级行业类型'    => 'industry_id',
            '二级行业类型'    => 'industry_child_id',
            '单位地址省级'    => 'province_id',
            '单位地址市级'    => 'city_id',
            '单位地址区/县级' => 'district_id',
            '单位详细地址'    => 'address',
            '联系人'          => 'contact',
            '所在部门'        => 'department',
            '联系电话'        => 'mobile',
            '联系邮箱'        => 'email',
            '固定电话'        => 'telephone',
        ];

        if (!$filePath) {
            throw new Exception('文件路径为空');
        }

        $excel  = new Excel();
        $insert = $excel->import($filePath, $fieldArr);
        if (empty($insert)) {
            throw new Exception('Excel数据为空');
        }

        $count = 0;
        foreach ($insert as $key => &$item) {
            $company        = new Company();
            $member         = new Member();
            $companyContact = new CompanyContact();

            $key = $key + 2;
            if (empty($item['full_name'])) {
                throw new Exception('第' . $key . '行，单位名称不能为空');
            }
            if (empty($item['type'])) {
                // throw new Exception('第' . $key . '行，单位类型不能为空');
            }
            if (empty($item['nature'])) {
                throw new Exception('第' . $key . '行，单位性质不能为空');
            }
            if (!empty($item['email']) && !ValidateHelper::isEmail($item['email'])) {
                throw new Exception('第' . $key . '行，邮箱格式错误');
            }
            if (!empty($item['mobile']) && !ValidateHelper::isMobileCN($item['mobile'])) {
                throw new Exception('第' . $key . '行，联系人手机号格式错误');
            }
            $inFullName = $company::findOne(['full_name' => $item['full_name']]);
            if ($inFullName) {
                throw new Exception('第' . $key . '行，该单位名称已被使用');
            }

            // 省市区
            $provinceId = Area::findOneVal([
                'like',
                'name',
                $item['province_id'],
                ['level' => 1],
            ], 'id') ?: 0;
            $cityId     = Area::findOneVal([
                'like',
                'name',
                $item['city_id'],
                ['level' => 2],
            ], 'id') ?: 0;
            $districtId = Area::findOneVal([
                'like',
                'name',
                $item['district_id'],
                ['level' => 3],
            ], 'id') ?: 0;

            unset($province, $city, $district);

            $item['is_cooperation'] = $company::COOPERATIVE_UNIT_NO;
            $item['type']           = Dictionary::findOneVal(['name' => $item['type']], 'code') ?: '';
            $item['nature']         = Dictionary::findOneVal(['name' => $item['nature']], 'code') ?: '';
            $item['industry_id']    = Trade::findOneVal(['name' => $item['industry_child_id']], 'id') ?: '';

            /**
             * 新增会员信息(非合作单位帐号处于禁止登陆状态)
             */
            $member->status              = Member::STATUS_NO_COOPERATION;
            $member->type                = Member::TYPE_COMPANY;
            $member->username            = Member::createUserName();
            $member->password            = Member::COMPANY_USER_PASSWORD;
            $member->source_type         = Member::SOURCE_TYPE_PC;
            $member->is_chat_window      = BaseMember::IS_CHAT_WINDOW_NO;
            $member->is_greeting         = BaseMember::IS_GREETING_YES;
            $member->greeting_type       = BaseMember::GREETING_TYPE_SYSTEM;
            $member->greeting_default_id = BaseChatCommonGreetingSystem::getDefaultId(BaseMember::TYPE_COMPANY);
            if (!$member->save()) {
                throw new Exception($member->getFirstErrorsMessage());
            }
            // 新增会员的ID
            $memberInsertId = $member->attributes['id'];

            //新增单位基础信息
            $company->member_id      = $memberInsertId;
            $company->is_cooperation = Company::COOPERATIVE_UNIT_NO;
            $company->status         = $company::STATUS_ACTIVE;
            $company->source_type    = $company::TYPE_SOURCE_ADD;
            $company->full_name      = trim($item['full_name']);
            $company->type           = $item['type'];
            $company->nature         = $item['nature'];
            $company->province_id    = $provinceId ?: 0;
            $company->city_id        = $cityId ?: 0;
            $company->district_id    = $districtId ?: 0;
            $company->industry_id    = $item['industry_id'];
            $company->address        = $item['address'] ?: '';
            $company->contact        = $item['contact'] ?: '';
            $company->department     = $item['department'] ?: '';
            $company->delivery_type  = BaseCompany::DELIVERY_TYPE_OUTER;
            $company->is_hide        = BaseCompany::IS_HIDE_NO;
            $company->is_miniapp     = BaseCompany::IS_MINIAPP_NO;
            $company->is_manual_tag  = BaseCompany::IS_MANUAL_TAG_NONE;
            //群组处理
            $groupScoreSystemId             = BaseCompanyGroupScoreSystem::getSystemScoreId(BaseCompanyGroup::COOPERATIVE_GROUP_NON_ID);
            $company->group_score_system_id = $groupScoreSystemId;
            if (!$company->save($item)) {
                throw new Exception($company->getFirstErrorsMessage());
            }
            BaseCompany::editGroup($company->id, BaseCompanyGroup::COOPERATIVE_GROUP_NON_ID);
            $companyInsertId = $company->attributes['id'];
            $rule_model      = new RuleCompany();
            $company_bool    = $rule_model->exec($companyInsertId);
            $company_info    = BaseCompany::findOne($companyInsertId);
            if ($company_bool) {
                $company_info->is_miniapp = BaseCompany::IS_MINIAPP_YES;
            }
            if (!$company_info->save()) {
                throw new Exception($company_info->getFirstErrorsMessage());
            }
            //新增联系信息部分
            $companyContact->member_id  = $memberInsertId;
            $companyContact->company_id = $companyInsertId;
            $companyContact->status     = $company::STATUS_ACTIVE;
            $companyContact->name       = $item['contact'] ?: '';
            $companyContact->department = $item['department'] ?: '';
            $companyContact->mobile     = (string)$item['mobile'] ?: '';
            $companyContact->telephone  = (string)$item['telephone'] ?: '';
            $companyContact->email      = (string)$item['email'] ?: '';
            if (!$companyContact->save($item)) {
                throw new Exception($companyContact->getFirstErrorsMessage());
            }

            // 会员地址
            $memberAddress = new MemberAddress();

            $memberAddress->member_id   = $memberInsertId;
            $memberAddress->province_id = $item['areaId'][0] ?: 0;
            $memberAddress->city_id     = $item['areaId'][1] ?: 0;
            $memberAddress->district_id = $item['areaId'][2] ?: 0;
            $memberAddress->detail      = $item['address'];
            if (!$memberAddress->save()) {
                throw new Exception($memberAddress->getFirstErrorsMessage());
            }

            $count++;
        }
        unlink($filePath);

        return ['msgTxt' => '成功批量导入' . $count . '条数据'];
    }

    /**
     * 免费单位新增(帐号处于禁止登陆状态)
     * @param $data
     * @throws Exception
     */
    public static function freeCreate($data)
    {
        if (empty($data['fullName'])) {
            throw new Exception('单位名称不能为空');
        }
        if (empty($data['type'])) {
            // throw new Exception('单位类型不能为空');
        }
        if (empty($data['nature'])) {
            // throw new Exception('请选择单位性质');
        }
        if (!empty($data['email']) || !empty($data['mobile'])) {
            if (!ValidateHelper::isEmail($data['email'])) {
                throw new Exception('邮箱格式错误');
            }
            if (!ValidateHelper::isMobileCN($data['mobile'])) {
                throw new Exception('联系人手机号格式错误');
            }
        }

        if (!empty($data['shortName']) && mb_strlen($data['shortName'], 'utf-8') > 50) {
            throw new Exception('单位简称最多50个字符');
        }

        $companyModel = new Company();
        $memberModel  = new Member();

        if ($data['id']) {
            if (Company::checkNameOnly($data['id'], trim($data['fullName']))) {
                throw new Exception('该单位名称已存在');
            }
        } else {
            if (Company::checkNameOnly(0, trim($data['fullName']))) {
                throw new Exception('该单位名称已存在');
            }
        }

        $memberModel->status                = Member::STATUS_NO_COOPERATION;
        $memberModel->type                  = Member::TYPE_COMPANY;
        $memberModel->mobile                = '';
        $memberModel->username              = Member::createUserName();
        $memberModel->password              = Member::COMPANY_USER_PASSWORD;
        $memberModel->email                 = '';
        $memberModel->source_type           = Member::SOURCE_TYPE_PC;
        $memberModel->email_register_status = Member::EMAIL_REGISTER_STATUS_ILLEGAL;
        $memberModel->is_chat_window        = BaseMember::IS_CHAT_WINDOW_NO;
        $memberModel->is_greeting           = BaseMember::IS_GREETING_YES;
        $memberModel->greeting_type         = BaseMember::GREETING_TYPE_SYSTEM;
        $memberModel->greeting_default_id   = BaseChatCommonGreetingSystem::getDefaultId(BaseMember::TYPE_COMPANY);
        if (!$memberModel->save()) {
            throw new Exception($memberModel->getFirstErrorsMessage());
        }
        // 新增会员的ID
        $memberInsertId = $memberModel->attributes['id'];

        /**
         * 新增单位基础信息
         */
        $area                          = $data['area'];
        $companyModel->member_id       = $memberInsertId;
        $companyModel->is_cooperation  = Company::COOPERATIVE_UNIT_NO;
        $companyModel->status          = Company::STATUS_ACTIVE;
        $companyModel->full_name       = trim($data['fullName']);
        $companyModel->type            = $data['type'];
        $companyModel->nature          = $data['nature'];
        $companyModel->industry_id     = $data['industryId'];
        $companyModel->province_id     = $area[0] ?: 0;
        $companyModel->city_id         = $area[1] ?: 0;
        $companyModel->district_id     = $area[2] ?: 0;
        $companyModel->address         = $data['address'] ?: '';
        $companyModel->contact         = $data['contact'] ?: '';
        $companyModel->department      = $data['department'] ?: '';
        $companyModel->create_admin_id = Yii::$app->user->id ?: 0;
        $companyModel->source_type     = Company::TYPE_SOURCE_ADD;
        $companyModel->delivery_type   = BaseCompany::DELIVERY_TYPE_OUTER;
        $companyModel->is_hide         = BaseCompany::IS_HIDE_NO;
        $companyModel->is_miniapp      = BaseCompany::IS_MINIAPP_NO;
        $companyModel->is_manual_tag   = BaseCompany::IS_MANUAL_TAG_NONE;
        $companyModel->is_abroad       = BaseCompany::IS_ABROAD_NO;
        $companyModel->short_name      = trim($data['shortName'] ?? '');

        //群组处理
        $groupScoreSystemId                  = BaseCompanyGroupScoreSystem::getSystemScoreId(BaseCompanyGroup::COOPERATIVE_GROUP_NON_ID);
        $companyModel->group_score_system_id = $groupScoreSystemId;
        if (!$companyModel->save()) {
            throw new Exception($companyModel->getFirstErrorsMessage());
        }
        BaseCompany::editGroup($companyModel->id, BaseCompanyGroup::COOPERATIVE_GROUP_NON_ID);
        // 新增单位的ID
        $companyInsertId = $companyModel->attributes['id'];
        $rule_model      = new RuleCompany();
        $company_bool    = $rule_model->exec($companyInsertId);
        $company_info    = BaseCompany::findOne($companyInsertId);
        if ($company_bool) {
            $company_info->is_miniapp = BaseCompany::IS_MINIAPP_YES;
        }
        if (!$company_info->save()) {
            throw new Exception($company_info->getFirstErrorsMessage());
        }
        /**
         * 新增联系信息部分
         */
        $companyContactModel             = new CompanyContact();
        $companyContactModel->member_id  = $memberInsertId;
        $companyContactModel->company_id = $companyInsertId;
        $companyContactModel->status     = $companyContactModel::STATUS_ACTIVE;
        $companyContactModel->name       = $data['contact'] ?: '';
        $companyContactModel->department = $data['department'] ?: '';
        $companyContactModel->mobile     = $data['mobile'] ?: '';
        $companyContactModel->telephone  = $data['telephone'] ?: '';
        $companyContactModel->email      = $data['email'] ?: '';
        if (!$companyContactModel->save()) {
            throw new Exception($companyContactModel->getFirstErrorsMessage());
        }

        // 会员地址
        $memberAddress = new MemberAddress();

        $memberAddress->member_id   = $memberInsertId;
        $memberAddress->province_id = $data['areaId'][0] ?: 0;
        $memberAddress->city_id     = $data['areaId'][1] ?: 0;
        $memberAddress->district_id = $data['areaId'][2] ?: 0;
        $memberAddress->detail      = $data['address'] ?: '';
        if (!$memberAddress->save()) {
            throw new Exception($memberAddress->getFirstErrorsMessage());
        }

        // 入库meilisearch
        Producer::meilisearch($companyInsertId, MeilisearchJob::TYPE_COMPANY);

        return $companyInsertId;
    }

    /**
     * 获取单位入网审核列表
     * @param $params
     * @return array
     * @throws \Exception
     */
    public static function getAuditCompanyList($params): array
    {
        $query = Member::find()
            ->alias('m')
            ->leftJoin(['cia' => CompanyInfoAuth::tableName()], 'cia.member_id = m.id')
            ->leftJoin(['c' => Company::tableName()], 'c.member_id = m.id')
            ->groupBy('cia.audit_status,cia.add_time')
            ->where([
                'cia.phase' => [
                    self::PHASE_ONE,
                    self::PHASE_TWO,
                    self::PHASE_THREE,
                ],
            ]);

        // 判断审核状态
        switch ($params['auditStatus']) {
            // 待审核
            case BaseCompanyInfoAuth::AUDIT_STATUS_FIRST_WAIT:
                $query->andFilterWhere([
                    'cia.audit_status' => BaseCompanyInfoAuth::AUDIT_STATUS_FIRST_WAIT,
                ]);
                break;
            // 待初审
            case BaseCompanyInfoAuth::AUDIT_STATUS_SECOND_WAIT:
                $query->andFilterWhere([
                    'cia.audit_status' => BaseCompanyInfoAuth::AUDIT_STATUS_SECOND_WAIT,
                ]);
                break;
            //初审通过
            case BaseCompanyInfoAuth::AUDIT_STATUS_IDENTITY_AUTH:
                $query->andFilterWhere([
                    'cia.audit_status' => BaseCompanyInfoAuth::AUDIT_STATUS_IDENTITY_AUTH,
                ]);
                break;
            case BaseCompanyInfoAuth::AUDIT_STATUS_SECOND_PASS_IDENTITY_REJECT:
                $query->andFilterWhere([
                    'cia.audit_status' => BaseCompanyInfoAuth::AUDIT_STATUS_SECOND_PASS_IDENTITY_REJECT,
                ]);
                break;
            //初审拒绝
            case BaseCompanyInfoAuth::AUDIT_STATUS_SECOND_REJECT:
                $query->andFilterWhere([
                    'cia.audit_status' => BaseCompanyInfoAuth::AUDIT_STATUS_SECOND_REJECT,
                ]);
                break;
            //终审拒绝
            case BaseCompanyInfoAuth::AUDIT_STATUS_FIRST_REJECT:
                $query->andFilterWhere([
                    'cia.audit_status' => BaseCompanyInfoAuth::AUDIT_STATUS_FIRST_REJECT,
                ]);
                break;
            default:
                $query->andFilterWhere([
                    'cia.audit_status' => [
                        self::AUDIT_STATUS_FIRST_WAIT,
                        self::AUDIT_STATUS_SECOND_WAIT,
                        self::AUDIT_STATUS_IDENTITY_AUTH,
                        self::AUDIT_STATUS_SECOND_REJECT,
                        self::AUDIT_STATUS_FIRST_REJECT,
                        self::AUDIT_STATUS_SECOND_PASS_IDENTITY_REJECT,
                    ],
                ]);
        }

        //单位名称
        $query->andFilterWhere([
            'like',
            'cia.full_name',
            $params['fullName'],
        ]);

        $query->andFilterCompare('cia.type', $params['type']);

        //所在地区
        if ($params['area']) {
            $query->andFilterWhere([
                'in',
                'cia.city_id',
                $params['area'],
            ]);
        }

        //所属行业
        if ($params['industryId']) {
            $query->andFilterWhere([
                'in',
                'cia.industry_id',
                $params['industryId'],
            ]);
        }

        //单位性质
        $query->andFilterWhere([
            'cia.nature' => $params['nature'],
        ]);

        //联系号码
        $query->andFilterWhere([
            'or',
            [
                'm.mobile' => $params['mobile'],
            ],
            [
                'cia.mobile' => $params['mobile'],
            ],
        ]);
        //注册邮箱
        $query->andFilterCompare('m.email', $params['email'], 'like');
        //单位创建时间
        if ($params['addTimeFrom']) {
            $query->andFilterWhere([
                '>=',
                'c.add_time',
                TimeHelper::dayToBeginTime($params['addTimeFrom']),
            ]);
        }

        if ($params['addTimeTo']) {
            $query->andFilterWhere([
                '<',
                'c.add_time',
                TimeHelper::dayToEndTime($params['addTimeTo']),
            ]);
        }
        //单位申请时间
        if ($params['applyTimeFrom']) {
            $query->andFilterWhere([
                '>=',
                'cia.submit_audit_time',
                TimeHelper::dayToBeginTime($params['applyTimeFrom']),
            ]);
        }

        if ($params['applyTimeTo']) {
            $query->andFilterWhere([
                '<',
                'cia.submit_audit_time',
                TimeHelper::dayToEndTime($params['applyTimeTo']),
            ]);
        }
        //单位入网来源
        $query->andFilterWhere([
            'c.source_type' => $params['sourceType'],
        ]);
        //创建人
        if (!empty($params['createName'])) {
            //            $createMemberId = Member::findOneVal(['username' => $params['createName']], 'id');
            //            $createAdminId  = Admin::findOneVal(['username' => $params['createName']], 'id');
            //这里应该取的是用户跟管理人员的id群
            $memberList = Member::find()
                ->select(['id'])
                ->where([
                    'like',
                    'username',
                    $params['createName'],
                ])
                ->asArray()
                ->all();

            $adminList = Admin::find()
                ->select(['id'])
                ->where([
                    'like',
                    'username',
                    $params['createName'],
                ])
                ->asArray()
                ->all();

            $memberIds      = array_column($memberList, 'id');
            $adminIds       = array_column($adminList, 'id');
            $createAdminIds = $memberIds + $adminIds;

            $query->andWhere([
                'c.create_admin_id' => $createAdminIds,
            ]);

            //            if ($createMemberId) {
            //                //会员创建
            //                $query->andFilterWhere([
            //                    'c.create_admin_id' => $createMemberId,
            //                ]);
            //            } else {
            //                //运营创建
            //                $query->andFilterWhere([
            //                    'c.create_admin_id' => $createAdminId,
            //                ]);
            //            }
        }

        $orderBy = 'cia.audit_status desc,cia.add_time desc';
        if ($params['sortNature'] == Company::ORDER_BY_DESC) {
            $orderBy = 'cia.sortNature desc,cia.audit_status desc';
        } else {
            if ($params['sortLastLoginTime'] == Company::ORDER_BY_ASC) {
                $orderBy = 'cia.sortNature asc,cia.audit_status asc';
            }
        }
        if ($params['sortAddTime'] == Company::ORDER_BY_DESC) {
            $orderBy = 'c.add_time desc,cia.audit_status desc';
        } else {
            if ($params['sortAddTime'] == Company::ORDER_BY_ASC) {
                $orderBy = 'c.add_time asc,cia.audit_status asc';
            }
        }
        if ($params['sortApplyTime'] == Company::ORDER_BY_DESC) {
            $orderBy = 'cia.submit_audit_time desc,cia.audit_status desc';
        } else {
            if ($params['sortApplyTime'] == Company::ORDER_BY_ASC) {
                $orderBy = 'cia.submit_audit_time asc,cia.audit_status asc';
            }
        }

        $count    = $query->count();
        $pageSize = $params['pageSize'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $params['page'], $pageSize);

        $select = [
            'c.id as company_id',
            'c.status as status_company',
            'c.source_type',
            'c.create_admin_id',
            'c.add_time',
            // 创建时间
            'cia.member_id',
            'cia.full_name',
            'cia.id as info_auth_id',
            'cia.contact',
            'cia.type',
            'cia.mobile as c_mobile',
            'cia.email as c_email',
            'cia.department',
            'cia.nature',
            'cia.address',
            'cia.submit_audit_time as apply_time',
            // 申请时间
            'cia.audit_status',
            'cia.province_id',
            'cia.city_id',
            'cia.district_id',
            'm.username as create_name',
            'm.mobile as m_mobile',
            'm.email as m_email',
        ];

        $allList = $query->select($select)
            ->asArray()
            ->orderBy($orderBy)
            ->all();

        $list = $query->offset($pages['offset'])
            ->select($select)
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        //获取地区表缓存
        $cache     = Yii::$app->cache;
        $areaCache = $cache->get(Cache::PC_ALL_AREA_TABLE_KEY);
        if (!$areaCache) {
            $areaCache = BaseArea::setAreaCache();
        }

        foreach ($list as &$item) {
            $item['sourceTypeTxt']  = Company::TYPE_SOURCE_LIST[$item['source_type']];
            $item['natureTxt']      = Dictionary::getCompanyNatureName($item['nature']);
            $item['typeTxt']        = Dictionary::getCompanyTypeName($item['type']);
            $item['auditStatusTxt'] = CompanyInfoAuth::AUDIT_STATUS_LIST[$item['audit_status']];
            $item['address']        = $areaCache[$item['province_id']]['name'] . $areaCache[$item['city_id']]['name'] . $item['address'];

            if ($item['source_type'] == Company::TYPE_SOURCE_ADD) {
                $item['createName'] = Admin::findOneVal(['id' => $item['create_admin_id']], 'username') ?: '';
            } else {
                $item['createName'] = Member::findOneVal(['id' => $item['create_admin_id']], 'username') ?: '';
            }
            $item['mMobile'] = $item['m_mobile'];
            $item['mEmail']  = $item['m_email'];
            $item['cMobile'] = $item['c_mobile'];
            $item['cEmail']  = $item['c_email'];
        }

        // 判断是否导出
        if ($params['export']) {
            $headers = [
                '单位名称',
                '联系人',
                '注册手机',
                '注册邮箱',
                '联系手机',
                '联系邮箱',
                '所在部门',
                '入网来源',
                '创建人',
                '单位类型',
                '单位性质',
                '所在地',
                '申请时间',
                '审核状态',
            ];

            $data = [];
            foreach ($list as $val) {
                $data[] = [
                    $val['full_name'] ?: '',
                    $val['contact'] ?: '',
                    $val['mMobile'] ?: '-',
                    $val['mEmail'] ?: '-',
                    $val['cMobile'] ?: '-',
                    $val['cEmail'] ?: '-',
                    $val['department'] ?: '',
                    $val['sourceTypeTxt'] ?: '',
                    $val['createName'] ?: '',
                    $val['typeTxt'] ?: '',
                    $val['natureTxt'] ?: '',
                    $val['address'] ?: '',
                    $val['add_time'] ?: '',
                    $val['auditStatusTxt'] ?: '',
                ];
            }

            $excel    = new Excel();
            $fileName = $excel->export($data, $headers);

            return [
                'excelUrl' => $fileName,
            ];
        } else {
            $stayExamineCount   = 0;
            $rejectExamineCount = 0;
            foreach ($allList as $value) {
                if (in_array($value['audit_status'], [
                    BaseCompanyInfoAuth::AUDIT_STATUS_SECOND_WAIT,
                    BaseCompanyInfoAuth::AUDIT_STATUS_FIRST_WAIT,
                ])) {
                    $stayExamineCount++;
                }
                if (in_array($value['audit_status'], [
                    BaseCompanyInfoAuth::AUDIT_STATUS_SECOND_REJECT,
                    BaseCompanyInfoAuth::AUDIT_STATUS_FIRST_REJECT,
                ])) {
                    $rejectExamineCount++;
                }
            }

            return [
                'list'   => $list,
                'pages'  => [
                    'size'  => (int)$pageSize,
                    'total' => (int)$count,
                ],
                'amount' => [
                    'stayExamineCount'   => $stayExamineCount,
                    'rejectExamineCount' => $rejectExamineCount,
                ],
            ];
        }
    }

    /**
     * 审核记录查看详情
     * @param $request
     * @return array
     * @throws Exception
     */
    public static function getAuditDetail($request)
    {
        if (empty($request['id'])) {
            throw new Exception('单位id不能为空');
        }

        // 单位字段
        $select = [
            'c.id',
            'c.scale',
            'c.source_type',
            'c.status',
            'c.delivery_type',
            'c.account_nature',
            'c.status',
            'cia.full_name',
            'cia.nature',
            'cia.industry_id',
            'cia.contact',
            'cia.department',
            'cia.mobile as cMobile',
            'cia.email as cEmail',
            'cia.telephone',
            'cia.province_id',
            'cia.city_id',
            'cia.district_id',
            'cia.address',
            'cia.license_path',
            'cia.person_info_path',
            'm.mobile as mMobile',
            'm.email as mEmail',
        ];

        //基本信息
        $basicsInfo = Member::find()
            ->alias('m')
            ->leftJoin(['cia' => CompanyInfoAuth::tableName()], 'cia.member_id = m.id')
            ->leftJoin(['c' => Company::tableName()], 'c.id = cia.company_id')
            ->select($select)
            ->where([
                'cia.company_id' => $request['id'],
            ])
            ->asArray()
            ->one() ?: [
            'id'               => '',
            'scale'            => '',
            'source_type'      => '',
            'full_name'        => '',
            'delivery_type'    => '',
            'account_nature'   => '',
            'nature'           => '',
            'industry_id'      => '',
            'contact'          => '',
            'department'       => '',
            'mMobile'          => '',
            'mEmail'           => '',
            'cMbile'           => '',
            'cEmail'           => '',
            'telephone'        => '',
            'province_id'      => '',
            'city_id'          => '',
            'district_id'      => '',
            'address'          => '',
            'license_path'     => '',
            'person_info_path' => '',
        ];

        if ($basicsInfo['status'] == Company::STATUS_ACTIVE) {
            throw new Exception('已经审核通过无法再次审核');
        }
        //获取地区表缓存
        $cache     = Yii::$app->cache;
        $areaCache = $cache->get(Cache::PC_ALL_AREA_TABLE_KEY);
        if (!$areaCache) {
            $areaCache = BaseArea::setAreaCache();
        }

        $basicsInfo['natureTxt']          = Dictionary::getCompanyNatureName($basicsInfo['nature']);
        $basicsInfo['delivery_type_txt']  = BaseCompany::DELIVERY_TYPE_NAME[$basicsInfo['delivery_type']];
        $basicsInfo['account_nature_txt'] = BaseCompany::ACCOUNT_NATURE_NAME[$basicsInfo['account_nature']];
        $basicsInfo['industryTxt']        = Trade::getIndustryName($basicsInfo['industry_id']);
        $basicsInfo['scaleTxt']           = Dictionary::getCompanyScaleName($basicsInfo['scale']);
        $basicsInfo['license_path']       = FileHelper::getFullUrl($basicsInfo['license_path']);
        $basicsInfo['person_info_path']   = FileHelper::getFullUrl($basicsInfo['person_info_path']);
        $basicsInfo['address']            = $areaCache[$basicsInfo['province_id']]['name'] . $areaCache[$basicsInfo['city_id']]['name'] . $basicsInfo['address'];

        //审核记录列表
        $auditRecordList = CompanyInfoAuthLog::find()
            ->select('admin_id,add_time,audit_status,reason')
            ->where([
                'company_id' => $request['id'],
            ])
            ->orderBy('add_time desc')
            ->asArray()
            ->all();

        foreach ($auditRecordList as &$item) {
            $item['auditStatusTxt'] = CompanyInfoAuth::AUDIT_STATUS_LIST[$item['audit_status']];
            $item['actionTxt']      = Admin::findOneVal(['id' => $item['admin_id']], 'name');
        }

        return compact('basicsInfo', 'auditRecordList');
    }

    /**
     * 入网审核记录操作-视图
     * @param $request
     * @return array
     * @throws Exception
     */
    public static function getAuditHandleView($request)
    {
        if (empty($request['id'])) {
            throw new Exception('单位id不能为空');
        }

        if (empty($request['auditStatus'])) {
            throw new Exception('审核状态不能为空');
        }

        $select = [
            'c.id',
            'c.full_name',
            'c.status as status_company',
            'cia.contact',
            'cia.mobile',
            'cia.email',
            'cia.department',
            'cia.nature',
            'cia.industry_id',
            'cia.contact',
            'cia.department',
            'cia.audit_status',
            'cia.mobile',
            'cia.telephone',
            'cia.email',
            'cia.address',
            'cia.license_path',
            'cia.person_info_path',
        ];

        $data = Company::find()
            ->alias('c')
            ->leftJoin('company_info_auth as cia', 'cia.company_id = c.id')
            ->select($select)
            ->where([
                'c.id' => $request['id'],
            ])
            ->asArray()
            ->one();

        $data['natureTxt']   = Dictionary::getCompanyNatureName($data['nature']);
        $data['industryTxt'] = Trade::getIndustryName($data['industry_id']);

        return $data;
    }

    /**
     * 入网审核-初审拒绝操作
     * @param $request
     * @throws Exception
     */
    public static function setAuditHandleTrialRefuse($request)
    {
        if (empty($request['id'])) {
            throw new Exception('单位id不能为空');
        }
        if (empty($request['auditStatus'])) {
            throw new Exception('审核状态不能为空');
        }
        if (empty($request['reason'])) {
            throw new Exception('处理意见不能为空');
        }

        // 单位审核
        $companyInfoAuth = CompanyInfoAuth::findOne(['company_id' => $request['id']]) ?: new CompanyInfoAuth();
        // 审核记录
        $companyInfoAuthLog = new CompanyInfoAuthLog();

        $companyInfoAuthLog->company_id = $request['id'];
        $companyInfoAuthLog->admin_id   = Yii::$app->user->id;
        $companyInfoAuth->audit_status  = CompanyInfoAuth::AUDIT_STATUS_SECOND_REJECT;
        $companyInfoAuth->reason        = $request['reason'];
        $companyInfoAuthLog->reason     = $request['reason'];
        // 操作单位审核表
        if (!$companyInfoAuth->save()) {
            throw new Exception($companyInfoAuth->getFirstErrorsMessage());
        }
        // 操作单位审核记录表
        if (!$companyInfoAuthLog->save()) {
            throw new Exception($companyInfoAuthLog->getFirstErrorsMessage());
        }

        $company = Company::findOne(['id' => $request['id']]);
        // 同步更新单位表的主状态
        $company->status = BaseCompany::STATUS_REJECT;
        if (!$company->save()) {
            throw new Exception($company->getFirstErrorsMessage());
        }
        // 找到联系方式
        $companyContact = CompanyContact::findOne(['company_id' => $request['id']]);

        if ($companyContact->email && $company->source_type == $company::TYPE_SOURCE_APPLY) {
            Producer::email($companyContact->email, 2, EmailQueue::EMAIL_COMPANY_INFO_AUDIT_REJECT,
                ['reason' => $request['reason']]);
        }

        if ($companyContact->mobile && $company->source_type == $company::TYPE_SOURCE_APPLY) {
            Producer::sms($companyContact->mobile, 2, SmsQueue::COMPANY_INFO_AUDIT_REJECT);
        }

        //发送站内信
        BaseMemberMessage::send($company->member_id, BaseMemberMessage::TYPE_COMPANY_SYSTEM, '资质审核',
            '您的单位资质认证不通过，请查看详情并按照要求重新提交单位资质认证。');
    }

    /**
     * 入网审核-初审通过操作
     * @param $request
     * @throws Exception
     */
    public static function setAuditHandleTrialPass($request)
    {
        if (empty($request['id'])) {
            throw new Exception('单位id不能为空');
        }
        if (empty($request['auditStatus'])) {
            throw new Exception('审核状态不能为空');
        }
        if ($request['isAgent'] == 1 && empty($request['agent'])) {
            throw new Exception('经办人资质处理意见不能为空');
        }

        // 单位审核
        $companyInfoAuth = CompanyInfoAuth::findOne(['company_id' => $request['id']]) ?: new CompanyInfoAuth();
        // 审核记录
        $companyInfoAuthLog = new CompanyInfoAuthLog();
        // 单位表
        $company = Company::findOne(['id' => $request['id']]) ?: new Company();

        if (Company::checkNameOnly($company->id, $companyInfoAuth['full_name'])) {
            throw new Exception('该单位名称已存在');
        }

        // 这里是初审通过，有填经办人信息，但经办人信息不通过
        if ($request['isAgent'] == 1 && !empty($request['agent'])) {
            $companyInfoAuth->audit_status    = CompanyInfoAuth::AUDIT_STATUS_SECOND_PASS_IDENTITY_REJECT;
            $companyInfoAuthLog->audit_status = CompanyInfoAuth::AUDIT_STATUS_SECOND_PASS_IDENTITY_REJECT;
        } else {
            $companyInfoAuth->audit_status    = CompanyInfoAuth::AUDIT_STATUS_IDENTITY_AUTH;
            $companyInfoAuthLog->audit_status = CompanyInfoAuth::AUDIT_STATUS_IDENTITY_AUTH;
        }

        $companyInfoAuth->phase         = CompanyInfoAuth::PHASE_TWO;
        $companyInfoAuth->reason        = $request['agent'];
        $companyInfoAuthLog->company_id = $request['id'];
        $companyInfoAuthLog->phase      = CompanyInfoAuth::PHASE_TWO;
        $companyInfoAuthLog->reason     = $request['agent'];
        $companyInfoAuthLog->admin_id   = Yii::$app->user->id;
        $company->full_name             = $companyInfoAuth->full_name;

        if ($request['isAgent'] == 2) {
            $companyInfoAuthLog->reason = '经办人信息为空-' . $request['agent'];
            $companyInfoAuth->reason    = '经办人信息为空-' . $request['agent'];
        }
        // 操作单位审核表
        if (!$companyInfoAuth->save()) {
            throw new Exception($companyInfoAuth->getFirstErrorsMessage());
        }

        // 操作单位审核记录表
        if (!$companyInfoAuthLog->save()) {
            throw new Exception($companyInfoAuthLog->getFirstErrorsMessage());
        }
        // 操作单位表
        if (!$company->save()) {
            throw new Exception($company->getFirstErrorsMessage());
        }

        //发送站内信
        BaseMemberMessage::send($company->member_id, BaseMemberMessage::TYPE_COMPANY_SYSTEM, '资质审核',
            '您的单位资质认证已通过，欢迎使用高校人才网。');
    }

    /**
     * 入网审核记录-终审通过/拒绝操作
     * @param $request
     * @return array|void
     * @throws Exception
     */
    public static function setAuditHandleFinalJudgment($request)
    {
        if (empty($request['id'])) {
            throw new Exception('单位id不能为空');
        }
        if (empty($request['auditStatus']) && empty($request['statusAudit'])) {
            throw new Exception('审核状态不能为空');
        }
        $request['auditStatus'] = $request['auditStatus'] ?: $request['statusAudit'];
        $companyInfoAuth        = CompanyInfoAuth::findOne(['company_id' => $request['id']]) ?: new CompanyInfoAuth();
        $companyInfoAuthLog     = new CompanyInfoAuthLog();
        $company                = Company::findOne(['id' => $request['id']]) ?: new Company();
        $companyContact         = CompanyContact::findOne(['company_id' => $request['id']]) ?: new CompanyContact();
        $member                 = Member::findOne(['id' => $company['member_id']]) ?: new Member();

        if ($company->status == Company::STATUS_ACTIVE) {
            throw new Exception('该单位已经通过审核');
        }
        // 终审通过
        if ($request['auditStatus'] == CompanyInfoAuth::AUDIT_STATUS_PASS) {
            if (Company::checkNameOnly($request['id'], $companyInfoAuth['full_name'])) {
                throw new Exception('该单位名称已存在');
            }

            if ($company->source_type == $company::TYPE_SOURCE_APPLY) {
                $adminId = $request['adminId'];
                if (!$adminId) {
                    throw new Exception('自主申请的必须选择归属业务员');
                }
                // 设置业务员
                $company->admin_id = $request['adminId'];
            }

            // 审核状态修改
            $companyInfoAuth->audit_status = CompanyInfoAuth::AUDIT_STATUS_PASS;
            $companyInfoAuth->phase        = CompanyInfoAuth::PHASE_THREE;

            $companyInfoAuthLog->company_id   = $request['id'];
            $companyInfoAuthLog->admin_id     = Yii::$app->user->id;
            $companyInfoAuthLog->audit_status = CompanyInfoAuth::AUDIT_STATUS_PASS;
            $companyInfoAuthLog->phase        = CompanyInfoAuth::PHASE_THREE;

            // 审核通过之后，更新补全单位表的单位信息
            $company->status       = CompanyInfoAuth::AUDIT_STATUS_PASS;
            $company->full_name    = $companyInfoAuth->full_name ?: '';
            $company->type         = (string)$companyInfoAuth->type;
            $company->nature       = (string)$companyInfoAuth->nature;
            $company->industry_id  = $companyInfoAuth->industry_id;
            $company->province_id  = $companyInfoAuth->province_id;
            $company->city_id      = $companyInfoAuth->city_id;
            $company->district_id  = $companyInfoAuth->district_id;
            $company->address      = $companyInfoAuth->address;
            $company->telephone    = $companyInfoAuth->telephone;
            $company->package_type = BaseCompanyPackageConfig::COMPANY_ROLE_FREE;
            // 2.0 添加
            $company->is_abroad = BaseCompany::IS_ABROAD_NO;
            if (empty($company->delivery_type)) {
                $company->delivery_type = BaseCompany::DELIVERY_TYPE_OUTER_INNER;
            }
            if ($company->is_manual_tag == BaseCompany::IS_MANUAL_TAG_NONE) {
                $rule_model          = new RuleCompany();
                $res                 = $rule_model->exec($company->id);
                $company->is_miniapp = $res ? BaseCompany::IS_MINIAPP_YES : BaseCompany::IS_MINIAPP_NO;
            } elseif ($company->is_manual_tag == BaseCompany::IS_MANUAL_TAG_YES) {
                $company->is_miniapp = BaseCompany::IS_MINIAPP_YES;
            }
            // 新增单位联系人具体信息
            $companyContact->member_id  = $member->id;
            $companyContact->company_id = $company->id;
            $companyContact->name       = $companyInfoAuth->contact;
            $companyContact->department = $companyInfoAuth->department;
            $companyContact->mobile     = $companyInfoAuth->mobile;
            $companyContact->telephone  = $companyInfoAuth->telephone;
            $companyContact->email      = $companyInfoAuth->email;
            $member->status             = Member::STATUS_ACTIVE;

            // 添加审核日志
            if (!$companyInfoAuthLog->save()) {
                throw new Exception($companyInfoAuthLog->getFirstErrorsMessage());
            }
            // 更新审核信息
            if (!$companyInfoAuth->save()) {
                throw new Exception($companyInfoAuth->getFirstErrorsMessage());
            }
            // 更新单位信息
            if (!$company->save()) {
                throw new Exception($company->getFirstErrorsMessage());
            }
            //单位更新成功
            //单位群组加一
            BaseCompanyGroup::companyGroupNumber(BaseCompanyGroupScoreSystem::findOneVal(['id' => $company->group_score_system_id],
                'group_ids'), BaseCompanyGroup::OPERATION_TYPE_INCREASE);
            // 更新会员信息
            if (!$member->save()) {
                throw new Exception($member->getFirstErrorsMessage());
            }
            // 新增单位联系信息
            if (!$companyContact->save()) {
                throw new Exception($companyContact->getFirstErrorsMessage());
            }
            // 配置单位免费会员
            //BaseCompanyPackageConfig::setCurrencyCompanyPackageConfig($company->id);
            $configureService = new ConfigureService();
            $adminId          = Yii::$app->user->id;
            $packageAmount    = BaseCompanyPackageSystemConfig::PACKAGE_AMOUNT_SYSTEM;
            $packageUnitDay   = BaseCompanyPackageChangeLog::PACKAGE_UNIT_DAY;
            $packageId        = BaseCompanyPackageSystemConfig::findOneVal(['code' => 'free'], 'id');
            $remark           = BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME[BaseCompanyPackageChangeLog::HANDLE_TYPE_CONFIGURE_FREE];

            $requestList = [
                'company_id'         => $company->id,
                'package_id'         => $packageId,
                'package_amount'     => $packageAmount,
                'package_amount_day' => $packageAmount * $packageUnitDay,
                'effect_time'        => CUR_DATE,
                'remark'             => $remark,
            ];
            $configureService->setOperator($adminId)
                ->setCompany($company->id)
                ->setData($requestList)
                ->run();

            if ($companyContact->email && $company->source_type == $company::TYPE_SOURCE_APPLY) {
                Producer::email($companyContact->email, 2, EmailQueue::EMAIL_COMPANY_INFO_AUDIT_PASS, '');
            }
            if ($companyContact->mobile && $company->source_type == $company::TYPE_SOURCE_APPLY) {
                Producer::sms($companyContact->mobile, 2, SmsQueue::COMPANY_INFO_AUDIT_PASS);
            }
            //初始化子账号配置信息
            BaseCompanyMemberConfig::initAdd($company->id);
            //初始化账号消息配置表
            BaseCompanyMemberMessageConfig::initAdd($company->member_id, $company->id);
            //日志
            $data = [
                'member_id'         => $company->member_id,
                'company_id'        => $company->id,
                'type'              => BaseCompanyMemberOperationLog::OPERATION_TYPE_COMPANY_PASS,
                'operation_id'      => Yii::$app->user->id,
                'operation_port'    => BaseCompanyMemberOperationLog::OPERATION_PORT_ADMIN,
                'operation_content' => '单位终审通过，获得权限：超管权限',
            ];
            BaseCompanyMemberOperationLog::addLog($data);

            $sendTitle   = '【注册审核通过】您的注册资料已通过终审，欢迎使用高校人才网。';
            $sendContent = '您的注册资料已审核通过，欢迎使用高校人才网。';
            //发送站内信
            BaseMemberMessage::send($company->member_id, BaseMemberMessage::TYPE_COMPANY_SYSTEM, $sendTitle,
                $sendContent);

            // 这个时候就可以去meilisearch了
            Producer::meilisearch($company->id, MeilisearchJob::TYPE_COMPANY);
        } else {//终审不通过
            if (empty($request['reason'])) {
                throw new Exception('处理意见不能为空');
            }
            // 终审拒绝，修改审核状态
            $companyInfoAuthLog->company_id   = $request['id'];
            $companyInfoAuthLog->audit_status = $request['auditStatus'];
            $companyInfoAuthLog->reason       = $request['reason'];
            $companyInfoAuthLog->phase        = CompanyInfoAuth::PHASE_TWO;
            $companyInfoAuthLog->admin_id     = Yii::$app->user->id;

            $companyInfoAuth->audit_status = $request['auditStatus'];
            $companyInfoAuth->reason       = $request['reason'];
            $companyInfoAuth->phase        = CompanyInfoAuth::PHASE_TWO;

            $company->status = $request['auditStatus'];
            if (!$company->save()) {
                throw new Exception($companyInfoAuthLog->getFirstErrorsMessage());
            }

            if (!$companyInfoAuthLog->save()) {
                throw new Exception($companyInfoAuthLog->getFirstErrorsMessage());
            }

            if (!$companyInfoAuth->save()) {
                throw new Exception($companyInfoAuth->getFirstErrorsMessage());
            }

            if ($companyContact->email && $company->source_type == $company::TYPE_SOURCE_APPLY) {
                Producer::email($companyContact->email, 2, EmailQueue::EMAIL_COMPANY_INFO_AUDIT_REJECT,
                    ['reason' => $request['reason']]);
            }
            if ($companyContact->mobile && $company->source_type == $company::TYPE_SOURCE_APPLY) {
                Producer::sms($companyContact->mobile, 2, SmsQueue::COMPANY_INFO_AUDIT_REJECT);
            }

            $sendTitle   = '【注册审核不通过】您的经办人认证资料暂不符合要求，请重新提交认证';
            $sendContent = '您的经办人认证资料暂不符合要求，请至【单位中心】重新提交资料。';
            //发送站内信
            BaseMemberMessage::send($company->member_id, BaseMemberMessage::TYPE_COMPANY_SYSTEM, $sendTitle,
                $sendContent);
        }
    }

    /**
     * 获取审核详情编辑
     * @param $id
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function getAuditDetailEdit($id)
    {
        $select = [
            'cia.company_id',
            'cia.full_name',
            'cia.nature',
            'cia.type',
            'cia.industry_id',
            'cia.province_id',
            'cia.city_id',
            'cia.district_id',
            'cia.address',
            'cia.contact',
            'cia.department',
            'cia.telephone',
            'cia.license_path',
            'cia.person_info_path',
            'cia.audit_status',
            'cia.reason',
            'c.admin_id',
            'm.mobile',
            'm.email',
        ];

        $baseInfo = Member::find()
            ->alias('m')
            ->leftJoin(['c' => Company::tableName()], 'c.member_id = m.id')
            ->leftJoin(['cia' => CompanyInfoAuth::tableName()], 'cia.member_id = m.id')
            ->select($select)
            ->where([
                'cia.company_id' => $id,
            ])
            ->andWhere([
                'in',
                'cia.audit_status',
                [
                    CompanyInfoAuth::AUDIT_STATUS_SECOND_REJECT,
                    CompanyInfoAuth::AUDIT_STATUS_FIRST_REJECT,
                ],
            ])
            ->asArray()
            ->one() ?: [
            'company_id'       => '',
            'full_name'        => '',
            'nature'           => '',
            'type'             => '',
            'industry_id'      => '',
            'province_id'      => '',
            'city_id'          => '',
            'district_id'      => '',
            'address'          => '',
            'contact'          => '',
            'department'       => '',
            'telephone'        => '',
            'license_path'     => '',
            'person_info_path' => '',
            'audit_status'     => '',
            'admin_id'         => '',
            'mobile'           => '',
            'email'            => '',
        ];

        $baseInfo['area']             = $baseInfo['province_id'] . ',' . $baseInfo['city_id'];
        $baseInfo['license_path']     = FileHelper::getFullUrl($baseInfo['license_path']);
        $baseInfo['person_info_path'] = FileHelper::getFullUrl($baseInfo['person_info_path']);
        $baseInfo['auditReject']      = 1;

        return $baseInfo;
    }

    /**
     * 获取审核详情编辑提交
     * @param $data
     * @throws Exception
     */
    public static function setAuditDetailSave($data)
    {
        $checkData = [
            'companyId',
            'fullName',
            'nature',
            'type',
            'industryId',
            'area',
            'address',
            'contact',
            'department',
            'licensePath',
            'personInfoPath',
            'adminId',
            'mobile',
            'email',
        ];
        foreach ($checkData as $list) {
            if (!isset($data[$list])) {
                throw new Exception('参数' . $list . '不能为空');
            }
        }

        $companyInfoAuthModel = self::findOne(['company_id' => $data['companyId']]);
        $companyModel         = Company::findOne(['id' => $companyInfoAuthModel->company_id]) ?: new Company();
        $memberModel          = Member::findOne(['id' => $companyInfoAuthModel->member_id]) ?: new Member();
        $memberAddressModel   = MemberAddress::findOne(['member_id' => $companyInfoAuthModel->member_id]) ?: new MemberAddress();

        /**
         * 单位审核信息
         */
        $companyInfoAuthModel->audit_status      = self::AUDIT_STATUS_FIRST_WAIT;
        $companyInfoAuthModel->full_name         = trim($data['fullName']);
        $companyInfoAuthModel->type              = $data['type'];
        $companyInfoAuthModel->nature            = $data['nature'];
        $companyInfoAuthModel->industry_id       = $data['industryId'];
        $companyInfoAuthModel->province_id       = $data['area'][0];
        $companyInfoAuthModel->city_id           = $data['area'][1];
        $companyInfoAuthModel->address           = $data['address'];
        $companyInfoAuthModel->contact           = $data['contact'];
        $companyInfoAuthModel->department        = $data['department'];
        $companyInfoAuthModel->license_path      = $data['licensePath'];
        $companyInfoAuthModel->person_info_path  = $data['personInfoPath'];
        $companyInfoAuthModel->telephone         = $data['telephone'] ?: '';
        $companyInfoAuthModel->submit_audit_time = CUR_DATETIME;
        if (!$companyInfoAuthModel->save()) {
            throw new Exception($companyInfoAuthModel->getFirstErrorsMessage());
        }
        /**
         * 更新单位基础信息
         */
        $companyModel->full_name   = '';
        $companyModel->admin_id    = $data['adminId'];
        $companyModel->type        = $data['type'];
        $companyModel->nature      = $data['nature'];
        $companyModel->industry_id = $data['industryId'];
        $companyModel->province_id = $data['area'][0];
        $companyModel->city_id     = $data['area'][1];
        $companyModel->address     = $data['address'];
        $companyModel->contact     = $data['contact'];
        $companyModel->department  = $data['department'];
        $companyModel->telephone   = $data['telephone'] ?: '';
        // 同步更新单位表的主状态
        $companyModel->status = BaseCompany::STATUS_WAIT_SECOND_AUDIT;
        if (!$companyModel->save()) {
            throw new Exception($companyModel->getFirstErrorsMessage());
        }
        /**
         * 会员信息
         */
        $memberModel->mobile = $data['mobile'];
        $memberModel->email  = $data['email'];
        if (!$memberModel->save()) {
            throw new Exception($memberModel->getFirstErrorsMessage());
        }
        /**
         * 会员地址
         */
        $memberAddressModel->member_id   = $companyInfoAuthModel->member_id;
        $memberAddressModel->province_id = $data['area'][0];
        $memberAddressModel->city_id     = $data['area'][1];
        $memberAddressModel->detail      = $data['address'];
        if (!$memberAddressModel->save()) {
            throw new Exception($memberAddressModel->getFirstErrorsMessage());
        }
        //更新单位信息的联系人与所在部门
        $company_member_info = BaseCompanyMemberInfo::findOne(['member_id' => $memberModel->id]);
        if ($company_member_info) {
            $company_member_info->contact    = $data['contact'];
            $company_member_info->department = $data['department'];
            if (!$company_member_info->save()) {
                throw new Exception($company_member_info->getFirstErrorsMessage());
            }
        }
    }
}
