<?php

namespace admin\models;

use common\base\models\BaseDailyAnnouncementSummary;
use common\helpers\TimeHelper;
use yii\base\Exception;

class DailyAnnouncementSummary extends BaseDailyAnnouncementSummary
{
    public static function getList($params)
    {
        $query = self::find()
            ->select('id,add_time,belong_date,status')
            ->where([
                '<>',
                'status',
                self::STATUS_DELETE,
            ]);

        $query->andFilterWhere([
            'status' => $params['status'],
        ]);

        if ($params['dateFrom']) {
            $query->andWhere([
                '>=',
                'add_time',
                TimeHelper::dayToBeginTime($params['dateFrom']),
            ]);
        }

        if ($params['dateTo']) {
            $query->andWhere([
                '<=',
                'add_time',
                TimeHelper::dayToBeginTime($params['dateTo']),
            ]);
        }

        $pageSize = $params['pageSize'] ?: \Yii::$app->params['defaultPageSize'];
        $count    = $query->count();
        $pages    = self::setPage($count, $params['page'], $pageSize);
        $list     = $query->limit($pages['limit'])
            ->offset($pages['offset'])
            ->orderBy('id desc')
            ->asArray()
            ->all();
        foreach ($list as &$item) {
            $item['statusTxt'] = self::STATUS_LIST[$item['status']];
        }

        return [
            'list'  => $list,
            'pages' => [
                'limit' => $pages['limit'],
                'count' => (int)$count,
                'page'  => $params['page'],
            ],
        ];
    }

    public static function getDetail($id)
    {
        $articleId = self::findOneVal(['id' => $id], 'article_id');

        $content = Article::findOneVal(['id' => $articleId], 'content');

        return ['content' => $content];
    }

    public function edit($id, $content, $adminId)
    {
        $articleId = self::findOneVal(['id' => $id], 'article_id');

        $model = Article::findOne(['id' => $articleId]);

        $model->content = $content;

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
    }

    public function changeRelease($id, $adminId)
    {
        $model   = self::findOne($id);
        $article = Article::findOne(['id' => $model->article_id]);

        if ($model->status == self::STATUS_WAIT_RELEASE) {
            $model->status   = self::STATUS_ONLINE;
            $article->status = Article::STATUS_ONLINE;
        } else {
            $model->status   = self::STATUS_WAIT_RELEASE;
            $article->status = Article::STATUS_STAGING;
        }

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
    }

    public function submitDelete($id, $adminId)
    {
        $model = self::findOne($id);

        $article = Article::findOne(['id' => $model->article_id]);

        $model->status      = self::STATUS_DELETE;
        $article->is_delete = Article::IS_DELETE_YES;

        if (!$model->save() || !$article->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
    }
}