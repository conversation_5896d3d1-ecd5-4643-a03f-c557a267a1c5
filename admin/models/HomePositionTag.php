<?php

namespace admin\models;

use common\base\models\BaseHomePositionTag;
use yii\base\Exception;

class HomePositionTag extends BaseHomePositionTag
{
    /**
     * 保存内容
     * @param $name
     * @return bool|void
     * @throws Exception
     */
    public static function saveTag($name, $adminId)
    {
        $data = [
            'admin_id'    => $adminId,
            'name'         => $name,
        ];

        $model = new self();
        $model->load($data);
        $res = $model->save();
        if (!$res) {
            throw new Exception(implode(', ', array_values($model->getFirstErrors())));
        }
    }
}