<?php
/**
 * create user：shannon
 * create time：2024/3/1 09:04
 */
namespace admin\models;

use common\base\models\BaseSeoHotWordConfig;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\libs\Cache;
use common\service\meilisearch\hotWord\HotWordService;
use common\service\meilisearch\hotWord\JobHotService;
use yii\base\Exception;
use Yii;

/**
 * 处理热词相关业务
 * 处理热词数据量大的系统业务
 * 处理meilisearch对应相关业务
 * Class SeoHotWork
 * @package admin\models
 */
class SeoHotWord
{
    /**
     * 在添加热词将热词添加到meilisearch中
     */
    public static function addHotWordToMeilisearch($id)
    {
        //todo
        (new HotWordService())->add($id);
    }

    /**
     * 在删除热词将热词从meilisearch中删除
     */
    public static function deleteHotWordFromMeilisearch($id)
    {
        //todo
        (new HotWordService())->delete($id);
    }

    /**
     * 在更新热词将热词更新到meilisearch中
     */
    public static function updateHotWordToMeilisearch($id)
    {
        //todo
        (new HotWordService())->update($id);
    }

    /**
     * 构建查询条件
     * @param $params
     * @return \yii\db\ActiveQuery
     */
    public static function getQueryBuilder($params)
    {
        //查询热词
        $query = BaseSeoHotWordConfig::find()
            ->andFilterWhere([
                'like',
                'keyword',
                $params['keyword'],
            ])
            ->andFilterCompare('code', $params['code'])
            ->andFilterWhere([
                '>=',
                'add_time',
                TimeHelper::dayToBeginTime($params['startAddTime']),
            ])
            ->andFilterWhere([
                '<=',
                'add_time',
                TimeHelper::dayToEndTime($params['endAddTime']),
            ]);

        return $query;
    }

    /**
     * 获取关键字列表
     */
    public static function getList($params)
    {
        $query = self::getQueryBuilder($params);
        //获取热词总数
        $count    = $query->count();
        $page     = $params['page'] ?: 1;
        $pageSize = $params['pageSize'] ?: \Yii::$app->params['defaultPageSize'];
        $pages    = BaseSeoHotWordConfig::setPage($count, $page, $pageSize);
        //获取热词列表
        $list = $query->orderBy('id desc')
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->asArray()
            ->all();

        return [
            'list'  => $list,
            'count' => (int)$count,
        ];
    }

    /**
     * 导出热词
     * @param $params
     * @return array
     */
    public static function export($params)
    {
        $query = self::getQueryBuilder($params);
        $list  = $query->orderBy('id asc')
            ->asArray()
            ->all();
        $data  = [];
        foreach ($list as $k => $v) {
            $item = [
                $v['id'],
                $v['keyword'],
                $v['code'],
                $v['add_time'],
            ];
            array_push($data, $item);
        }
        $headers  = [
            'ID',
            '关键词',
            'code',
            '添加时间',
        ];
        $fileName = '热词列表' . date('YmdHis');

        return [
            'headers'  => $headers,
            'data'     => $data,
            'fileName' => $fileName,
        ];
    }

    /**
     * 添加热词
     * @throws Exception
     */
    public static function add($keyword)
    {
        //判断关键词是否为空
        if (empty($keyword)) {
            throw new Exception('关键词不能为空');
        }
        //去除关键词中的空格
        $keyword = trim($keyword);
        //关键词有可能是多个，采用换行符分割
        $keywordArr = explode("\n", $keyword);
        //去掉空的内容
        $keywordArr = array_filter($keywordArr);
        //去除重复的关键词
        $keywordArr = array_unique($keywordArr);
        //再次判断关键词是否为空
        if (empty($keywordArr)) {
            throw new Exception('关键词不能为空');
        }
        //判断关键词是否超过1000个
        if (count($keywordArr) > 1000) {
            throw new Exception('关键词不能超过1000个');
        }
        //关键词已经存在数量
        $number = 0;
        $total  = count($keywordArr);
        //将关键词插入数据库
        $data    = [];
        $codeSet = [];
        //判断关键词每个长度不能超过50个字符
        foreach ($keywordArr as $k => &$v) {
            //正则替换关键词中的空格
            $v = str_replace(' ', '', $v);
            if (empty($v)) {
                unset($keywordArr[$k]);
                $total = $total - 1;
                continue;
            }
            if (mb_strlen($v) > 50) {
                throw new Exception('【' . $v . '】关键词长度不能超过50个字符');
            }
            //判断关键词是否已经存在
            if (BaseSeoHotWordConfig::find()
                ->where(['keyword' => $v])
                ->exists()) {
                //return $this->fail('【' . $v . '】关键词已经存在');
                //去掉已经存在的关键词
                unset($keywordArr[$k]);
                $number = $number + 1;
                continue;
            }
            //调用一下meiliSearch的搜索服务获取一下匹配的职位
            $search_service_model = new JobHotService();
            $jobIds               = $search_service_model->setKeyword($v)
                ->run();
            if (count($jobIds) <= 0) {
                $number = $number + 1;
                continue;
            }
            $code = StringHelper::randTextAndNumber();
            //校验code是否重复
            if (in_array($code, $codeSet) || BaseSeoHotWordConfig::find()
                    ->where(['code' => $code])
                    ->exists()) {
                $code = StringHelper::randTextAndNumber(5, 5);
            }
            array_push($codeSet, $code);
            $item = [
                'keyword'  => $v,
                //随机4个小写字母+6个数
                'code'     => $code,
                'add_time' => CUR_DATETIME,
            ];
            array_push($data, $item);
        }
        //去掉已经存在的关键词后$keywordArr为空直接返回
        if (empty($keywordArr)) {
            return [
                'res' => true,
                'msg' => '已成功导入0关键词，发现' . $number . '个关键词已存在，已为您自动过滤。',
            ];
        }
        //判断$number是否大于0，name这里需要对数组key重置成为索引数组
        if ($number > 0) {
            $keywordArr = array_values($keywordArr);
        }
        $res = Yii::$app->db->createCommand()
            ->batchInsert(BaseSeoHotWordConfig::tableName(), [
                'keyword',
                'code',
                'add_time',
            ], $data)
            ->execute();
        if ($res) {
            //获取批量写入的id
            $ids = BaseSeoHotWordConfig::find()
                ->select('id')
                ->where(['keyword' => $keywordArr])
                ->asArray()
                ->column();
            //添加到meilisearch
            self::addHotWordToMeilisearch($ids);

            if ($number > 0) {
                $msg = '已成功导入' . ($total - $number) . '个关键词，发现' . $number . '个关键词已存在，已为您自动过滤。';
            } else {
                $msg = '已成功导入' . $total . '关键词';
            }

            return [
                'res' => $res,
                'msg' => $msg,
            ];
        }
        throw new Exception('导入失败');
    }

    /**
     * 编辑热词
     * @throws Exception
     */
    public static function edit($params)
    {
        //获取输入的关键词参数
        $id      = $params['id'];
        $keyword = trim($params['keyword']);
        //判断关键词是否为空
        if (empty($id)) {
            throw new Exception('参数错误');
        }
        if (empty($keyword)) {
            throw new Exception('关键字不允许为空');
        }
        //去除关键词中的空格
        $keyword = trim($keyword);
        $keyword = str_replace(' ', '', $keyword);
        //判断关键词是否超过50个字符
        if (mb_strlen($keyword) > 50) {
            throw new Exception('关键词长度不能超过50个字符');
        }
        //判断关键词是否已经存在
        if (BaseSeoHotWordConfig::find()
            ->where(['keyword' => $keyword])
            ->andWhere([
                '!=',
                'id',
                $id,
            ])
            ->exists()) {
            throw new Exception('"' . $keyword . '"关键词已经存在');
        }
        //将关键词插入数据库
        $info = BaseSeoHotWordConfig::findOne($id);
        if (!$info) {
            throw new Exception('记录不存在');
        }
        if ($info->keyword == $keyword) {
            //直接返回
            return true;
        }
        $info->keyword = $keyword;
        if ($info->save()) {
            //更新到meilisearch
            self::updateHotWordToMeilisearch($id);
            //删除关键字缓存
            Cache::delete(Cache::SEO_HOT_SEARCH_KEYWORD_KEY_JOB_LIST . ':' . md5($info->code));
            Cache::delete(Cache::SEO_HOT_SEARCH_KEYWORD_KEY_RECOMMEND . ':' . md5($info->code));

            return true;
        } else {
            return false;
        }
    }

    /**
     * 删除热词
     * @param $id
     * @return bool
     * @throws Exception
     * @throws \Throwable
     * @throws \yii\db\StaleObjectException
     */
    public static function delete($id): bool
    {
        //判断关键词是否为空
        if (empty($id)) {
            throw new Exception('参数错误');
        }
        //将关键词删除
        $info = BaseSeoHotWordConfig::findOne($id);
        if (!$info) {
            throw new Exception('记录不存在');
        }
        if ($info->delete()) {
            //删除到meilisearch
            self::deleteHotWordFromMeilisearch($id);

            return true;
        } else {
            return false;
        }
    }
}