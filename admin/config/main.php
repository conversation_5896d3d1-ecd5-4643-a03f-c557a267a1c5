<?php
$params = array_merge(require __DIR__ . '/../../common/config/params.php',
    require __DIR__ . '/../../common/config/params-local.php', require __DIR__ . '/params.php',
    require __DIR__ . '/params-local.php');

return [
    'id'                  => 'app-admin',
    'basePath'            => dirname(__DIR__),
    'controllerNamespace' => 'admin\controllers',
    'bootstrap'           => ['log'],
    'defaultRoute'        => 'home',
    'components'          => [
        'request' => [
            'csrfParam' => '_csrf-admin',
        ],
        'session' => [
            // this is the name of the session cookie used for login on the admin
            'name'    => 'advanced-admin',
            'timeout' => 21600,
        ],

        'user'         => [
            'class'           => 'yii\web\User',
            'identityClass'   => 'admin\models\Admin',
            'enableAutoLogin' => true,
            'authTimeout'     => 21600,
            'identityCookie'  => [
                'name'     => '_identity-frontendPc',
                'httpOnly' => true,
            ],
            'idParam'         => '__member',
        ],
        'errorHandler' => [
            'errorAction' => 'site/error',
        ],
        'urlManager'   => [
            'enablePrettyUrl' => true,
            'showScriptName'  => false,
        ],
    ],
    'params'              => $params,
];
